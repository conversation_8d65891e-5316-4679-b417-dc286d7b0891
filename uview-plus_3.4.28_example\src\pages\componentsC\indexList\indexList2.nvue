<template>
	<up-button type="primary" size="small" style="width: 120px;"
		@click="show = true">打开弹窗</up-button>
	<!-- 因为up-popup默认带有安全底部需要取消。 -->
	<up-popup v-model:show="show" :safeAreaInsetBottom="false">
		<view v-if="show">
			<up-index-list style="height: 600px;"
				:indexList="indexList">
				<template #header>
					<view class="list">
						<view class="list__item">
							<up-avatar shape="square" size="35" icon="man-add-fill" fontSize="26" randomBgColor></up-avatar>
							<text class="list__item__user-name">新的朋友</text>
						</view>
						<up-line></up-line>
						<view class="list__item">
							<up-avatar shape="square" size="35" icon="tags-fill" fontSize="26" randomBgColor></up-avatar>
							<text class="list__item__user-name">标签</text>
						</view>
						<up-line></up-line>
						<view class="list__item">
							<up-avatar shape="square" size="35" icon="chrome-circle-fill" fontSize="26" randomBgColor></up-avatar>
							<text class="list__item__user-name">朋友圈</text>
						</view>
						<up-line></up-line>
						<view class="list__item">
							<up-avatar shape="square" size="35" icon="qq-fill" fontSize="26" randomBgColor></up-avatar>
							<text class="list__item__user-name">QQ</text>
						</view>
						<up-line></up-line>
					</view>
				</template>
				<template :key="index" v-for="(item, index) in itemArr">
					<!-- #ifdef APP-NVUE -->
					<up-index-anchor :text="indexList[index]"></up-index-anchor>
					<!-- #endif -->
					<up-index-item>
						<!-- #ifndef APP-NVUE -->
						<up-index-anchor :text="indexList[index]"></up-index-anchor>
						<!-- #endif -->
						<view class="list" v-for="(item1, index1) in item" :key="index1">
							<view class="list__item">
								<image class="list__item__avatar" :src="item1.url"></image>
								<text class="list__item__user-name">{{item1.name}}</text>
							</view>
							<up-line></up-line>
						</view>
					</up-index-item>
				</template>
				<template #footer>
					<view class="u-safe-area-inset--bottom">
						<text class="list__footer">共305位好友</text>
					</view>
				</template>
			</up-index-list>
		</view>
	</up-popup>
</template>

<script>
	const indexList = () => {
		const indexList = []
		const charCodeOfA = 'A'.charCodeAt(0)
		indexList.push("↑")
		indexList.push("☆")
		for (let i = 0; i < 10; i++) {
			indexList.push(String.fromCharCode(charCodeOfA + i))
		}
		indexList.push('#')
		return indexList
	}
	export default {
		data() {
			return {
				show: false,
				indexList: indexList(),
				urls: [
					'https://uiadmin.net/uview-plus/album/1.jpg',
					'https://uiadmin.net/uview-plus/album/2.jpg',
					'https://uiadmin.net/uview-plus/album/3.jpg',
					'https://uiadmin.net/uview-plus/album/4.jpg',
					'https://uiadmin.net/uview-plus/album/5.jpg',
					'https://uiadmin.net/uview-plus/album/6.jpg',
					'https://uiadmin.net/uview-plus/album/7.jpg',
					'https://uiadmin.net/uview-plus/album/8.jpg',
					'https://uiadmin.net/uview-plus/album/9.jpg',
					'https://uiadmin.net/uview-plus/album/10.jpg',
				],
				names: ["勇往无敌", "疯狂的迪飙", "磊爱可", "梦幻梦幻梦", "枫中飘瓢", "飞翔天使",
					"曾经第一", "追风幻影族长", "麦小姐", "胡格罗雅", "Red磊磊", "乐乐立立", "青龙爆风", "跑跑卡叮车", "山里狼", "supersonic超"
				]
			}
		},
		computed: {
			itemArr() {
				return this.indexList.map(item => {
					const arr = []
					for (let i = 0; i < 10; i++) {
						arr.push({
							name: this.names[uni.$u.random(0, this.names.length - 1)],
							url: this.urls[uni.$u.random(0, this.urls.length - 1)]
						})
					}
					return arr
				})
			}
		},
	}
</script>

<style lang="scss">
	.list {
		
		&__item {
			@include flex;
			padding: 6px 12px;
			align-items: center;
			
			&__avatar {
				height: 35px;
				width: 35px;
				border-radius: 3px;
			}
			
			&__user-name {
				font-size: 16px;
				margin-left: 10px;
				color: $u-main-color;
			}
		}
		
		&__footer {
			color: $u-tips-color;
			font-size: 14px;
			text-align: center;
			margin: 15px 0;
		}
	}
</style>
