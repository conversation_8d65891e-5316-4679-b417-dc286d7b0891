{"version": 3, "file": "new_article.js", "sources": ["pages/news/new_article.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbmV3cy9uZXdfYXJ0aWNsZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"common-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar title=\"NEWS详情\" :showBack=\"true\" :showHome=\"true\" @leftClick=\"goBack\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 使用公共文章详情组件 -->\n\t\t<article-detail\n\t\t\t:article=\"article\"\n\t\t\tarticleType=\"normal\"\n\t\t\t:showFooter=\"true\"\n\t\t\t@back=\"goBack\"\n\t\t></article-detail>\n\t</view>\n</template>\n\n<script>\n\timport { articleApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\timport ArticleDetail from '@/components/article/article-detail.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar,\n\t\t\tArticleDetail\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tarticleId: null,\n\t\t\t\tarticle: {\n\t\t\t\t\tid: null,\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tcontent_markdown: '',\n\t\t\t\t\tcreated_at: '',\n\t\t\t\t\tcategory: '',\n\t\t\t\t\tauthor: '',\n\t\t\t\t\tsource_document: '',\n\t\t\t\t\tcoverImageId: null\n\t\t\t\t},\n\t\t\t\tloading: true\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取传递过来的文章ID\n\t\t\tif (options && options.id) {\n\t\t\t\tthis.articleId = options.id;\n\t\t\t\tthis.fetchArticleDetail();\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文章ID不存在',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.goBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取文章详情\n\t\t\tasync fetchArticleDetail() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\tconst result = await articleApi.getArticleDetail(this.articleId);\n\t\t\t\t\tconsole.log('获取文章详情成功:', result);\n\n\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\tthis.article = result.data;\n\t\t\t\t\t\t// 如果没有内容，设置默认内容\n\t\t\t\t\t\tif (!this.article.content_markdown) {\n\t\t\t\t\t\t\tthis.article.content_markdown = '这是一篇关于\"' + this.article.title + '\"的文章内容。\\n\\n由于API暂未提供文章内容，这里展示的是默认内容。';\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '获取文章详情失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取文章详情失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取文章详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\t.common-container {\n\t\tpadding-bottom: 30rpx;\n\t}\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/news/new_article.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "articleApi.getArticleDetail"], "mappings": ";;;AAuBC,MAAK,eAAgB,MAAW;AAChC,MAAO,gBAAe,MAAW;AAEjC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,SAAS;AAAA,QACR,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,cAAc;AAAA,MACd;AAAA,MACD,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,WAAW,QAAQ,IAAI;AAC1B,WAAK,YAAY,QAAQ;AACzB,WAAK,mBAAkB;AAAA,WACjB;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChB,aAAK,OAAM;AAAA,MACX,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,qBAAqB;AAC1B,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMC,SAAAA,iBAA4B,KAAK,SAAS;AAC/DD,sBAAA,MAAA,MAAA,OAAA,oCAAY,aAAa,MAAM;AAE/B,YAAI,UAAU,OAAO,WAAW,OAAO,MAAM;AAC5C,eAAK,UAAU,OAAO;AAEtB,cAAI,CAAC,KAAK,QAAQ,kBAAkB;AACnC,iBAAK,QAAQ,mBAAmB,YAAY,KAAK,QAAQ,QAAQ;AAAA,UAClE;AAAA,eACM;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,oCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvGD,GAAG,WAAW,eAAe;"}