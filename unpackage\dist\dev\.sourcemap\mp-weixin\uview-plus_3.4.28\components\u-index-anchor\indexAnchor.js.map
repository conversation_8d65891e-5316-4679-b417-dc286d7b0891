{"version": 3, "file": "indexAnchor.js", "sources": ["uview-plus_3.4.28/components/u-index-anchor/indexAnchor.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:15\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexAnchor.js\n */\nexport default {\n    // indexAnchor 组件\n    indexAnchor: {\n        text: '',\n        color: '#606266',\n        size: 14,\n        bgColor: '#dedede',\n        height: 32\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,EACX;AACL;;"}