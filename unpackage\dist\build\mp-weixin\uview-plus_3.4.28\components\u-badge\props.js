"use strict";const e=require("../../libs/vue.js"),p=require("../../libs/config/props.js"),o=e.defineMixin({props:{isDot:{type:Boolean,default:()=>p.props.badge.isDot},value:{type:[Number,String],default:()=>p.props.badge.value},modelValue:{type:[Number,String],default:()=>p.props.badge.modelValue},show:{type:Boolean,default:()=>p.props.badge.show},max:{type:[Number,String],default:()=>p.props.badge.max},type:{type:String,default:()=>p.props.badge.type},showZero:{type:<PERSON>olean,default:()=>p.props.badge.showZero},bgColor:{type:[String,null],default:()=>p.props.badge.bgColor},color:{type:[String,null],default:()=>p.props.badge.color},shape:{type:String,default:()=>p.props.badge.shape},numberType:{type:String,default:()=>p.props.badge.numberType},offset:{type:Array,default:()=>p.props.badge.offset},inverted:{type:Boolean,default:()=>p.props.badge.inverted},absolute:{type:Boolean,default:()=>p.props.badge.absolute}}});exports.props=o;
