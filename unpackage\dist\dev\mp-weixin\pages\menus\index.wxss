/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
/* 使用全局样式文件中的.common-container */
.tabs-container {
  background-color: #FFF1F0;
  padding: 15rpx 20rpx;
  position: fixed;
  top: 85px;
  /* 导航栏高度 + 额外空间，减少间距 */
  left: 0;
  right: 0;
  z-index: 998;
}

/* 使用全局样式文件中的.navbar-right */
/* 组织架构相关样式 */
.party-content {
  margin-bottom: 30rpx;
  padding: 20rpx;
}
.party-content .content-section {
  background-color: #FFFFFF;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.party-content .content-section .section-title {
  display: flex;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 20rpx;
}
.party-content .content-section .section-title text {
  margin-left: 10rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}
.party-content .content-section .section-content {
  min-height: 200rpx;
}
.party-footer {
  padding: 20rpx;
  margin-top: 20rpx;
}
.content-container {
  flex: 1;
  padding: 15rpx;
  margin-top: 30px;
  /* 减少顶部间距，使布局更紧凑 */
}
.content-container .article-item {
  display: flex;
  justify-content: space-between;
  background-color: #FFFFFF;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border-radius: 4rpx;
}
.content-container .article-item .article-content {
  flex: 1;
  margin-right: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 120rpx;
  /* 与图片高度一致 */
}
.content-container .article-item .article-content .article-title {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  font-weight: 500;
  line-height: 1.4;
  overflow: hidden;
}
.content-container .article-item .article-content .article-summary {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
  line-height: 1.4;
  overflow: hidden;
}
.content-container .article-item .article-content .article-info {
  display: flex;
  font-size: 24rpx;
  color: #999999;
  align-items: center;
  margin-top: auto;
  /* 推到底部 */
}
.content-container .article-item .article-content .article-info .article-date {
  margin-right: 20rpx;
}
.content-container .article-item .article-image {
  width: 180rpx;
  height: 120rpx;
  border-radius: 4rpx;
  overflow: hidden;
  flex-shrink: 0;
  align-self: center;
  /* 垂直居中 */
}
.content-container .article-item .article-image image {
  width: 100%;
  height: 100%;
}
.content-container .empty-container {
  padding: 100rpx 0;
}