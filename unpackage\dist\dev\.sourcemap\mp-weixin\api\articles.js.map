{"version": 3, "file": "articles.js", "sources": ["api/articles.js"], "sourcesContent": ["import request from './request.js';\n\n/**\n * 获取文章列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getArticleList(params = {}) {\n  return request.get('/api/news/', params);\n}\n\n/**\n * 获取文章详情\n * @param {String|Number} id - 文章ID\n * @returns {Promise} - 返回Promise对象\n */\nexport function getArticleDetail(id) {\n  return request.get(`/api/news/${id}`);\n}\n"], "names": ["request"], "mappings": ";;AAOO,SAAS,eAAe,SAAS,IAAI;AAC1C,SAAOA,oBAAQ,IAAI,cAAc,MAAM;AACzC;AAOO,SAAS,iBAAiB,IAAI;AACnC,SAAOA,YAAAA,QAAQ,IAAI,aAAa,EAAE,EAAE;AACtC;;;"}