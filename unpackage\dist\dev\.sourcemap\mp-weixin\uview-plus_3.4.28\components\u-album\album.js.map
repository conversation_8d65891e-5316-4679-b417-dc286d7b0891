{"version": 3, "file": "album.js", "sources": ["uview-plus_3.4.28/components/u-album/album.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:47:24\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/album.js\n */\nexport default {\n    // album 组件\n    album: {\n        urls: [],\n        keyName: '',\n        singleSize: 180,\n        multipleSize: 70,\n        space: 6,\n        singleMode: 'scaleToFill',\n        multipleMode: 'aspectFill',\n        maxCount: 9,\n        previewFullImage: true,\n        rowCount: 3,\n        showMore: true,\n        autoWrap: false,\n        unit: 'px',\n        stop: true,\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM,CAAE;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,IACV,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,EACT;AACL;;"}