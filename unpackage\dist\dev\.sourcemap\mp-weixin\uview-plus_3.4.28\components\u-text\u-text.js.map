{"version": 3, "file": "u-text.js", "sources": ["uview-plus_3.4.28/components/u-text/u-text.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtdGV4dC91LXRleHQudnVl"], "sourcesContent": ["<template>\r\n    <view\r\n        class=\"u-text\"\r\n        :class=\"[customClass]\"\r\n        v-if=\"show\"\r\n        :style=\"wrapStyle\"\r\n        @tap=\"clickHandler\"\r\n    >\r\n        <text\r\n            :class=\"['u-text__price', type && `u-text__value--${type}`]\"\r\n            v-if=\"mode === 'price'\"\r\n            :style=\"[valueStyle]\"\r\n            >￥</text\r\n        >\r\n        <view class=\"u-text__prefix-icon\" v-if=\"prefixIcon\">\r\n            <u-icon\r\n                :name=\"prefixIcon\"\r\n                :customStyle=\"addStyle(iconStyle)\"\r\n            ></u-icon>\r\n        </view>\r\n        <u-link\r\n            v-if=\"mode === 'link'\" class=\"u-text__value\"\r\n            :style=\"{fontWeight: valueStyle.fontWeight, wordWrap: valueStyle.wordWrap, fontSize: valueStyle.fontSize}\"\r\n            :class=\"[type && `u-text__value--${type}`,lines && `u-line-${lines}`]\" :text=\"value\"\r\n            :href=\"href\"\r\n            underLine\r\n        ></u-link>\r\n        <template v-else-if=\"openType && isMp\">\r\n            <button\r\n                class=\"u-reset-button u-text__value\"\r\n                :style=\"[valueStyle]\"\r\n                :data-index=\"index\"\r\n                :openType=\"openType\"\r\n                @getuserinfo=\"onGetUserInfo\"\r\n                @contact=\"onContact\"\r\n                @getphonenumber=\"onGetPhoneNumber\"\r\n                @error=\"onError\"\r\n                @launchapp=\"onLaunchApp\"\r\n                @opensetting=\"onOpenSetting\"\r\n                :lang=\"lang\"\r\n                :session-from=\"sessionFrom\"\r\n                :send-message-title=\"sendMessageTitle\"\r\n                :send-message-path=\"sendMessagePath\"\r\n                :send-message-img=\"sendMessageImg\"\r\n                :show-message-card=\"showMessageCard\"\r\n                :app-parameter=\"appParameter\"\r\n            >\r\n                {{ value }}\r\n            </button>\r\n        </template>\r\n        <text\r\n            v-else\r\n            class=\"u-text__value\"\r\n            :style=\"[valueStyle]\"\r\n            :class=\"[\r\n                type && `u-text__value--${type}`,\r\n                lines && `u-line-${lines}`\r\n            ]\"\r\n            >{{ value }}</text\r\n        >\r\n        <view class=\"u-text__suffix-icon\" v-if=\"suffixIcon\">\r\n            <u-icon\r\n                :name=\"suffixIcon\"\r\n                :customStyle=\"addStyle(iconStyle)\"\r\n            ></u-icon>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport { props } from './props'\r\nimport value from './value.js'\r\nimport { mpMixin } from '../../libs/mixin/mpMixin';\r\nimport { mixin } from '../../libs/mixin/mixin';\r\nimport { buttonMixin } from '../../libs/mixin/button';\r\nimport { openType } from '../../libs/mixin/openType';\r\nimport { addStyle, addUnit, deepMerge } from '../../libs/function/index';\r\n/**\r\n * Text 文本\r\n * @description 此组件集成了文本类在项目中的常用功能，包括状态，拨打电话，格式化日期，*替换，超链接...等功能。 您大可不必在使用特殊文本时自己定义，text组件几乎涵盖您能使用的大部分场景。\r\n * @tutorial https://ijry.github.io/uview-plus/components/loading.html\r\n * @property {String} \t\t\t\t\ttype\t\t主题颜色\r\n * @property {Boolean} \t\t\t\t\tshow\t\t是否显示（默认 true ）\r\n * @property {String | Number}\t\t\ttext\t\t显示的值\r\n * @property {String}\t\t\t\t\tprefixIcon\t前置图标\r\n * @property {String} \t\t\t\t\tsuffixIcon\t后置图标\r\n * @property {String} \t\t\t\t\tmode\t\t文本处理的匹配模式 text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\r\n * @property {String} \t\t\t\t\thref\t\tmode=link下，配置的链接\r\n * @property {String | Function} \t\tformat\t\t格式化规则\r\n * @property {Boolean} \t\t\t\t\tcall\t\tmode=phone时，点击文本是否拨打电话（默认 false ）\r\n * @property {String} \t\t\t\t\topenType\t小程序的打开方式\r\n * @property {Boolean} \t\t\t\t\tbold\t\t是否粗体，默认normal（默认 false ）\r\n * @property {Boolean} \t\t\t\t\tblock\t\t是否块状（默认 false ）\r\n * @property {String | Number} \t\t\tlines\t\t文本显示的行数，如果设置，超出此行数，将会显示省略号\r\n * @property {String} \t\t\t\t\tcolor\t\t文本颜色（默认 '#303133' ）\r\n * @property {String | Number} \t\t\tsize\t\t字体大小（默认 15 ）\r\n * @property {Object | String} \t\t\ticonStyle\t图标的样式 （默认 {fontSize: '15px'} ）\r\n * @property {String} \t\t\t\t\tdecoration\t文字装饰，下划线，中划线等，可选值 none|underline|line-through（默认 'none' ）\r\n * @property {Object | String | Number}\tmargin\t\t外边距，对象、字符串，数值形式均可（默认 0 ）\r\n * @property {String | Number} \t\t\tlineHeight\t文本行高\r\n * @property {String} \t\t\t\t\talign\t\t文本对齐方式，可选值left|center|right（默认 'left' ）\r\n * @property {String} \t\t\t\t\twordWrap\t文字换行，可选值break-word|normal|anywhere（默认 'normal' ）\r\n * @event {Function} click  点击触发事件\r\n * @example <up-text text=\"我用十年青春,赴你最后之约\"></up-text>\r\n */\r\nexport default {\r\n    name: 'up-text',\r\n    // #ifdef MP\r\n    mixins: [mpMixin, mixin, value, buttonMixin, openType, props],\r\n    // #endif\r\n    // #ifndef MP\r\n    mixins: [mpMixin, mixin, value, props],\r\n    // #endif\r\n\temits: ['click'],\r\n    computed: {\r\n        wrapStyle() {\r\n            let style = {\r\n                margin: this.margin,\r\n\t\t\t    justifyContent: this.align === 'left' ? 'flex-start' : this.align === 'center' ? 'center' : 'flex-end'\r\n            }\r\n            // 占满剩余空间\r\n            if (this.flex1) {\r\n                style.flex = 1;\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tstyle.width = '100%';\n\t\t\t\t// #endif\r\n            }\n\t\t\treturn style;\r\n        },\r\n        valueStyle() {\r\n            const style = {\r\n                textDecoration: this.decoration,\r\n                fontWeight: this.bold ? 'bold' : 'normal',\r\n                wordWrap: this.wordWrap,\r\n                fontSize: addUnit(this.size)\r\n            }\r\n            !this.type && (style.color = this.color)\r\n            this.isNvue && this.lines && (style.lines = this.lines)\r\n            this.lineHeight &&\r\n                (style.lineHeight = addUnit(this.lineHeight))\r\n            !this.isNvue && this.block && (style.display = 'block')\r\n            return deepMerge(style, addStyle(this.customStyle))\r\n        },\r\n        isNvue() {\r\n            let nvue = false\r\n            // #ifdef APP-NVUE\r\n            nvue = true\r\n            // #endif\r\n            return nvue\r\n        },\r\n        isMp() {\r\n            let mp = false\r\n            // #ifdef MP\r\n            mp = true\r\n            // #endif\r\n            return mp\r\n        }\r\n    },\r\n    data() {\r\n        return {}\r\n    },\r\n    methods: {\r\n        addStyle,\r\n        clickHandler(e) {\r\n            // 如果为手机号模式，拨打电话\r\n            if (this.call && this.mode === 'phone') {\r\n                uni.makePhoneCall({\r\n                    phoneNumber: this.text\r\n                })\r\n            }\r\n            this.$emit('click', e)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/components.scss';\r\n\r\n.u-text {\r\n    @include flex(row);\r\n    align-items: center;\r\n    flex-wrap: nowrap;\r\n\r\n    &__price {\r\n        font-size: 14px;\r\n        color: $u-content-color;\r\n    }\r\n\r\n    &__value {\r\n        font-size: 14px;\r\n        @include flex;\r\n        color: $u-content-color;\r\n        flex-wrap: wrap;\r\n        // flex: 1;\r\n        text-overflow: ellipsis;\r\n        align-items: center;\r\n\r\n        &--primary {\r\n            color: $u-primary;\r\n        }\r\n\r\n        &--warning {\r\n            color: $u-warning;\r\n        }\r\n\r\n        &--success {\r\n            color: $u-success;\r\n        }\r\n\r\n        &--info {\r\n            color: $u-info;\r\n        }\r\n\r\n        &--error {\r\n            color: $u-error;\r\n        }\r\n\r\n        &--main {\r\n            color: $u-main-color;\r\n        }\r\n\r\n        &--content {\r\n            color: $u-content-color;\r\n        }\r\n\r\n        &--tips {\r\n            color: $u-tips-color;\r\n        }\r\n\r\n        &--light {\r\n            color: $u-light-color;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-text/u-text.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "value", "buttonMixin", "openType", "props", "addUnit", "deepMerge", "addStyle", "uni"], "mappings": ";;;;;;;;;AAyGA,MAAK,YAAU;AAAA,EACX,MAAM;AAAA,EAEN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,wCAAK,OAAEC,mCAAW,aAAEC,qCAAQ,UAAEC,6CAAK;AAAA,EAK/D,OAAO,CAAC,OAAO;AAAA,EACZ,UAAU;AAAA,IACN,YAAY;AACR,UAAI,QAAQ;AAAA,QACR,QAAQ,KAAK;AAAA,QACtB,gBAAgB,KAAK,UAAU,SAAS,eAAe,KAAK,UAAU,WAAW,WAAW;AAAA,MACvF;AAEA,UAAI,KAAK,OAAO;AACZ,cAAM,OAAO;AAEzB,cAAM,QAAQ;AAAA,MAEN;AACT,aAAO;AAAA,IACD;AAAA,IACD,aAAa;AACT,YAAM,QAAQ;AAAA,QACV,gBAAgB,KAAK;AAAA,QACrB,YAAY,KAAK,OAAO,SAAS;AAAA,QACjC,UAAU,KAAK;AAAA,QACf,UAAUC,qCAAAA,QAAQ,KAAK,IAAI;AAAA,MAC/B;AACA,OAAC,KAAK,SAAS,MAAM,QAAQ,KAAK;AAClC,WAAK,UAAU,KAAK,UAAU,MAAM,QAAQ,KAAK;AACjD,WAAK,eACA,MAAM,aAAaA,qCAAAA,QAAQ,KAAK,UAAU;AAC/C,OAAC,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU;AAC/C,aAAOC,qCAAS,UAAC,OAAOC,qCAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACrD;AAAA,IACD,SAAS;AACL,UAAI,OAAO;AAIX,aAAO;AAAA,IACV;AAAA,IACD,OAAO;AACH,UAAI,KAAK;AAET,WAAK;AAEL,aAAO;AAAA,IACX;AAAA,EACH;AAAA,EACD,OAAO;AACH,WAAO,CAAC;AAAA,EACX;AAAA,EACD,SAAS;AAAA,IACL,UAAAA,qCAAQ;AAAA,IACR,aAAa,GAAG;AAEZ,UAAI,KAAK,QAAQ,KAAK,SAAS,SAAS;AACpCC,sBAAAA,MAAI,cAAc;AAAA,UACd,aAAa,KAAK;AAAA,SACrB;AAAA,MACL;AACA,WAAK,MAAM,SAAS,CAAC;AAAA,IACzB;AAAA,EACJ;AACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5KA,GAAG,gBAAgB,SAAS;"}