"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "custom-navbar",
  props: {
    // 标题
    title: {
      type: String,
      default: ""
    },
    // 是否显示返回图标
    showBack: {
      type: Boolean,
      default: true
    },
    // 是否显示返回首页图标
    showHome: {
      type: Boolean,
      default: false
    },
    // 右侧图标
    rightIcon: {
      type: String,
      default: ""
    },
    // 右侧文字
    rightText: {
      type: String,
      default: ""
    },
    // 是否自动返回上一页
    autoBack: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      statusBarHeight: 20
      // 状态栏高度，默认值
    };
  },
  created() {
    this.getStatusBarHeight();
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      try {
        const windowInfo = common_vendor.index.getWindowInfo();
        this.statusBarHeight = windowInfo.statusBarHeight || 20;
      } catch (e) {
        common_vendor.index.__f__("error", "at components/common/custom-navbar.vue:97", "获取状态栏高度失败:", e);
        this.statusBarHeight = 20;
      }
    },
    // 返回按钮点击事件
    onBackClick() {
      this.$emit("leftClick");
      const pages = getCurrentPages();
      if (pages.length <= 1) {
        common_vendor.index.switchTab({
          url: "/pages/index/index"
        });
        return;
      }
      common_vendor.index.navigateBack({
        delta: 1,
        fail: () => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }
      });
    },
    // 首页按钮点击事件
    onHomeClick() {
      this.$emit("homeClick");
      common_vendor.index.navigateTo({
        url: "/pages/index/index",
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/common/custom-navbar.vue:138", "跳转到首页失败:", err);
          common_vendor.index.reLaunch({
            url: "/pages/index/index",
            fail: (err2) => {
              common_vendor.index.__f__("error", "at components/common/custom-navbar.vue:143", "reLaunch跳转到首页也失败:", err2);
              common_vendor.index.showToast({
                title: "跳转到首页失败",
                icon: "none"
              });
            }
          });
        }
      });
    },
    // 右侧点击事件
    onRightClick() {
      this.$emit("rightClick");
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_line2 = common_vendor.resolveComponent("u-line");
  (_easycom_u_icon2 + _easycom_u_line2)();
}
const _easycom_u_icon = () => "../u-icon/u-icon.js";
const _easycom_u_line = () => "../../uview-plus_3.4.28/components/u-line/u-line.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_line)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.statusBarHeight + "px",
    b: $props.showBack
  }, $props.showBack ? {
    c: common_vendor.p({
      name: "arrow-left",
      color: "#FFFFFF",
      size: "20"
    }),
    d: common_vendor.o((...args) => $options.onBackClick && $options.onBackClick(...args))
  } : {}, {
    e: $props.showBack && $props.showHome
  }, $props.showBack && $props.showHome ? {
    f: common_vendor.p({
      direction: "column",
      hairline: false,
      length: "16",
      color: "#FFFFFF",
      width: "1"
    })
  } : {}, {
    g: $props.showHome
  }, $props.showHome ? {
    h: common_vendor.p({
      name: "home",
      color: "#FFFFFF",
      size: "20"
    }),
    i: common_vendor.o((...args) => $options.onHomeClick && $options.onHomeClick(...args))
  } : {}, {
    j: common_vendor.t($props.title),
    k: $props.rightIcon
  }, $props.rightIcon ? {
    l: common_vendor.p({
      name: $props.rightIcon,
      color: "#FFFFFF",
      size: "20"
    })
  } : {}, {
    m: $props.rightText
  }, $props.rightText ? {
    n: common_vendor.t($props.rightText)
  } : {}, {
    o: common_vendor.o((...args) => $options.onRightClick && $options.onRightClick(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/common/custom-navbar.js.map
