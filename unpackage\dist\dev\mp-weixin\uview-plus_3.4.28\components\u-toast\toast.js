"use strict";
const Toast = {
  // toast组件
  toast: {
    zIndex: 10090,
    loading: false,
    message: "",
    icon: "",
    type: "",
    loadingMode: "",
    show: "",
    overlay: false,
    position: "center",
    params: {},
    duration: 2e3,
    isTab: false,
    url: "",
    callback: null,
    back: false
  }
};
exports.Toast = Toast;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-toast/toast.js.map
