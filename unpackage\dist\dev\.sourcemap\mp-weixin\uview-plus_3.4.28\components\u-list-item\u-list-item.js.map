{"version": 3, "file": "u-list-item.js", "sources": ["uview-plus_3.4.28/components/u-list-item/u-list-item.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtbGlzdC1pdGVtL3UtbGlzdC1pdGVtLnZ1ZQ"], "sourcesContent": ["<template>\n\t<!-- #ifdef APP-NVUE -->\n\t<cell>\n\t\t<!-- #endif -->\n\t\t<view\n\t\t\tclass=\"u-list-item\"\n\t\t\t:ref=\"`u-list-item-${anchor}`\"\n\t\t\t:anchor=\"`u-list-item-${anchor}`\"\n\t\t\t:class=\"[`u-list-item-${anchor}`]\"\n\t\t>\n\t\t\t<slot />\n\t\t</view>\n\t\t<!-- #ifdef APP-NVUE -->\n\t</cell>\n\t<!-- #endif -->\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { getWindowInfo } from '../../libs/function/index';\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * List 列表\n\t * @description 该组件为高性能列表组件\n\t * @tutorial https://ijry.github.io/uview-plus/components/list.html\n\t * @property {String | Number}\tanchor\t用于滚动到指定item\n\t * @example <u-list-ite v-for=\"(item, index) in indexList\" :key=\"index\" ></u-list-item>\n\t */\n\texport default {\n\t\tname: 'u-list-item',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 节点信息\n\t\t\t\trect: {},\n\t\t\t\tindex: 0,\n\t\t\t\tshow: true,\n\t\t\t\tsys: getWindowInfo()\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\tinject: ['uList'],\n\t\twatch: {\n\t\t\t// #ifndef APP-NVUE\n\t\t\t'uList.innerScrollTop'(n) {\n\t\t\t\tconst preLoadScreen = this.uList.preLoadScreen\n\t\t\t\tconst windowHeight = this.sys.windowHeight\n\t\t\t\tif(n <= windowHeight * preLoadScreen) {\n\t\t\t\t\tthis.parent.updateOffsetFromChild(0)\n\t\t\t\t} else if (this.rect.top <= n - windowHeight * preLoadScreen) {\n\t\t\t\t\tthis.parent.updateOffsetFromChild(this.rect.top)\n\t\t\t\t}\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t\tcreated() {\n\t\t\tthis.parent = {}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 初始化数据\n\t\t\t\tthis.updateParentData()\n\t\t\t\tthis.index = this.parent.children.indexOf(this)\n\t\t\t\tthis.resize()\n\t\t\t},\n\t\t\tupdateParentData() {\n\t\t\t\t// 此方法在mixin中\n\t\t\t\tthis.getParentData('u-list')\n\t\t\t},\n\t\t\tresize() {\n\t\t\t\tthis.queryRect(`u-list-item-${this.anchor}`).then(size => {\n\t\t\t\t\tconst lastChild = this.parent.children[this.index - 1]\n\t\t\t\t\tthis.rect = size\n\t\t\t\t\tconst preLoadScreen = this.uList.preLoadScreen\n\t\t\t\t\tconst windowHeight = this.sys.windowHeight\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tif (lastChild) {\n\t\t\t\t\t\tthis.rect.top = lastChild.rect.top + lastChild.rect.height\n\t\t\t\t\t}\n\t\t\t\t\tif (size.top >= this.uList.innerScrollTop + (1 + preLoadScreen) * windowHeight) this.show =\n\t\t\t\t\t\tfalse\n\t\t\t\t\t// #endif\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 查询元素尺寸\n\t\t\tqueryRect(el) {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tthis.$uGetRect(`.${el}`).then(size => {\n\t\t\t\t\t\tresolve(size)\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tconst ref = this.$refs[el]\n\t\t\t\t\tdom.getComponentRect(ref, res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-list-item {}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-list-item/u-list-item.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "getWindowInfo"], "mappings": ";;;;;;AAgCC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,iDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,MAAM,CAAE;AAAA,MACR,OAAO;AAAA,MACP,MAAM;AAAA,MACN,KAAKC,qCAAAA,cAAc;AAAA,IACpB;AAAA,EACA;AAAA,EACD,UAAU,CAET;AAAA,EACD,QAAQ,CAAC,OAAO;AAAA,EAChB,OAAO;AAAA,IAEN,uBAAuB,GAAG;AACzB,YAAM,gBAAgB,KAAK,MAAM;AACjC,YAAM,eAAe,KAAK,IAAI;AAC9B,UAAG,KAAK,eAAe,eAAe;AACrC,aAAK,OAAO,sBAAsB,CAAC;AAAA,iBACzB,KAAK,KAAK,OAAO,IAAI,eAAe,eAAe;AAC7D,aAAK,OAAO,sBAAsB,KAAK,KAAK,GAAG;AAAA,MAChD;AAAA,IACD;AAAA,EAEA;AAAA,EACD,UAAU;AACT,SAAK,SAAS,CAAC;AAAA,EACf;AAAA,EACD,UAAU;AACT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,SAAS;AAAA,IACR,OAAO;AAEN,WAAK,iBAAiB;AACtB,WAAK,QAAQ,KAAK,OAAO,SAAS,QAAQ,IAAI;AAC9C,WAAK,OAAO;AAAA,IACZ;AAAA,IACD,mBAAmB;AAElB,WAAK,cAAc,QAAQ;AAAA,IAC3B;AAAA,IACD,SAAS;AACR,WAAK,UAAU,eAAe,KAAK,MAAM,EAAE,EAAE,KAAK,UAAQ;AACzD,cAAM,YAAY,KAAK,OAAO,SAAS,KAAK,QAAQ,CAAC;AACrD,aAAK,OAAO;AACZ,cAAM,gBAAgB,KAAK,MAAM;AACjC,cAAM,eAAe,KAAK,IAAI;AAE9B,YAAI,WAAW;AACd,eAAK,KAAK,MAAM,UAAU,KAAK,MAAM,UAAU,KAAK;AAAA,QACrD;AACA,YAAI,KAAK,OAAO,KAAK,MAAM,kBAAkB,IAAI,iBAAiB;AAAc,eAAK,OACpF;AAAA,OAED;AAAA,IACD;AAAA;AAAA,IAED,UAAU,IAAI;AACb,aAAO,IAAI,QAAQ,aAAW;AAE7B,aAAK,UAAU,IAAI,EAAE,EAAE,EAAE,KAAK,UAAQ;AACrC,kBAAQ,IAAI;AAAA,SACZ;AAAA,OASD;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;AC9GD,GAAG,gBAAgB,SAAS;"}