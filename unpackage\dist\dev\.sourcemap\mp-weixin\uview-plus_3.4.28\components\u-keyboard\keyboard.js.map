{"version": 3, "file": "keyboard.js", "sources": ["uview-plus_3.4.28/components/u-keyboard/keyboard.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:07:49\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/keyboard.js\n */\nexport default {\n    // 键盘组件\n    keyboard: {\n        mode: 'number',\n        dotDisabled: false,\n        tooltip: true,\n        showTips: true,\n        tips: '',\n        showCancel: true,\n        showConfirm: true,\n        random: false,\n        safeAreaInsetBottom: true,\n        closeOnClickOverlay: true,\n        show: false,\n        overlay: true,\n        zIndex: 10075,\n        cancelText: '取消',\n        confirmText: '确定',\n        autoChange: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,qBAAqB;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,YAAY;AAAA,EACf;AACL;;"}