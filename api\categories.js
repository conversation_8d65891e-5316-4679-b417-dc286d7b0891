import request from './request.js';

/**
 * 获取分类列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryList(params = {}) {
  return request.get('/api/categories', params);
}

/**
 * 获取分类树形列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryTreeList(params = {}) {
  return request.get('/api/categories/tree', params);
}

/**
 * 获取分类详情
 * @param {String|Number} id - 分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryDetail(id) {
  return request.get(`/api/categories/${id}`);
}

/**
 * 获取分类下的文章列表
 * @param {String|Number} categoryId - 分类ID
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryArticles(categoryId, params = {}) {
  return request.get(`/api/categories/${categoryId}/articles`, params);
}
