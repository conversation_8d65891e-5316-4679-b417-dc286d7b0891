{"version": 3, "file": "openType.js", "sources": ["uview-plus_3.4.28/libs/mixin/openType.js"], "sourcesContent": ["import { defineMixin } from '../vue'\r\n\r\nexport const openType = defineMixin({\r\n    props: {\r\n        openType: String\r\n    },\r\n    methods: {\r\n        onGetUserInfo(event) {\r\n            this.$emit('getuserinfo', event.detail)\r\n        },\r\n        onContact(event) {\r\n            this.$emit('contact', event.detail)\r\n        },\r\n        onGetPhoneNumber(event) {\r\n            this.$emit('getphonenumber', event.detail)\r\n        },\r\n        onError(event) {\r\n            this.$emit('error', event.detail)\r\n        },\r\n        onLaunchApp(event) {\r\n            this.$emit('launchapp', event.detail)\r\n        },\r\n        onOpenSetting(event) {\r\n            this.$emit('opensetting', event.detail)\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin"], "mappings": ";;AAEY,MAAC,WAAWA,0BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA,IACH,UAAU;AAAA,EACb;AAAA,EACD,SAAS;AAAA,IACL,cAAc,OAAO;AACjB,WAAK,MAAM,eAAe,MAAM,MAAM;AAAA,IACzC;AAAA,IACD,UAAU,OAAO;AACb,WAAK,MAAM,WAAW,MAAM,MAAM;AAAA,IACrC;AAAA,IACD,iBAAiB,OAAO;AACpB,WAAK,MAAM,kBAAkB,MAAM,MAAM;AAAA,IAC5C;AAAA,IACD,QAAQ,OAAO;AACX,WAAK,MAAM,SAAS,MAAM,MAAM;AAAA,IACnC;AAAA,IACD,YAAY,OAAO;AACf,WAAK,MAAM,aAAa,MAAM,MAAM;AAAA,IACvC;AAAA,IACD,cAAc,OAAO;AACjB,WAAK,MAAM,eAAe,MAAM,MAAM;AAAA,IACzC;AAAA,EACJ;AACL,CAAC;;"}