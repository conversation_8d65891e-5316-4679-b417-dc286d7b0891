{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-grid-item/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 宫格的name\n        name: {\n            type: [String, Number, null],\n            default: () => defProps.gridItem.name\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: () => defProps.gridItem.bgColor\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,QAAQ,IAAI;AAAA,MAC3B,SAAS,MAAMC,yCAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}