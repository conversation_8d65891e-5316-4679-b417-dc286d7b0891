<template>
	<view
	    class="u-page"
	    ref="u-back-top"
	>
	<view class="u-demo-block">
		<text class="u-demo-block__title">自定义backTop(滚动页面即可在右下角看到图标)</text>
		<view class="u-demo-block__content">
			<view class="u-page__backTop-item">
				<up-checkbox-group
				    placement="column"
					shape="square"
				    @change="checkboxChange"
					v-model="value"
				>
					<up-checkbox
					    :customStyle="{marginBottom: '8px'}"
					    v-for="(item, index) in checkboxList"
					    :key="index"
					    :label="item.name"
					    :name="item.name"
					>
					</up-checkbox>
				</up-checkbox-group>
			</view>
		</view>
	</view>
		<up-back-top
		    :right="backTopData.right"
		    :customStyle="backTopData.customStyle"
		    :bottom="backTopData.bottom"
		    :icon="backTopData.icon"
		    :mode="backTopData.mode"
		    :iconStyle="backTopData.iconStyle"
			:duration="backTopData.duration"
		    :scrollTop="scrollTop"
			@click="click"
		></up-back-top>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value: ['自定义图标'],
				backTopData: {
					mode: 'circle',
					icon: 'arrow-upward',
					bottom: 100,
					customStyle: {},
					iconStyle: {},
					right:20,
					duration: 300
				},
				scrollTop: 0,
				// 被自定义的样式
				checkboxList: [{
						name: '显示方形',
					},
					{
						name: '自定义图标',
					},
					{
						name: '自定义距离',
					},
					{
						name: '自定义样式',
					},
					{
						name:'自定义返回顶部滚动时间',
					}
				]
			}
		},
		onLoad() {
			// 演示中默认勾选了自定义图标
			this.backTopData.icon = "arrow-up"
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		methods: {
			checkboxChange(n) {
				if (n.includes('显示方形')) {
					this.backTopData.mode = 'square'
				} else {
					this.backTopData.mode = "circle"
				}
				if (n.includes('自定义图标')) {
					this.backTopData.icon = "arrow-up"
				} else {
					this.backTopData.icon = "arrow-upward"
				}
				if (n.includes('自定义距离')) {
					this.backTopData.bottom = 300
					this.backTopData.right=20
				} else {
					this.backTopData.bottom = 100
				}
				if (n.includes('自定义样式')) {
					this.backTopData.customStyle = {
						backgroundColor: '#2979ff',
					}
					this.backTopData.iconStyle = {
						color: '#ffffff'
					}
				} else {
					this.backTopData.customStyle = {}
					this.backTopData.iconStyle = {}
				}
				if (n.includes('自定义返回顶部滚动时间')) {
					this.backTopData.duration =1500 
				} else {
					this.backTopData.duration =300
				}
			},
			click() {
				console.log('click');
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		height: 1200px;
		&__backTop-item{
			margin-top:10px;
		}
	}
</style>
