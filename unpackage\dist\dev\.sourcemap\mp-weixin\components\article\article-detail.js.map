{"version": 3, "file": "article-detail.js", "sources": ["components/article/article-detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby9jb21wb25lbnRzL2FydGljbGUvYXJ0aWNsZS1kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"article-detail\">\n\t\t<!-- 文章标题 -->\n\t\t<view class=\"article-header\">\n\t\t\t<view class=\"article-title\">\n\t\t\t\t<text>{{ article.title || '加载中...' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"article-meta\">\n\t\t\t\t<view v-if=\"article.created_at || article.createdAt\">\n\t\t\t\t\t<text>发布时间：{{ formatDate(article.created_at || article.createdAt) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"article.category\">\n\t\t\t\t\t<text>分类：{{ article.category }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"article.author\">\n\t\t\t\t\t<text>作者：{{ article.author }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 文章封面图 -->\n\t\t<view class=\"article-cover\" v-if=\"coverImage\">\n\t\t\t<image\n\t\t\t\t:show-menu-by-longpress=\"true\"\n\t\t\t\t:src=\"getFullImageUrl(coverImage.url)\"\n\t\t\t\t:alt=\"coverImage.altText || article.title\"\n\t\t\t\tmode=\"widthFix\"\n\t\t\t\t@click=\"previewCoverImage\"\n\t\t\t></image>\n\t\t</view>\n\n\t\t<!-- 文章内容 -->\n\t\t<view class=\"article-content\">\n\t\t\t<rich-text :nodes=\"articleContent || '加载中...'\"></rich-text>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"article-footer\">\n\t\t\t<u-button type=\"primary\" text=\"返回列表\" @click=\"goBack\" :customStyle=\"{backgroundColor: '#D9001B'}\"></u-button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { getFullImageUrl as getFullImageUrlUtil, fetchImageById } from '@/utils/image.js';\n\n\texport default {\n\t\tname: 'ArticleDetail',\n\t\tprops: {\n\t\t\t// 文章对象\n\t\t\tarticle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: () => ({\n\t\t\t\t\tid: null,\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tcontent: '',\n\t\t\t\t\tcontent_markdown: '',\n\t\t\t\t\tcreated_at: '',\n\t\t\t\t\tcreatedAt: '',\n\t\t\t\t\tcategory: '',\n\t\t\t\t\tauthor: '',\n\t\t\t\t\tcoverImageId: null\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 文章类型：'normal'(普通文章) 或 'menu'(菜单文章)\n\t\t\tarticleType: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'normal',\n\t\t\t\tvalidator: (value) => ['normal', 'menu'].includes(value)\n\t\t\t},\n\t\t\t// 是否显示底部按钮\n\t\t\tshowFooter: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcoverImage: null,\n\t\t\t\tloading: true\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 处理后的文章内容\n\t\t\tarticleContent() {\n\t\t\t\tif (this.articleType === 'normal') {\n\t\t\t\t\t// 普通文章，需要将Markdown转为HTML\n\t\t\t\t\treturn this.formatContent(this.article.content_markdown);\n\t\t\t\t} else {\n\t\t\t\t\t// 菜单文章，内容已经是HTML格式\n\t\t\t\t\treturn this.article.content;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 监听文章变化，获取封面图\n\t\t\tarticle: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tif (newVal && newVal.coverImageId) {\n\t\t\t\t\t\tthis.fetchCoverImage(newVal.coverImageId);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\timmediate: true,\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 格式化日期\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '';\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n\t\t\t},\n\n\t\t\t// 格式化Markdown内容为HTML\n\t\t\tformatContent(markdown) {\n\t\t\t\tif (!markdown) return '';\n\n\t\t\t\t// 简单的Markdown转HTML处理\n\t\t\t\tlet html = markdown\n\t\t\t\t\t// 段落\n\t\t\t\t\t.replace(/\\n\\n/g, '</p><p class=\"article-paragraph\">')\n\t\t\t\t\t// 单行换行\n\t\t\t\t\t.replace(/\\n/g, '<br>')\n\t\t\t\t\t// 标题\n\t\t\t\t\t.replace(/#{6}\\s(.*?)\\s*$/gm, '<h6>$1</h6>')\n\t\t\t\t\t.replace(/#{5}\\s(.*?)\\s*$/gm, '<h5>$1</h5>')\n\t\t\t\t\t.replace(/#{4}\\s(.*?)\\s*$/gm, '<h4>$1</h4>')\n\t\t\t\t\t.replace(/#{3}\\s(.*?)\\s*$/gm, '<h3>$1</h3>')\n\t\t\t\t\t.replace(/#{2}\\s(.*?)\\s*$/gm, '<h2>$1</h2>')\n\t\t\t\t\t.replace(/#{1}\\s(.*?)\\s*$/gm, '<h1>$1</h1>')\n\t\t\t\t\t// 加粗\n\t\t\t\t\t.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n\t\t\t\t\t// 斜体\n\t\t\t\t\t.replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n\t\t\t\t\t// 引用\n\t\t\t\t\t.replace(/>\\s(.*?)\\s*$/gm, '<blockquote>$1</blockquote>');\n\n\t\t\t\t// 确保内容被包裹在段落标签中\n\t\t\t\tif (!html.startsWith('<')) {\n\t\t\t\t\thtml = '<p class=\"article-paragraph\">' + html;\n\t\t\t\t}\n\t\t\t\tif (!html.endsWith('>')) {\n\t\t\t\t\thtml = html + '</p>';\n\t\t\t\t}\n\n\t\t\t\treturn html;\n\t\t\t},\n\n\t\t\t// 获取封面图片\n\t\t\tasync fetchCoverImage(imageId) {\n\t\t\t\ttry {\n\t\t\t\t\tconst imageData = await fetchImageById(imageId);\n\t\t\t\t\tconsole.log('获取封面图片成功:', imageData);\n\n\t\t\t\t\tif (imageData) {\n\t\t\t\t\t\tthis.coverImage = imageData;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取封面图片失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取完整的图片URL\n\t\t\tgetFullImageUrl(url) {\n\t\t\t\treturn getFullImageUrlUtil(url);\n\t\t\t},\n\n\t\t\t// 预览封面图片\n\t\t\tpreviewCoverImage() {\n\t\t\t\tif (this.coverImage && this.coverImage.url) {\n\t\t\t\t\tconst imageUrl = this.getFullImageUrl(this.coverImage.url);\n\t\t\t\t\tconsole.log('预览封面图片:', imageUrl);\n\n\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\turls: [imageUrl],\n\t\t\t\t\t\tcurrent: imageUrl,\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tconsole.log('图片预览成功');\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('图片预览失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '图片预览失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\t// 直接在组件内部处理返回操作，不再触发事件\n\t\t\t\tuni.navigateBack();\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.article-detail {\n\t\tpadding: 30rpx;\n\n\t\t.article-header {\n\t\t\tmargin-bottom: 30rpx;\n\n\t\t\t.article-title {\n\t\t\t\tfont-size: 36rpx;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #333333;\n\t\t\t\tline-height: 1.4;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\n\t\t\t.article-meta {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999999;\n\t\t\t\tline-height: 1.6;\n\n\t\t\t\tview {\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\tdisplay: inline-block;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.article-cover {\n\t\t\tmargin: 20rpx 0 30rpx;\n\t\t\twidth: 100%;\n\n\t\t\timage {\n\t\t\t\twidth: 100%;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t}\n\t\t}\n\n\t\t.article-content {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #333333;\n\t\t\tline-height: 1.6;\n\t\t\tmargin-bottom: 40rpx;\n\n\n\t\t}\n\n\t\t.article-footer {\n\t\t\tmargin-top: 40rpx;\n\t\t\tpadding: 20rpx 0;\n\t\t}\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/components/article/article-detail.vue'\nwx.createComponent(Component)"], "names": ["fetchImageById", "uni", "getFullImageUrlUtil"], "mappings": ";;;AA8CC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEN,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS,OAAO;AAAA,QACf,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,cAAc;AAAA;IAEf;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA,MACT,WAAW,CAAC,UAAU,CAAC,UAAU,MAAM,EAAE,SAAS,KAAK;AAAA,IACvD;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,iBAAiB;AAChB,UAAI,KAAK,gBAAgB,UAAU;AAElC,eAAO,KAAK,cAAc,KAAK,QAAQ,gBAAgB;AAAA,aACjD;AAEN,eAAO,KAAK,QAAQ;AAAA,MACrB;AAAA,IACD;AAAA,EACA;AAAA,EACD,OAAO;AAAA;AAAA,IAEN,SAAS;AAAA,MACR,QAAQ,QAAQ;AACf,YAAI,UAAU,OAAO,cAAc;AAClC,eAAK,gBAAgB,OAAO,YAAY;AAAA,QACzC;AAAA,MACA;AAAA,MACD,WAAW;AAAA,MACX,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACvH;AAAA;AAAA,IAGD,cAAc,UAAU;AACvB,UAAI,CAAC;AAAU,eAAO;AAGtB,UAAI,OAAO,SAET,QAAQ,SAAS,mCAAmC,EAEpD,QAAQ,OAAO,MAAM,EAErB,QAAQ,qBAAqB,aAAa,EAC1C,QAAQ,qBAAqB,aAAa,EAC1C,QAAQ,qBAAqB,aAAa,EAC1C,QAAQ,qBAAqB,aAAa,EAC1C,QAAQ,qBAAqB,aAAa,EAC1C,QAAQ,qBAAqB,aAAa,EAE1C,QAAQ,kBAAkB,qBAAqB,EAE/C,QAAQ,cAAc,aAAa,EAEnC,QAAQ,kBAAkB,6BAA6B;AAGzD,UAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AAC1B,eAAO,kCAAkC;AAAA,MAC1C;AACA,UAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,eAAO,OAAO;AAAA,MACf;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,gBAAgB,SAAS;AAC9B,UAAI;AACH,cAAM,YAAY,MAAMA,2BAAe,OAAO;AAC9CC,sBAAY,MAAA,MAAA,OAAA,gDAAA,aAAa,SAAS;AAElC,YAAI,WAAW;AACd,eAAK,aAAa;AAAA,QACnB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gDAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,KAAK;AACpB,aAAOC,YAAAA,gBAAoB,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,oBAAoB;AACnB,UAAI,KAAK,cAAc,KAAK,WAAW,KAAK;AAC3C,cAAM,WAAW,KAAK,gBAAgB,KAAK,WAAW,GAAG;AACzDD,sBAAA,MAAA,MAAA,OAAA,gDAAY,WAAW,QAAQ;AAE/BA,sBAAAA,MAAI,aAAa;AAAA,UAChB,MAAM,CAAC,QAAQ;AAAA,UACf,SAAS;AAAA,UACT,SAAS,MAAM;AACdA,0BAAAA,MAAY,MAAA,OAAA,gDAAA,QAAQ;AAAA,UACpB;AAAA,UACD,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,gDAAc,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AAERA,oBAAG,MAAC,aAAY;AAAA,IACjB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpMD,GAAG,gBAAgB,SAAS;"}