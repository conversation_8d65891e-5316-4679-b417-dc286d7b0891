{"version": 3, "file": "u-link.js", "sources": ["uview-plus_3.4.28/components/u-link/u-link.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtbGluay91LWxpbmsudnVl"], "sourcesContent": ["<template>\n\t<text\n\t    class=\"u-link\"\n\t    @tap.stop=\"openLink\"\n\t    :style=\"[linkStyle, addStyle(customStyle)]\"\n\t>{{text}}</text>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, addUnit, getPx, toast } from '../../libs/function/index';\n\t/**\n\t * link 超链接\n\t * @description 该组件为超链接组件，在不同平台有不同表现形式：在APP平台会通过plus环境打开内置浏览器，在小程序中把链接复制到粘贴板，同时提示信息，在H5中通过window.open打开链接。\n\t * @tutorial https://ijry.github.io/uview-plus/components/link.html\n\t * @property {String}\t\t\tcolor\t\t文字颜色 （默认 color['u-primary'] ）\n\t * @property {String ｜ Number}\tfontSize\t字体大小，单位px （默认 15 ）\n\t * @property {Boolean}\t\t\tunderLine\t是否显示下划线 （默认 false ）\n\t * @property {String}\t\t\thref\t\t跳转的链接，要带上http(s)\n\t * @property {String}\t\t\tmpTips\t\t各个小程序平台把链接复制到粘贴板后的提示语（默认“链接已复制，请在浏览器打开”）\n\t * @property {String}\t\t\tlineColor\t下划线颜色，默认同color参数颜色 \n\t * @property {String}\t\t\ttext\t\t超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色 \n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * \n\t * @example <u-link href=\"http://www.uviewui.com\">蜀道难，难于上青天</u-link>\n\t */\n\texport default {\n\t\tname: \"u-link\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tlinkStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tcolor: this.color,\n\t\t\t\t\tfontSize: addUnit(this.fontSize),\n\t\t\t\t\t// line-height设置为比字体大小多2px\n\t\t\t\t\tlineHeight: addUnit(getPx(this.fontSize) + 2),\n\t\t\t\t\ttextDecoration: this.underLine ? 'underline' : 'none'\n\t\t\t\t}\n\t\t\t\t// if (this.underLine) {\n\t\t\t\t// \tstyle.borderBottomColor = this.lineColor || this.color\n\t\t\t\t// \tstyle.borderBottomWidth = '1px'\n\t\t\t\t// }\n\t\t\t\treturn style\n\t\t\t}\n\t\t},\n\t\temits: [\"click\"],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\topenLink() {\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\tplus.runtime.openURL(this.href)\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef H5\n\t\t\t\twindow.open(this.href)\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: this.href,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.hideToast();\n\t\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t\ttoast(this.mpTips);\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t\tthis.$emit('click')\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\t$u-link-line-height:1 !default;\n\n\t.u-link {\n\t\t/* #ifndef APP-NVUE */\n\t\tline-height: $u-link-line-height;\n\t\t/* #endif */\n\t\t@include flex;\n\t\tflex-wrap: wrap;\n\t\tflex: 1;\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-link/u-link.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "getPx", "addStyle", "uni", "toast"], "mappings": ";;;;;;AA4BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,6CAAK;AAAA,EAC9B,UAAU;AAAA,IACT,YAAY;AACX,YAAM,QAAQ;AAAA,QACb,OAAO,KAAK;AAAA,QACZ,UAAUC,qCAAAA,QAAQ,KAAK,QAAQ;AAAA;AAAA,QAE/B,YAAYA,qCAAO,QAACC,qCAAK,MAAC,KAAK,QAAQ,IAAI,CAAC;AAAA,QAC5C,gBAAgB,KAAK,YAAY,cAAc;AAAA,MAChD;AAKA,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,UAAAC,qCAAQ;AAAA,IACR,WAAW;AAQVC,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM,KAAK;AAAA,QACX,SAAS,MAAM;AACdA,wBAAG,MAAC,UAAS;AACb,eAAK,UAAU,MAAM;AACpBC,uDAAM,KAAK,MAAM;AAAA,WACjB;AAAA,QACF;AAAA,MACD,CAAC;AAED,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;ACtED,GAAG,gBAAgB,SAAS;"}