"use strict";const t=require("../../../common/vendor.js"),e=require("./test.js"),r=require("./digit.js"),n=require("../config/config.js");function o(r,n=!1){return e.number(r)?n?`${r}px`:Number(r):/(rpx|upx)$/.test(r)?n?`${t.index.upx2px(parseInt(r))}px`:Number(t.index.upx2px(parseInt(r))):n?`${parseInt(r)}px`:parseInt(r)}function i(t=30){return new Promise((e=>{setTimeout((()=>{e()}),t)}))}function s(){let e={};return e=t.index.getWindowInfo(),e}function a(t){let e=this.$parent;for(;e;){if(t=t.replace(/up-([a-zA-Z0-9-_]+)/g,"u-$1"),!e.$options||e.$options.name===t)return e;e=e.$parent}return!1}function c(t,r="object"){if(e.empty(t)||"object"==typeof t&&"object"===r||"string"===r&&"string"==typeof t)return t;if("object"===r){const e=(t=h(t)).split(";"),r={};for(let t=0;t<e.length;t++)if(e[t]){const n=e[t].split(":");r[h(n[0])]=h(n[1])}return r}let n="";return"object"==typeof t&&t.forEach(((t,e)=>{const r=e.replace(/([A-Z])/g,"-$1").toLowerCase();n+=`${r}:${t};`})),h(n)}function u(t="auto",r=""){return r||(r=n.config.unit||"px"),"rpx"==r&&e.number(String(t))&&(t*=2),t=String(t),e.number(t)?`${t}${r}`:t}function p(t){if([null,void 0,NaN,!1].includes(t))return t;if("object"!=typeof t&&"function"!=typeof t)return t;const r=e.array(t)?[]:{};for(const e in t)t.hasOwnProperty(e)&&(r[e]="object"==typeof t[e]?p(t[e]):t[e]);return r}function f(t={},e={}){let r=p(t);if("object"!=typeof r||"object"!=typeof e)return!1;for(const n in e)e.hasOwnProperty(n)&&(n in r?null==e[n]||"object"!=typeof r[n]||"object"!=typeof e[n]?r[n]=e[n]:r[n].concat&&e[n].concat?r[n]=r[n].concat(e[n]):r[n]=f(r[n],e[n]):r[n]=e[n]);return r}function l(t,e={}){if("object"!=typeof t||"object"!=typeof e)return!1;for(const r in e)e.hasOwnProperty(r)&&(r in t?null==e[r]||"object"!=typeof t[r]||"object"!=typeof e[r]?t[r]=e[r]:t[r].concat&&e[r].concat?t[r]=t[r].concat(e[r]):t[r]=l(t[r],e[r]):t[r]=e[r]);return t}function g(t){}function d(t=null,e="yyyy-mm-dd"){let r;r=t?/^\d{10}$/.test(t.toString().trim())?new Date(1e3*t):"string"==typeof t&&/^\d+$/.test(t.trim())?new Date(Number(t)):new Date("string"==typeof t?t.replace(/-/g,"/"):t):new Date;const n={y:r.getFullYear().toString(),m:(r.getMonth()+1).toString().padStart(2,"0"),d:r.getDate().toString().padStart(2,"0"),h:r.getHours().toString().padStart(2,"0"),M:r.getMinutes().toString().padStart(2,"0"),s:r.getSeconds().toString().padStart(2,"0")};for(const o in n){const[t]=new RegExp(`${o}+`).exec(e)||[];if(t){const r="y"===o&&2===t.length?2:0;e=e.replace(t,n[o].slice(r))}}return e}function h(t,e="both"){return t=String(t),"both"==e?t.replace(/^\s+|\s+$/g,""):"left"==e?t.replace(/^\s*/,""):"right"==e?t.replace(/(\s*$)/g,""):"all"==e?t.replace(/\s+/g,""):t}function m(t={},e=!0,r="brackets"){const n=e?"?":"",o=[];-1==["indices","brackets","repeat","comma"].indexOf(r)&&(r="brackets");for(const i in t){const e=t[i];if(!(["",void 0,null].indexOf(e)>=0))if(e.constructor===Array)switch(r){case"indices":for(let r=0;r<e.length;r++)o.push(`${i}[${r}]=${e[r]}`);break;case"brackets":default:e.forEach((t=>{o.push(`${i}[]=${t}`)}));break;case"repeat":e.forEach((t=>{o.push(`${i}=${t}`)}));break;case"comma":let t="";e.forEach((e=>{t+=(t?",":"")+e})),o.push(`${i}=${t}`)}else o.push(`${i}=${e}`)}return o.length?n+o.join("&"):""}function y(e,r=2e3){t.index.showToast({title:String(e),icon:"none",duration:r})}function b(t,e=0,n=".",o=","){t=`${t}`.replace(/[^0-9+-Ee.]/g,"");const i=isFinite(+t)?+t:0,s=isFinite(+e)?Math.abs(e):0,a=void 0===o?",":o,c=void 0===n?".":n;let u="";u=(s?r.round(i,s)+"":`${Math.round(i)}`).split(".");const p=/(-?\d+)(\d{3})/;for(;p.test(u[0]);)u[0]=u[0].replace(p,`$1${a}$2`);return(u[1]||"").length<s&&(u[1]=u[1]||"",u[1]+=new Array(s-u[1].length+1).join("0")),u.join(c)}function $(){const t=getCurrentPages();return`/${t[t.length-1].route||""}`}String.prototype.padStart||(String.prototype.padStart=function(t,e=" "){if("[object String]"!==Object.prototype.toString.call(e))throw new TypeError("fillString must be String");const r=this;if(r.length>=t)return String(r);const n=t-r.length;let o=Math.ceil(n/e.length);for(;o>>=1;)e+=e,1===o&&(e+=e);return e.slice(0,n)+r});const x={range:function(t=0,e=0,r=0){return Math.max(t,Math.min(e,Number(r)))},getPx:o,sleep:i,os:function(){return t.index.getDeviceInfo().platform.toLowerCase()},sys:function(){return t.index.getSystemInfoSync()},getWindowInfo:s,random:function(t,e){if(t>=0&&e>0&&e>=t){const r=e-t+1;return Math.floor(Math.random()*r+t)}return 0},guid:function(t=32,e=!0,r=null){const n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),o=[];if(r=r||n.length,t)for(let i=0;i<t;i++)o[i]=n[0|Math.random()*r];else{let t;o[8]=o[13]=o[18]=o[23]="-",o[14]="4";for(let e=0;e<36;e++)o[e]||(t=0|16*Math.random(),o[e]=n[19==e?3&t|8:t])}return e?(o.shift(),`u${o.join("")}`):o.join("")},$parent:a,addStyle:c,addUnit:u,deepClone:p,deepMerge:f,shallowMerge:l,error:g,randomArray:function(t=[]){return t.sort((()=>Math.random()-.5))},timeFormat:d,timeFrom:function(t=null,e="yyyy-mm-dd"){null==t&&(t=Number(new Date)),10==(t=parseInt(t)).toString().length&&(t*=1e3);let r=(new Date).getTime()-t;r=parseInt(r/1e3);let n="";switch(!0){case r<300:n="刚刚";break;case r>=300&&r<3600:n=`${parseInt(r/60)}分钟前`;break;case r>=3600&&r<86400:n=`${parseInt(r/3600)}小时前`;break;case r>=86400&&r<2592e3:n=`${parseInt(r/86400)}天前`;break;default:n=!1===e?r>=2592e3&&r<31536e3?`${parseInt(r/2592e3)}个月前`:`${parseInt(r/31536e3)}年前`:d(t,e)}return n},trim:h,queryParams:m,toast:y,type2icon:function(t="success",e=!1){-1==["primary","info","error","warning","success"].indexOf(t)&&(t="success");let r="";switch(t){case"primary":case"info":r="info-circle";break;case"error":r="close-circle";break;case"warning":r="error-circle";break;default:r="checkmark-circle"}return e&&(r+="-fill"),r},priceFormat:b,getDuration:function(t,e=!0){const r=parseInt(t);return e?/s$/.test(t)?t:t>30?`${t}ms`:`${t}s`:/ms$/.test(t)?r:/s$/.test(t)?r>30?r:1e3*r:r},padZero:function(t){return`00${t}`.slice(-2)},formValidate:function(t,e){const r=a.call(t,"u-form-item"),n=a.call(t,"u-form");r&&n&&n.validateField(r.prop,(()=>{}),e)},getProperty:function(t,e){if("object"!=typeof t||null==t)return"";if("string"!=typeof e||""===e)return"";if(-1!==e.indexOf(".")){const r=e.split(".");let n=t[r[0]]||{};for(let t=1;t<r.length;t++)n&&(n=n[r[t]]);return n}return t[e]},setProperty:function(t,e,r){if("object"!=typeof t||null==t)return;const n=function(t,e,r){if(1!==e.length)for(;e.length>1;){const o=e[0];t[o]&&"object"==typeof t[o]||(t[o]={}),e.shift(),n(t[o],e,r)}else t[e[0]]=r};if("string"!=typeof e||""===e);else if(-1!==e.indexOf(".")){const o=e.split(".");n(t,o,r)}else t[e]=r},page:$,pages:function(){return getCurrentPages()},getValueByPath:function(t,e){return e.split(".").reduce(((t,e)=>t&&void 0!==t[e]?t[e]:void 0),t)},genLightColor:function(t,e=95){const r=function(t){const e=t.toLowerCase().trim();if(e.startsWith("#")){const t=e.replace("#",""),r=3===t.length?t.split("").map((t=>t+t)).join(""):t;return{r:parseInt(r.substring(0,2),16),g:parseInt(r.substring(2,4),16),b:parseInt(r.substring(4,6),16)}}const r=e.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);if(r)return{r:+r[1],g:+r[2],b:+r[3]};throw new Error("Invalid color format")}(t),n=function(t,e,r){t/=255,e/=255,r/=255;const n=Math.max(t,e,r),o=Math.min(t,e,r);let i,s,a=(n+o)/2;if(n===o)i=s=0;else{const c=n-o;switch(s=a>.5?c/(2-n-o):c/(n+o),n){case t:i=(e-r)/c+(e<r?6:0);break;case e:i=(r-t)/c+2;break;case r:i=(t-e)/c+4}i=(60*i).toFixed(1)}return{h:+i,s:+(100*s).toFixed(1),l:+(100*a).toFixed(1)}}(r.r,r.g,r.b),o={h:n.h,s:n.s,l:Math.min(e,95)};return function(t,e,r){r/=100;const n=e*Math.min(r,1-r)/100,o=e=>{const o=(e+t/30)%12,i=r-n*Math.max(Math.min(o-3,9-o,1),-1);return Math.round(255*i).toString(16).padStart(2,"0")};return`#${o(0)}${o(8)}${o(4)}`}(o.h,o.s,o.l)}};exports.$parent=a,exports.addStyle=c,exports.addUnit=u,exports.deepMerge=f,exports.error=g,exports.getPx=o,exports.getWindowInfo=s,exports.index=x,exports.page=$,exports.priceFormat=b,exports.queryParams=m,exports.shallowMerge=l,exports.sleep=i,exports.timeFormat=d,exports.toast=y;
