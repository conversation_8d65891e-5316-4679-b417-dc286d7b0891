<template>
	<view class="common-container red-theme">
		<!-- 自定义导航栏 -->
		<custom-navbar title="红色环投" :showBack="false" :showHome="false">
			<template #right>
				<view class="navbar-right">
					<u-icon name="more-dot-fill" color="#FFFFFF" size="20"></u-icon>
					<u-icon name="camera-fill" color="#FFFFFF" size="20" style="margin-left: 15px;"></u-icon>
				</view>
			</template>
		</custom-navbar>

		<!-- 搜索框 -->
		<view class="search-box">
			<u-search placeholder="搜索" placeholderColor="#000000" :disabled="true" @search="onSearchClick" shape="round"></u-search>
		</view>

		<!-- 广告图 -->
	<view class="ad-section">
		<u-image
			:src="adImageUrl"
			width="100%"
			height="120"
			mode="widthFix"
			:fade="true"
			:borderRadius="8"
			@click="onAdClick"
		></u-image>
	</view>

		<!-- 新闻动态轮播图 -->
		<view class="swiper-section">
			<u-swiper
				:list="swiperList"
				height="300"
				radius="8"
				:autoplay="true"
				:interval="3000"
				:indicator="true"
				indicatorMode="dot"
				keyName="src"
				showTitle
				@click="onSwiperClick"
			></u-swiper>
		</view>

		<!-- 公告通知 -->
		<view class="notice-section">
			<view class="section-content">
				<u-notice-bar
					:text="noticeList.map(item => item.title)"
					direction="column"
					:duration="3000"
					:speed="80"
					color="#333333"
					bgColor="#ffffff"
					justifyContent="flex-start"
					@click="onNoticeClick"
				></u-notice-bar>
			</view>
		</view>

		<!-- 快捷入口 -->
		<view class="quick-entry">
			<view class="entry-grid">
				<view class="section-title">
					<u-text text="快捷入口" size="16" bold color="#333333"></u-text>
				</view>
				<u-grid :col="4" :border="false" @click="onGridClick">
					<u-grid-item v-for="(item, index) in quickEntryList" :key="index" :index="index">
						<view class="grid-item">
							<view class="icon-circle">
								<u-icon :name="item.icon || 'star-fill'" size="36" color="#E51C23"></u-icon>
							</view>
							<u-text :text="item.title" align="center" size="16" margin-top="4"></u-text>
						</view>
					</u-grid-item>
				</u-grid>
			</view>
		</view>

		<!-- 党建要闻 -->
		<view class="news-section">
			<view class="section-content">
				<view class="section-title">
					<u-text text="党建信息" size="16" bold color="#333333"></u-text>
				</view>
				<u-list :enable-flex="true" height="500px">
					<u-cell v-for="(item, index) in newsList" :key="index" :title="item.title" :titleStyle="{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}" isLink @click="onNewsClick(item)">
					</u-cell>
				</u-list>
			</view>
		</view>

		<!-- 底部导航 -->
		<view class="bottom-nav">
			<u-grid :col="5" :border="false" @click="onNavClick">
				<u-grid-item v-for="(item, index) in bottomNavList" :key="index" :index="index">
					<view class="nav-item">
						<u-icon :name="item.icon || 'home'" size="24" color="#FF0000"></u-icon>
						<u-text :text="item.name" align="center" size="14" margin-top="4"></u-text>
					</view>
				</u-grid-item>
			</u-grid>
		</view>


	</view>
</template>

<script>
	import { articleApi, testGetArticleList, menuApi } from '@/api/index.js';
	import CustomNavbar from '@/components/common/custom-navbar.vue';
	import { fetchImageById, getFullImageUrl } from '@/utils/image.js';

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				// 轮播图数据
				swiperList: [
					{
						src: '/static/images/1.png',
						title: '学习贯彻习近平新时代中国特色社会主义思想'
					},
					{
						src: '/static/images/2.png',
						title: '深入学习贯彻党的二十大精神'
					},
					{
						src: '/static/images/3.png',
						title: '喜迎建党102周年系列活动'
					}
				],
				// 公告通知数据
				noticeList: [
					{
						id: 1,
						title: '关于开展2023年度党员民主评议工作的通知'
					},
					{
						id: 2,
						title: '关于召开2023年度组织生活会的通知'
					},
					{
						id: 3,
						title: '关于开展党史学习教育的通知'
					}
				],
				// 快捷入口数据
				quickEntryList: [
					{
						id: 1,
						title: '党建工作',
						icon: 'home-fill',
						url: '/pages/party/index'
					},
					{
						id: 2,
						title: '基层动态',
						icon: 'bell-fill',
						url: ''
					},
					{
						id: 3,
						title: '党建品牌',
						icon: 'star-fill',
						url: ''
					},
					{
						id: 4,
						title: '红旗支部',
						icon: 'heart-fill',
						url: ''
					},
					{
						id: 5,
						title: '先锋党员',
						icon: 'account-fill',
						url: ''
					},
					{
						id: 6,
						title: '学习资料',
						icon: 'bookmark-fill',
						url: ''
					},
					{
						id: 7,
						title: '志愿服务',
						icon: 'gift-fill',
						url: ''
					},
					{
						id: 8,
						title: '党费交纳',
						icon: 'rmb-circle-fill',
						url: '',
						appId: 'wxc324e5e2fb50b1c6' // 党费通小程序的appId
					}
				],
				// 党建要闻数据
				newsList: [],
				// 底部导航数据
				bottomNavList: [],
				// 菜单图标映射
				menuIconMap: {
					'党建信息': 'home-fill',
					'阵地建设': 'star-fill',
					'学习园地': 'bookmark-fill',
					'示范典型': 'heart-fill',
					'群团工作': 'account-fill'
				},
				// 广告图数据
				adImage: null,
				// 广告图URL
				adImageUrl: ''
			}
		},
		onLoad() {
			// 页面加载时的逻辑
			console.log('开始测试API请求...');

			// 方法1：直接调用测试函数
			testGetArticleList();

			// 方法2：使用API函数获取文章列表
			this.fetchArticleList();

			// 获取菜单数据
			this.fetchMenuList();

			// 获取广告图片数据
			this.fetchAdImage();
		},
		methods: {
			// 获取文章列表
			async fetchArticleList() {
				try {
					const result = await articleApi.getArticleList();
					console.log('获取文章列表成功:', result);
					// 将API返回的数据赋值给页面上的变量，替换模拟数据
					if (result && result.success && Array.isArray(result.data)) {
						this.newsList = result.data;
						console.log('文章列表数据已更新');
					}
				} catch (error) {
					console.error('获取文章列表失败:', error);
				}
			},
			// 轮播图点击事件
			onSwiperClick(index) {
				uni.showToast({
					title: '点击了轮播图：' + this.swiperList[index].title,
					icon: 'none'
				})
			},
			// 搜索点击事件
			onSearchClick(e) {
				uni.showToast({
					title: '搜索内容：' + (e.value || ''),
					icon: 'none'
				})
			},
			// 公告点击事件
			onNoticeClick(index) {
				const item = this.noticeList[index];
				uni.showToast({
					title: '点击了公告：' + item.title,
					icon: 'none'
				})
			},
			// 快捷入口点击事件
			onGridClick(index) {
				const item = this.quickEntryList[index]
				console.log('点击了快捷入口:', item);

				// 如果有配置appId，则跳转到小程序
				if (item.appId) {
					console.log('跳转到小程序，appId:', item.appId);
					this.navigateToMiniProgram(item);
				} else if (item.url) {
					// 如果有配置URL，则跳转到对应页面
					console.log('跳转到URL:', item.url);

					uni.navigateTo({
						url: item.url,
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				} else {
					// 没有配置URL或appId的入口项显示提示
					uni.showToast({
						title: '暂未配置：' + item.title,
						icon: 'none'
					});
				}
			},
			// 跳转到小程序
			navigateToMiniProgram(item) {
				console.log('准备跳转到小程序:', item.title, 'appId:', item.appId);

				uni.navigateToMiniProgram({
					appId: item.appId,
					path: '', // 打开小程序首页，可以根据需要指定具体页面
					extraData: {
						// 可以传递一些数据给目标小程序
						source: '红色环投',
						from: 'quickEntry'
					},
					envVersion: 'release', // 正式版
					success(res) {
						console.log('跳转小程序成功:', res);
					},
					fail(err) {
						console.error('跳转小程序失败:', err);
						uni.showToast({
							title: '无法打开' + item.title,
							icon: 'none',
							duration: 2000
						});
					}
				});
			},
			// 新闻点击事件
			onNewsClick(item) {
				console.log('点击了文章:', item);
				// 跳转到统一的文章详情页面
				uni.navigateTo({
					url: `/pages/article/detail?id=${item.id}&type=news`,
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},
			// 获取菜单列表
			async fetchMenuList() {
				try {
					const result = await menuApi.getMenuList();
					console.log('获取菜单列表成功:', result);

					if (result && result.success && Array.isArray(result.data)) {
						// 过滤出一级菜单作为底部导航
						const firstLevelMenus = result.data.filter(item => item.level === 1);

						// 为每个菜单项添加图标
						this.bottomNavList = firstLevelMenus.map(item => {
							return {
								...item,
								icon: this.menuIconMap[item.name] || 'home-fill'
							};
						});

						console.log('底部导航菜单数据已更新:', this.bottomNavList);

						// 处理快捷入口的URL
						// 获取所有二级菜单
						const secondLevelMenus = result.data.filter(item => item.level === 2);

						// 遍历快捷入口列表
						this.quickEntryList.forEach((quickEntry, index) => {
							// 特殊处理"党建工作"，保持原有URL
							// if (quickEntry.title === "党建工作") {
							// 	return;
							// }

							// 查找与快捷入口标题匹配的二级菜单
							const matchedMenu = secondLevelMenus.find(menu => menu.name === quickEntry.title);

							if (matchedMenu) {
								// 找到匹配的二级菜单，设置URL，包含一级菜单ID和二级菜单ID
								const parentMenu = firstLevelMenus.find(menu => menu.id === matchedMenu.parentId);

								if (parentMenu) {
									// 设置URL，格式为：/pages/menus/index?id=一级菜单ID&name=一级菜单名称&subMenuId=二级菜单ID
									this.quickEntryList[index].url = `/pages/menus/index?id=${parentMenu.id}&name=${encodeURIComponent(parentMenu.name)}&subMenuId=${matchedMenu.id}`;
									console.log(`已设置快捷入口 "${quickEntry.title}" 的URL:`, this.quickEntryList[index].url);
								}
							}
							// 如果没有找到匹配的二级菜单，保持原有URL不变
						});
					}
				} catch (error) {
					console.error('获取菜单列表失败:', error);
				}
			},





			// 广告图点击事件
			onAdClick() {
				console.log('点击了广告图');
				uni.showToast({
					title: '点击了广告图',
					icon: 'none'
				});
			},

			// 底部导航点击事件
			onNavClick(index) {
				const item = this.bottomNavList[index];
				console.log('点击了底部导航:', item);

				// 跳转到对应的内页
				uni.navigateTo({
					url: `/pages/menus/index?id=${item.id}&name=${encodeURIComponent(item.name)}`,
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 获取广告图片数据
			async fetchAdImage() {
				try {
					// 获取ID为36的图片信息
					const imageInfo = await fetchImageById(36);
					console.log('获取广告图片信息成功:', imageInfo);

					if (imageInfo && imageInfo.url) {
						this.adImage = imageInfo;
						// 使用工具函数获取完整的图片URL
						this.adImageUrl = getFullImageUrl(imageInfo.url);
						console.log('广告图片URL:', this.adImageUrl);
					} else {
						console.error('广告图片信息不完整');
						// 设置默认图片
						this.adImageUrl = '/static/images/640.gif';
					}
				} catch (error) {
					console.error('获取广告图片信息失败:', error);
					// 设置默认图片
					this.adImageUrl = '/static/images/640.gif';
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 使用全局样式文件中的.common-container */

	.search-box {
		margin-bottom: 30rpx;
	}

	.section-title {
		margin-bottom: 20rpx;
		padding: 16rpx 24rpx;
		background-color: #ffffff;
		border-radius: 8rpx;
		box-shadow: 0 rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.section-content {
		background-color: #ffffff;
		border-radius: 8rpx;
		padding: 10rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.ad-section {
		margin-bottom: 30rpx;
		padding: 0 10rpx;
	}

	.swiper-section {
		margin-bottom: 30rpx;
	}

	/* 自定义轮播图样式 */
	:deep(.u-swiper__wrapper) {
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	}

	:deep(.u-swiper-indicator__dot) {
		width: 12rpx;
		height: 12rpx;
	}

	:deep(.u-swiper__title) {
		background-color: rgba(0, 0, 0, 0.4);
		padding: 16rpx;
		font-size: 28rpx;
	}

	.notice-section {
		margin-bottom: 30rpx;
	}

	/* 自定义NoticeBar样式 */
	:deep(.u-notice-bar) {
		padding: 10rpx 0;
	}

	:deep(.u-notice-bar__content__text) {
		font-size: 28rpx;
		padding: 12rpx 24rpx;
		text-align: left;
	}

	.quick-entry {
		margin-bottom: 30rpx;
	}

	.entry-grid {
		background-color: #ffffff;
		border-radius: 8rpx;
		padding: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.grid-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 8rpx;
		margin: 4rpx;
		height: 110rpx;
	}

	.icon-circle {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		background-color: #ffeeee;
		margin-bottom: 6rpx;
	}

	.news-section {
		margin-bottom: 30rpx;
	}

	/* 由于使用了u-cell组件，不再需要news-item样式 */

	.bottom-nav {
		background-color: #ffffff;
		border-radius: 0;
		padding: 6rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 999;
		border-top: 1rpx solid #f5f5f5;
		height: 100rpx;
	}

	.nav-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 6rpx 0;
	}


</style>
