"use strict";Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const o=require("./common/vendor.js"),n=require("./uview-plus_3.4.28/index.js"),e=require("./utils/uview-config.js");Math;const t={onLaunch:function(){console.log("App Launch"),this.$options.fontLoaded||(this.$options.fontLoaded=!0,o.index.loadFontFace({family:"uicon-iconfont",source:'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',success(){console.log("字体加载成功")},fail(o){console.error("字体加载失败",o)}}))},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};function i(){const i=o.createSSRApp(t);return i.use(n.uviewPlus,(()=>({options:{config:{unit:"rpx",iconUrl:e.uviewConfig.iconUrl,customIcon:e.uviewConfig.customIcon}}}))),{app:i}}i().app.mount("#app"),exports.createApp=i;
