{"version": 3, "file": "rowNotice.js", "sources": ["uview-plus_3.4.28/components/u-row-notice/rowNotice.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:19:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/rowNotice.js\n */\nexport default {\n    // rowNotice\n    rowNotice: {\n        text: '',\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        fontSize: 14,\n        speed: 80\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,EACV;AACL;;"}