"use strict";const o=require("./props.js"),e=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),t=require("../../libs/function/index.js"),n=require("../../../common/vendor.js"),c={name:"u-notice-bar",mixins:[e.mpMixin,i.mixin,o.props],data:()=>({show:!0}),emits:["click","close"],methods:{addStyle:t.addStyle,click(o){this.$emit("click",o),this.url&&this.linkType&&this.openPage()},close(){this.show=!1,this.$emit("close")}}};if(!Array){(n.resolveComponent("u-column-notice")+n.resolveComponent("u-row-notice"))()}Math||((()=>"../u-column-notice/u-column-notice.js")+(()=>"../u-row-notice/u-row-notice.js"))();const s=n._export_sfc(c,[["render",function(o,e,i,t,c,s){return n.e({a:c.show},c.show?n.e({b:"column"===o.direction||"row"===o.direction&&o.step},"column"===o.direction||"row"===o.direction&&o.step?{c:n.o(s.close),d:n.o(s.click),e:n.p({color:o.color,bgColor:o.bgColor,text:o.text,mode:o.mode,step:o.step,icon:o.icon,"disable-touch":o.disableTouch,fontSize:o.fontSize,duration:o.duration,justifyContent:o.justifyContent})}:{f:n.o(s.close),g:n.o(s.click),h:n.p({color:o.color,bgColor:o.bgColor,text:o.text,mode:o.mode,fontSize:o.fontSize,speed:o.speed,url:o.url,linkType:o.linkType,icon:o.icon})},{i:n.s({backgroundColor:o.bgColor}),j:n.s(s.addStyle(o.customStyle))}):{})}],["__scopeId","data-v-3633f13e"]]);wx.createComponent(s);
