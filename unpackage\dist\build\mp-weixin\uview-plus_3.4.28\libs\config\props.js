"use strict";const e=require("../../../common/vendor.js"),o=require("./config.js"),r=require("./zIndex.js"),n=require("./color.js"),s=require("../function/http.js"),t=require("../function/index.js"),i=require("../../components/u-action-sheet/actionSheet.js"),u=require("../../components/u-album/album.js"),c=require("../../components/u-alert/alert.js"),p=require("../../components/u-avatar/avatar.js"),a=require("../../components/u-avatar-group/avatarGroup.js"),m=require("../../components/u-back-top/backtop.js"),l=require("../../components/u-badge/badge.js"),j=require("../../components/u-button/button.js"),q=require("../../components/u-calendar/calendar.js"),d=require("../../components/u-car-keyboard/carKeyboard.js"),b=require("../../components/u-card/card.js"),g=require("../../components/u-cell/cell.js"),w=require("../../components/u-cell-group/cellGroup.js"),k=require("../../components/u-checkbox/checkbox.js"),I=require("../../components/u-checkbox-group/checkboxGroup.js"),x=require("../../components/u-circle-progress/circleProgress.js"),h=require("../../components/u-code/code.js"),y=require("../../components/u-code-input/codeInput.js"),v=require("../../components/u-col/col.js"),S=require("../../components/u-collapse/collapse.js"),C=require("../../components/u-collapse-item/collapseItem.js"),f=require("../../components/u-column-notice/columnNotice.js"),P=require("../../components/u-count-down/countDown.js"),G=require("../../components/u-count-to/countTo.js"),L=require("../../components/u-datetime-picker/datetimePicker.js"),N=require("../../components/u-divider/divider.js"),T=require("../../components/u-empty/empty.js"),A=require("../../components/u-form/form.js"),B=require("../../components/u-form-item/formItem.js"),M=require("../../components/u-gap/gap.js"),R=require("../../components/u-grid/grid.js"),K=require("../../components/u-grid-item/gridItem.js"),D=require("../../components/u-icon/icon.js"),z=require("../../components/u-image/image.js"),E=require("../../components/u-index-anchor/indexAnchor.js"),F=require("../../components/u-index-list/indexList.js"),O=require("../../components/u-input/input.js"),U=require("../../components/u-keyboard/keyboard.js"),H=require("../../components/u-line/line.js"),J=require("../../components/u-line-progress/lineProgress.js"),Q=require("../../components/u-link/link.js"),V=require("../../components/u-list/list.js"),W=require("../../components/u-list-item/listItem.js"),X=require("../../components/u-loading-icon/loadingIcon.js"),Y=require("../../components/u-loading-page/loadingPage.js"),Z=require("../../components/u-loadmore/loadmore.js"),$=require("../../components/u-modal/modal.js"),_=require("../../components/u-navbar/navbar.js"),ee=require("../../components/u-no-network/noNetwork.js"),oe=require("../../components/u-notice-bar/noticeBar.js"),re=require("../../components/u-notify/notify.js"),ne=require("../../components/u-number-box/numberBox.js"),se=require("../../components/u-number-keyboard/numberKeyboard.js"),te=require("../../components/u-overlay/overlay.js"),ie=require("../../components/u-parse/parse.js"),ue=require("../../components/u-picker/picker.js"),ce=require("../../components/u-popup/popup.js"),pe=require("../../components/u-radio/radio.js"),ae=require("../../components/u-radio-group/radioGroup.js"),me=require("../../components/u-rate/rate.js"),le=require("../../components/u-read-more/readMore.js"),je=require("../../components/u-row/row.js"),qe=require("../../components/u-row-notice/rowNotice.js"),de=require("../../components/u-scroll-list/scrollList.js"),be=require("../../components/u-search/search.js"),ge=require("../../components/u-section/section.js"),we=require("../../components/u-skeleton/skeleton.js"),ke=require("../../components/u-slider/slider.js"),Ie=require("../../components/u-status-bar/statusBar.js"),xe=require("../../components/u-steps/steps.js"),he=require("../../components/u-steps-item/stepsItem.js"),ye=require("../../components/u-sticky/sticky.js"),ve=require("../../components/u-subsection/subsection.js"),Se=require("../../components/u-swipe-action/swipeAction.js"),Ce=require("../../components/u-swipe-action-item/swipeActionItem.js"),fe=require("../../components/u-swiper/swiper.js"),Pe=require("../../components/u-swiper-indicator/swipterIndicator.js"),Ge=require("../../components/u-switch/switch.js"),Le=require("../../components/u-tabbar/tabbar.js"),Ne=require("../../components/u-tabbar-item/tabbarItem.js"),Te=require("../../components/u-tabs/tabs.js"),Ae=require("../../components/u-tag/tag.js"),Be=require("../../components/u-text/text.js"),Me=require("../../components/u-textarea/textarea.js"),Re=require("../../components/u-toast/toast.js"),Ke=require("../../components/u-toolbar/toolbar.js"),De=require("../../components/u-tooltip/tooltip.js"),ze=require("../../components/u-transition/transition.js"),Ee=require("../../components/u-upload/upload.js"),Fe={...i.ActionSheet,...u.Album,...c.Alert,...p.Avatar,...a.AvatarGroup,...m.Backtop,...l.Badge,...j.Button,...q.Calendar,...d.CarKeyboard,...b.Card,...g.Cell,...w.CellGroup,...k.Checkbox,...I.CheckboxGroup,...x.CircleProgress,...h.Code,...y.CodeInput,...v.Col,...S.Collapse,...C.CollapseItem,...f.ColumnNotice,...P.CountDown,...G.CountTo,...L.DatetimePicker,...N.Divider,...T.Empty,...A.Form,...B.GormItem,...M.Gap,...R.Grid,...K.GridItem,...D.Icon,...z.Image,...E.IndexAnchor,...F.IndexList,...O.Input,...U.Keyboard,...H.Line,...J.LineProgress,...Q.Link,...V.List,...W.ListItem,...X.LoadingIcon,...Y.LoadingPage,...Z.Loadmore,...$.Modal,..._.Navbar,...ee.NoNetwork,...oe.NoticeBar,...re.Notify,...ne.NumberBox,...se.NumberKeyboard,...te.Overlay,...ie.Parse,...ue.Picker,...ce.Popup,...pe.Radio,...ae.RadioGroup,...me.Rate,...le.ReadMore,...je.Row,...qe.RowNotice,...de.ScrollList,...be.Search,...ge.Section,...we.Skeleton,...ke.Slider,...Ie.StatusBar,...xe.Steps,...he.StepsItem,...ye.Sticky,...ve.Subsection,...Se.SwipeAction,...Ce.SwipeActionItem,...fe.Swiper,...Pe.SwipterIndicator,...Ge.Switch,...Le.Tabbar,...Ne.TabbarItem,...Te.Tabs,...Ae.Tag,...Be.Text,...Me.Textarea,...Re.Toast,...Ke.Toolbar,...De.Tooltip,...ze.Transition,...Ee.Upload};if(e.index&&e.index.upuiParams){console.log("setting uview-plus");let i=e.index.upuiParams();i.httpIns&&i.httpIns(s.http),i.options&&(Oe=i.options,t.shallowMerge(o.config,Oe.config||{}),t.shallowMerge(Fe,Oe.props||{}),t.shallowMerge(n.color,Oe.color||{}),t.shallowMerge(r.zIndex,Oe.zIndex||{}))}var Oe;exports.props=Fe;
