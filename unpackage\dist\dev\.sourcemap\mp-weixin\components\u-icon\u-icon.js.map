{"version": 3, "file": "u-icon.js", "sources": ["components/u-icon/u-icon.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby9jb21wb25lbnRzL3UtaWNvbi91LWljb24udnVl"], "sourcesContent": ["<template>\n\t<view\n\t    class=\"u-icon\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"['u-icon--' + labelPos]\"\n\t>\n\t\t<image\n\t\t    class=\"u-icon__img\"\n\t\t    v-if=\"isImg\"\n\t\t    :src=\"name\"\n\t\t    :mode=\"imgMode\"\n\t\t    :style=\"[imgStyle, addStyle(customStyle)]\"\n\t\t></image>\n\t\t<text\n\t\t    v-else\n\t\t    class=\"u-icon__icon\"\n\t\t    :class=\"uClasses\"\n\t\t    :style=\"[iconStyle, addStyle(customStyle)]\"\n\t\t    :hover-class=\"hoverClass\"\n\t\t>{{icon}}</text>\n\t\t<!-- 这里进行空字符串判断，如果仅仅是v-if=\"label\"，可能会出现传递0的时候，结果也无法显示 -->\n\t\t<text\n\t\t    v-if=\"label !== ''\"\n\t\t    class=\"u-icon__label\"\n\t\t    :style=\"{\n\t\t\tcolor: labelColor,\n\t\t\tfontSize: addUnit(labelSize),\n\t\t\tmarginLeft: labelPos == 'right' ? addUnit(space) : 0,\n\t\t\tmarginTop: labelPos == 'bottom' ? addUnit(space) : 0,\n\t\t\tmarginRight: labelPos == 'left' ? addUnit(space) : 0,\n\t\t\tmarginBottom: labelPos == 'top' ? addUnit(space) : 0,\n\t\t}\"\n\t\t>{{ label }}</text>\n\t</view>\n</template>\n\n<script>\n\t// 引入图标名称，已经对应的unicode\n\timport icons from '../../uview-plus_3.4.28/components/u-icon/icons';\n\timport { props } from '../../uview-plus_3.4.28/components/u-icon/props';\n\timport config from '../../uview-plus_3.4.28/libs/config/config';\n\timport { mpMixin } from '../../uview-plus_3.4.28/libs/mixin/mpMixin';\n\timport { mixin } from '../../uview-plus_3.4.28/libs/mixin/mixin';\n\timport { addUnit, addStyle } from '../../uview-plus_3.4.28/libs/function/index';\n\t/**\n\t * icon 图标\n\t * @description 基于字体的图标集，包含了大多数常见场景的图标。\n\t * @tutorial https://ijry.github.io/uview-plus/components/icon.html\n\t */\n\texport default {\n\t\tname: 'u-icon',\n\t\t// 移除 beforeCreate 钩子，避免覆盖原组件的行为\n\t\tdata() {\n\t\t\treturn {\n\t\t\t}\n\t\t},\n\t\temits: ['click'],\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\tuClasses() {\n\t\t\t\tlet classes = []\n\t\t\t\tclasses.push(this.customPrefix + '-' + this.name)\n\t\t\t\t// uview-plus内置图标类名为u-iconfont\n\t\t\t\tif (this.customPrefix == 'uicon') {\n\t\t\t\t\tclasses.push('u-iconfont')\n\t\t\t\t} else {\n\t\t\t\t\t// 不能缺少这一步，否则自定义图标会无效\n\t\t\t\t\tclasses.push(this.customPrefix)\n\t\t\t\t}\n\t\t\t\t// 主题色，通过类配置\n\t\t\t\tif (this.color && config.type.includes(this.color)) classes.push('u-icon__icon--' + this.color)\n\t\t\t\t// 阿里，头条，百度小程序通过数组绑定类名时，无法直接使用[a, b, c]的形式，否则无法识别\n\t\t\t\t// 故需将其拆成一个字符串的形式，通过空格隔开各个类名\n\t\t\t\t//#ifdef MP-ALIPAY || MP-TOUTIAO || MP-BAIDU\n\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t//#endif\n\t\t\t\treturn classes\n\t\t\t},\n\t\t\ticonStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle = {\n\t\t\t\t\tfontSize: addUnit(this.size),\n\t\t\t\t\tlineHeight: addUnit(this.size),\n\t\t\t\t\tfontWeight: this.bold ? 'bold' : 'normal',\n\t\t\t\t\t// 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中\n\t\t\t\t\ttop: addUnit(this.top)\n\t\t\t\t}\n\t\t\t\tif (this.customPrefix !== 'uicon') {\n\t\t\t\t\tstyle.fontFamily = this.customPrefix\n\t\t\t\t}\n\t\t\t\t// 非主题色值时，才当作颜色值\n\t\t\t\tif (this.color && !config.type.includes(this.color)) style.color = this.color\n\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 判断传入的name属性，是否图片路径，只要带有\"/\"均认为是图片形式\n\t\t\tisImg() {\n\t\t\t\treturn this.name.indexOf('/') !== -1\n\t\t\t},\n\t\t\timgStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\t// 如果设置width和height属性，则优先使用，否则使用size属性\n\t\t\t\tstyle.width = this.width ? addUnit(this.width) : addUnit(this.size)\n\t\t\t\tstyle.height = this.height ? addUnit(this.height) : addUnit(this.size)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 通过图标名，查找对应的图标\n\t\t\ticon() {\n\t\t\t\t// 使用自定义图标的时候页面上会把name属性也展示出来，所以在这里处理一下\n\t\t\t\tif (this.customPrefix !== \"uicon\") {\n\t\t\t\t\treturn config.customIcons[this.name] || this.name;\n\t\t\t\t}\n\t\t\t\t// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码\n\t\t\t\treturn icons['uicon-' + this.name] || this.name\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\taddUnit,\n\t\t\tclickHandler(e) {\n\t\t\t\tthis.$emit('click', this.index, e)\n\t\t\t\t// 是否阻止事件冒泡\n\t\t\t\tthis.stop && this.preventEvent(e)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../uview-plus_3.4.28/libs/css/components.scss\";\n\n\t// 变量定义\n\t$u-icon-primary: $u-primary !default;\n\t$u-icon-success: $u-success !default;\n\t$u-icon-info: $u-info !default;\n\t$u-icon-warning: $u-warning !default;\n\t$u-icon-error: $u-error !default;\n\t$u-icon-label-line-height:1 !default;\n\n\t.u-icon {\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\talign-items: center;\n\n\t\t&--left {\n\t\t\tflex-direction: row-reverse;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--right {\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--top {\n\t\t\tflex-direction: column-reverse;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&--bottom {\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&__icon {\n\t\t\tfont-family: uicon-iconfont;\n\t\t\tposition: relative;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\n\t\t\t&--primary {\n\t\t\t\tcolor: $u-icon-primary;\n\t\t\t}\n\n\t\t\t&--success {\n\t\t\t\tcolor: $u-icon-success;\n\t\t\t}\n\n\t\t\t&--error {\n\t\t\t\tcolor: $u-icon-error;\n\t\t\t}\n\n\t\t\t&--warning {\n\t\t\t\tcolor: $u-icon-warning;\n\t\t\t}\n\n\t\t\t&--info {\n\t\t\t\tcolor: $u-icon-info;\n\t\t\t}\n\t\t}\n\n\t\t&__img {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\theight: auto;\n\t\t\twill-change: transform;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&__label {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tline-height: $u-icon-label-line-height;\n\t\t\t/* #endif */\n\t\t}\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/components/u-icon/u-icon.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "config", "addUnit", "icons", "addStyle"], "mappings": ";;;;;;;;AAiDC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA;AAAA,EAEN,OAAO;AACN,WAAO,CACP;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,6CAAK;AAAA,EAC9B,UAAU;AAAA,IACT,WAAW;AACV,UAAI,UAAU,CAAC;AACf,cAAQ,KAAK,KAAK,eAAe,MAAM,KAAK,IAAI;AAEhD,UAAI,KAAK,gBAAgB,SAAS;AACjC,gBAAQ,KAAK,YAAY;AAAA,aACnB;AAEN,gBAAQ,KAAK,KAAK,YAAY;AAAA,MAC/B;AAEA,UAAI,KAAK,SAASC,2CAAO,KAAK,SAAS,KAAK,KAAK;AAAG,gBAAQ,KAAK,mBAAmB,KAAK,KAAK;AAM9F,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,UAAI,QAAQ,CAAC;AACb,cAAQ;AAAA,QACP,UAAUC,qCAAAA,QAAQ,KAAK,IAAI;AAAA,QAC3B,YAAYA,qCAAAA,QAAQ,KAAK,IAAI;AAAA,QAC7B,YAAY,KAAK,OAAO,SAAS;AAAA;AAAA,QAEjC,KAAKA,qCAAAA,QAAQ,KAAK,GAAG;AAAA,MACtB;AACA,UAAI,KAAK,iBAAiB,SAAS;AAClC,cAAM,aAAa,KAAK;AAAA,MACzB;AAEA,UAAI,KAAK,SAAS,CAACD,oCAAAA,OAAO,KAAK,SAAS,KAAK,KAAK;AAAG,cAAM,QAAQ,KAAK;AAExE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,QAAQ;AACP,aAAO,KAAK,KAAK,QAAQ,GAAG,MAAM;AAAA,IAClC;AAAA,IACD,WAAW;AACV,UAAI,QAAQ,CAAC;AAEb,YAAM,QAAQ,KAAK,QAAQC,qCAAO,QAAC,KAAK,KAAK,IAAIA,qCAAAA,QAAQ,KAAK,IAAI;AAClE,YAAM,SAAS,KAAK,SAASA,qCAAO,QAAC,KAAK,MAAM,IAAIA,qCAAAA,QAAQ,KAAK,IAAI;AACrE,aAAO;AAAA,IACP;AAAA;AAAA,IAED,OAAO;AAEN,UAAI,KAAK,iBAAiB,SAAS;AAClC,eAAOD,oCAAAA,OAAO,YAAY,KAAK,IAAI,KAAK,KAAK;AAAA,MAC9C;AAEA,aAAOE,wCAAAA,MAAM,WAAW,KAAK,IAAI,KAAK,KAAK;AAAA,IAC5C;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,UAAAC,qCAAQ;AAAA,IACR,SAAAF,qCAAO;AAAA,IACP,aAAa,GAAG;AACf,WAAK,MAAM,SAAS,KAAK,OAAO,CAAC;AAEjC,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IACjC;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5HD,GAAG,gBAAgB,SAAS;"}