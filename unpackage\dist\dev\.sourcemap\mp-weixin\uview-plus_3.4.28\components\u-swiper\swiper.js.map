{"version": 3, "file": "swiper.js", "sources": ["uview-plus_3.4.28/components/u-swiper/swiper.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:21:38\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiper.js\n */\nexport default {\n    // swiper 组件\n    swiper: {\n        list: [],\n        indicator: false,\n        indicatorActiveColor: '#FFFFFF',\n        indicatorInactiveColor: 'rgba(255, 255, 255, 0.35)',\n        indicatorStyle: '',\n        indicatorMode: 'line',\n        autoplay: true,\n        current: 0,\n        currentItemId: '',\n        interval: 3000,\n        duration: 300,\n        circular: false,\n        previousMargin: 0,\n        nextMargin: 0,\n        acceleration: false,\n        displayMultipleItems: 1,\n        easingFunction: 'default',\n        keyName: 'url',\n        imgMode: 'aspectFill',\n        height: 130,\n        bgColor: '#f3f4f6',\n        radius: 4,\n        loading: false,\n        showTitle: false\n    }\n\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM,CAAE;AAAA,IACR,WAAW;AAAA,IACX,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IACxB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,UAAU;AAAA,IACV,SAAS;AAAA,IACT,eAAe;AAAA,IACf,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,IAChB,SAAS;AAAA,IACT,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,WAAW;AAAA,EACd;AAEL;;"}