{"version": 3, "file": "index.js", "sources": ["pages/menus/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVudXMvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"common-container bottom-layout\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar :title=\"parentMenuName\" :showBack=\"true\" :showHome=\"true\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 顶部标签页 -->\n\t\t<view class=\"tabs-container\">\n\t\t\t<u-tabs\n\t\t\t\tv-if=\"tabList && tabList.length > 0\"\n\t\t\t\t:list=\"tabList\"\n\t\t\t\tv-model:current=\"current\"\n\t\t\t\t@change=\"onTabChange\"\n\t\t\t\tlineColor=\"#D9001B\"\n\t\t\t\tlineWidth=\"30\"\n\t\t\t\titemStyle=\"padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;\"\n\t\t\t\t:activeStyle=\"{\n\t\t\t\t\tcolor: '#D9001B',\n\t\t\t\t\tfontWeight: 'bold',\n\t\t\t\t\tfontSize: '28rpx'\n\t\t\t\t}\"\n\t\t\t\t:inactiveStyle=\"{\n\t\t\t\t\tcolor: '#333333',\n\t\t\t\t\tfontSize: '28rpx'\n\t\t\t\t}\"\n\t\t\t></u-tabs>\n\t\t</view>\n\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-container\">\n\t\t\t<!-- 组织架构组件 -->\n\t\t\t<view v-if=\"showPartyComponent\" class=\"party-content\">\n\t\t\t\t<view class=\"content-section\">\n\t\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t\t<u-icon name=\"grid-fill\" color=\"#D9001B\" size=\"20\"></u-icon>\n\t\t\t\t\t\t<text>{{ currentTitle }}</text>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t\t<u-list :enable-flex=\"true\">\n\t\t\t\t\t\t\t<u-cell v-for=\"(item, index) in categoryList\" :key=\"index\"\n\t\t\t\t\t\t\t\t:title=\"item.name\"\n\t\t\t\t\t\t\t\t:titleStyle=\"{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}\"\n\t\t\t\t\t\t\t\tisLink\n\t\t\t\t\t\t\t\t@click=\"onCategoryClick(index)\">\n\t\t\t\t\t\t\t</u-cell>\n\t\t\t\t\t\t</u-list>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 底部操作栏 -->\n\t\t\t\t<view class=\"party-footer\" v-if=\"categoryHistory.length > 0\">\n\t\t\t\t\t<u-button type=\"primary\" text=\"返回上一级\" @click=\"goBackToParent\" :customStyle=\"{backgroundColor: '#D9001B'}\"></u-button>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 文章列表 -->\n\t\t\t<view v-else>\n\t\t\t\t<view v-if=\"articleList.length > 0\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"article-item\"\n\t\t\t\t\t\tv-for=\"(item, index) in articleList\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"goToArticleDetail(item)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"article-content\">\n\t\t\t\t\t\t\t<view class=\"article-title u-line-2\">{{ item.title }}</view>\n\t\t\t\t\t\t\t<view class=\"article-summary u-line-2\" v-if=\"item.content\">\n\t\t\t\t\t\t\t\t{{ getPlainTextSummary(item.content) }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"article-info\">\n\t\t\t\t\t\t\t\t<text class=\"article-date\">{{ formatDate(item.createdAt) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"article-image\" v-if=\"item.coverImage\">\n\t\t\t\t\t\t\t<image :src=\"item.coverImage\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view v-else class=\"empty-container\">\n\t\t\t\t\t<u-empty mode=\"data\" text=\"暂无数据\"></u-empty>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { menuApi, menuArticleApi, categoryApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\timport { fetchImageById, getFullImageUrl } from '@/utils/image.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tparentMenuId: null, // 父级菜单ID\n\t\t\t\tparentMenuName: '组织工作', // 父级菜单名称\n\t\t\t\ttabList: [], // 标签列表\n\t\t\t\tcurrent: 0, // 当前选中的标签索引\n\t\t\t\tarticleList: [], // 文章列表\n\t\t\t\tstatusBarHeight: 20, // 状态栏高度，默认值\n\t\t\t\tshowPartyComponent: false, // 是否显示组织架构组件\n\n\t\t\t\t// 组织架构相关数据\n\t\t\t\tcategoryList: [], // 基层党组织数据\n\t\t\t\tcategoryHistory: [], // 分类历史记录，用于返回上一级\n\t\t\t\tcurrentLevel: 1, // 当前分类层级\n\t\t\t\tcurrentTitle: '基层党组织' // 当前分类标题\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算顶部安全区域的高度\n\t\t\tsafeAreaTop() {\n\t\t\t\treturn this.statusBarHeight + 45; // 状态栏高度 + 导航栏高度(90rpx转为px的近似值)\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取状态栏高度\n\t\t\tthis.getStatusBarHeight();\n\n\t\t\t// 获取父级菜单ID\n\t\t\tif (options.id) {\n\t\t\t\tthis.parentMenuId = Number(options.id);\n\n\t\t\t\t// 获取子菜单ID（如果有）\n\t\t\t\tconst subMenuId = options.subMenuId ? Number(options.subMenuId) : null;\n\n\t\t\t\t// 加载子菜单，并传入子菜单ID\n\t\t\t\tthis.loadSubMenus(subMenuId);\n\t\t\t}\n\n\t\t\t// 如果有菜单名称，则设置\n\t\t\tif (options.name) {\n\t\t\t\tthis.parentMenuName = decodeURIComponent(options.name);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取状态栏高度\n\t\t\tgetStatusBarHeight() {\n\t\t\t\ttry {\n\t\t\t\t\t// 使用uni-app提供的API获取窗口信息\n\t\t\t\t\tconst windowInfo = uni.getWindowInfo();\n\t\t\t\t\tthis.statusBarHeight = windowInfo.statusBarHeight || 20;\n\n\t\t\t\t\tconsole.log('状态栏高度:', this.statusBarHeight);\n\t\t\t\t\t// 在小程序中不能直接设置CSS变量，我们使用计算属性来处理\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取状态栏高度失败:', e);\n\t\t\t\t\t// 设置默认值\n\t\t\t\t\tthis.statusBarHeight = 20;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一页方法已由导航栏组件自动处理\n\n\t\t\t// 加载子菜单\n\t\t\tasync loadSubMenus(subMenuId = null) {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取所有菜单\n\t\t\t\t\tconst result = await menuApi.getMenuList();\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\t// 过滤出指定父ID的二级菜单\n\t\t\t\t\t\tconst subMenus = result.data.filter(item => item.parentId === this.parentMenuId);\n\n\t\t\t\t\t\t// 转换为标签格式\n\t\t\t\t\t\tthis.tabList = subMenus.map(item => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tname: item.name,\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 如果有子菜单\n\t\t\t\t\t\tif (this.tabList.length > 0) {\n\t\t\t\t\t\t\t// 如果指定了子菜单ID，则查找对应的索引\n\t\t\t\t\t\t\tif (subMenuId) {\n\t\t\t\t\t\t\t\tconst index = this.tabList.findIndex(item => item.id === subMenuId);\n\t\t\t\t\t\t\t\tif (index !== -1) {\n\t\t\t\t\t\t\t\t\t// 找到了指定的子菜单，设置当前索引\n\t\t\t\t\t\t\t\t\tthis.current = index;\n\t\t\t\t\t\t\t\t\t// 加载指定子菜单的文章\n\t\t\t\t\t\t\t\t\tthis.loadArticles(subMenuId);\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// 如果没有指定子菜单ID或者找不到指定的子菜单，则默认加载第一个子菜单\n\t\t\t\t\t\t\tthis.current = 0;\n\t\t\t\t\t\t\t// 直接加载第一个子菜单的文章\n\t\t\t\t\t\t\tthis.loadArticles(this.tabList[0].id);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取子菜单列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取菜单失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 标签切换事件\n\t\t\tonTabChange(tabItem) {\n\t\t\t\t// 使用tabItem对象，它包含index和其他属性\n\t\t\t\tif (tabItem && tabItem.id) {\n\t\t\t\t\tthis.loadArticles(tabItem.id);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载文章列表\n\t\t\tasync loadArticles(menuId) {\n\t\t\t\t// 根据menuId找到对应的标签\n\t\t\t\tconst currentTab = this.tabList.find(tab => tab.id === menuId);\n\n\t\t\t\t// 如果是\"组织架构\"标签\n\t\t\t\tif (currentTab && currentTab.name === \"组织架构\") {\n\t\t\t\t\tthis.showPartyComponent = true;\n\t\t\t\t\t// 获取组织架构数据\n\t\t\t\t\tthis.fetchCategoryList();\n\t\t\t\t\treturn; // 不继续加载文章\n\t\t\t\t} else {\n\t\t\t\t\tthis.showPartyComponent = false;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\t// 使用新的API接口获取菜单文章\n\t\t\t\t\tconst result = await menuArticleApi.getMenuArticles(menuId);\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\t// 处理文章列表数据\n\t\t\t\t\t\tconst articles = result.data;\n\n\t\t\t\t\t\t// 只处理每篇文章的封面图\n\t\t\t\t\t\tfor (let article of articles) {\n\t\t\t\t\t\t\t// 处理封面图\n\t\t\t\t\t\t\tif (article.coverImageId && !article.coverImage) {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tconst imageData = await fetchImageById(article.coverImageId);\n\t\t\t\t\t\t\t\t\tif (imageData && imageData.url) {\n\t\t\t\t\t\t\t\t\t\tarticle.coverImage = getFullImageUrl(imageData.url);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\t\t\tconsole.error('获取文章封面图失败:', err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.articleList = articles;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.articleList = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取菜单文章列表失败:', error);\n\t\t\t\t\tthis.articleList = [];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取文章失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取纯文本摘要\n\t\t\tgetPlainTextSummary(htmlContent) {\n\t\t\t\t// 移除HTML标签\n\t\t\t\tconst plainText = htmlContent.replace(/<[^>]+>/g, '');\n\t\t\t\t// 移除多余空格和换行\n\t\t\t\tconst trimmedText = plainText.replace(/\\s+/g, ' ').trim();\n\t\t\t\t// 截取前100个字符作为摘要\n\t\t\t\treturn trimmedText.length > 100 ? trimmedText.substring(0, 100) + '...' : trimmedText;\n\t\t\t},\n\n\t\t\t// 格式化日期\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '';\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t},\n\n\t\t\t// 跳转到文章详情\n\t\t\tgoToArticleDetail(article) {\n\t\t\t\t// 跳转到统一的文章详情页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/article/detail?id=${article.id}&type=menu`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 以下是组织架构相关方法\n\t\t\t// 获取分类列表\n\t\t\tasync fetchCategoryList() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\tconst result = await categoryApi.getCategoryTreeList();\n\t\t\t\t\tconsole.log('获取分类树形列表成功:', result);\n\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\tthis.categoryList = result.data;\n\t\t\t\t\t\tconsole.log('分类树形列表数据已更新');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取分类树形列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取分类数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 分类点击事件\n\t\t\tonCategoryClick(index) {\n\t\t\t\tconst item = this.categoryList[index];\n\t\t\t\tconsole.log('点击了基层党组织:', item);\n\n\t\t\t\t// 判断是否有子分类\n\t\t\t\tif (item.children && item.children.length > 0) {\n\t\t\t\t\t// 保存当前分类列表到历史记录\n\t\t\t\t\tthis.categoryHistory.push({\n\t\t\t\t\t\tlist: [...this.categoryList],\n\t\t\t\t\t\ttitle: this.currentTitle,\n\t\t\t\t\t\tlevel: this.currentLevel\n\t\t\t\t\t});\n\n\t\t\t\t\t// 有子分类，更新当前列表为子分类\n\t\t\t\t\tthis.categoryList = item.children;\n\n\t\t\t\t\t// 更新当前标题和层级\n\t\t\t\t\tthis.currentTitle = item.name;\n\t\t\t\t\tthis.currentLevel++;\n\t\t\t\t} else {\n\t\t\t\t\t// 没有子分类，跳转到分类文章列表页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/party/category-articles/index?id=${item.id}`,\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一级分类\n\t\t\tgoBackToParent() {\n\t\t\t\tif (this.categoryHistory.length > 0) {\n\t\t\t\t\t// 从历史记录中获取上一级分类信息\n\t\t\t\t\tconst prevCategory = this.categoryHistory.pop();\n\n\t\t\t\t\t// 恢复上一级分类列表\n\t\t\t\t\tthis.categoryList = prevCategory.list;\n\t\t\t\t\tthis.currentTitle = prevCategory.title;\n\t\t\t\t\tthis.currentLevel = prevCategory.level;\n\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t.tabs-container {\n\t\tbackground-color: #FFF1F0;\n\t\tpadding: 15rpx 20rpx;\n\t\tposition: fixed;\n\t\ttop: 85px; /* 导航栏高度 + 额外空间，减少间距 */\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 998;\n\t\t// border-bottom: 1px solid #f0f0f0;\n\t}\n\n\t/* 使用全局样式文件中的.navbar-right */\n\n\t/* 组织架构相关样式 */\n\t.party-content {\n\t\tmargin-bottom: 30rpx;\n\t\tpadding: 20rpx;\n\n\t\t.content-section {\n\t\t\tbackground-color: #FFFFFF;\n\t\t\tborder-radius: 8rpx;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t.section-title {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding-bottom: 20rpx;\n\t\t\t\tborder-bottom: 1px solid #f0f0f0;\n\t\t\t\tmargin-bottom: 20rpx;\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.section-content {\n\t\t\t\tmin-height: 200rpx;\n\t\t\t}\n\t\t}\n\t}\n\n\t.party-footer {\n\t\tpadding: 20rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.content-container {\n\t\tflex: 1;\n\t\tpadding: 15rpx;\n\t\tmargin-top: 30px; /* 减少顶部间距，使布局更紧凑 */\n\n\t\t.article-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tbackground-color: #FFFFFF;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 15rpx;\n\t\t\tborder-radius: 4rpx;\n\n\t\t\t.article-content {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmin-height: 120rpx; /* 与图片高度一致 */\n\n\t\t\t\t.article-title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t}\n\n\t\t\t\t.article-summary {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666666;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t}\n\n\t\t\t\t.article-info {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-top: auto; /* 推到底部 */\n\n\t\t\t\t\t.article-date {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.article-image {\n\t\t\t\twidth: 180rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\tflex-shrink: 0;\n\t\t\t\talign-self: center; /* 垂直居中 */\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.empty-container {\n\t\t\tpadding: 100rpx 0;\n\t\t}\n\t}\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/menus/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "menuApi.getMenuList", "menuArticleApi.getMenuArticles", "fetchImageById", "getFullImageUrl", "categoryApi.getCategoryTreeList"], "mappings": ";;;;;;AA8FC,MAAK,eAAgB,MAAW;AAGhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA;AAAA,MACd,gBAAgB;AAAA;AAAA,MAChB,SAAS,CAAE;AAAA;AAAA,MACX,SAAS;AAAA;AAAA,MACT,aAAa,CAAE;AAAA;AAAA,MACf,iBAAiB;AAAA;AAAA,MACjB,oBAAoB;AAAA;AAAA;AAAA,MAGpB,cAAc,CAAE;AAAA;AAAA,MAChB,iBAAiB,CAAE;AAAA;AAAA,MACnB,cAAc;AAAA;AAAA,MACd,cAAc;AAAA;AAAA,IACf;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,aAAO,KAAK,kBAAkB;AAAA,IAC/B;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,SAAK,mBAAkB;AAGvB,QAAI,QAAQ,IAAI;AACf,WAAK,eAAe,OAAO,QAAQ,EAAE;AAGrC,YAAM,YAAY,QAAQ,YAAY,OAAO,QAAQ,SAAS,IAAI;AAGlE,WAAK,aAAa,SAAS;AAAA,IAC5B;AAGA,QAAI,QAAQ,MAAM;AACjB,WAAK,iBAAiB,mBAAmB,QAAQ,IAAI;AAAA,IACtD;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB;AACpB,UAAI;AAEH,cAAM,aAAaA,oBAAI;AACvB,aAAK,kBAAkB,WAAW,mBAAmB;AAErDA,sBAAA,MAAA,MAAA,OAAA,gCAAY,UAAU,KAAK,eAAe;AAAA,MAE3C,SAAS,GAAG;AACXA,2EAAc,cAAc,CAAC;AAE7B,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA;AAAA;AAAA,IAKD,MAAM,aAAa,YAAY,MAAM;AACpC,UAAI;AAEH,cAAM,SAAS,MAAMC,UAAAA;AACrB,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAE3D,gBAAM,WAAW,OAAO,KAAK,OAAO,UAAQ,KAAK,aAAa,KAAK,YAAY;AAG/E,eAAK,UAAU,SAAS,IAAI,UAAQ;AACnC,mBAAO;AAAA,cACN,MAAM,KAAK;AAAA,cACX,IAAI,KAAK;AAAA;UAEX,CAAC;AAGD,cAAI,KAAK,QAAQ,SAAS,GAAG;AAE5B,gBAAI,WAAW;AACd,oBAAM,QAAQ,KAAK,QAAQ,UAAU,UAAQ,KAAK,OAAO,SAAS;AAClE,kBAAI,UAAU,IAAI;AAEjB,qBAAK,UAAU;AAEf,qBAAK,aAAa,SAAS;AAC3B;AAAA,cACD;AAAA,YACD;AAGA,iBAAK,UAAU;AAEf,iBAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,EAAE;AAAA,UACrC;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,gCAAc,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,SAAS;AAEpB,UAAI,WAAW,QAAQ,IAAI;AAC1B,aAAK,aAAa,QAAQ,EAAE;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,aAAa,QAAQ;AAE1B,YAAM,aAAa,KAAK,QAAQ,KAAK,SAAO,IAAI,OAAO,MAAM;AAG7D,UAAI,cAAc,WAAW,SAAS,QAAQ;AAC7C,aAAK,qBAAqB;AAE1B,aAAK,kBAAiB;AACtB;AAAA,aACM;AACN,aAAK,qBAAqB;AAAA,MAC3B;AAEA,UAAI;AAEH,cAAM,SAAS,MAAME,iCAA+B,MAAM;AAC1D,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAE3D,gBAAM,WAAW,OAAO;AAGxB,mBAAS,WAAW,UAAU;AAE7B,gBAAI,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;AAChD,kBAAI;AACH,sBAAM,YAAY,MAAMC,YAAAA,eAAe,QAAQ,YAAY;AAC3D,oBAAI,aAAa,UAAU,KAAK;AAC/B,0BAAQ,aAAaC,YAAAA,gBAAgB,UAAU,GAAG;AAAA,gBACnD;AAAA,cACC,SAAO,KAAK;AACbJ,8BAAA,MAAA,MAAA,SAAA,gCAAc,cAAc,GAAG;AAAA,cAChC;AAAA,YACD;AAAA,UACD;AAEA,eAAK,cAAc;AAAA,eACb;AACN,eAAK,cAAc;QACpB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,eAAe,KAAK;AAClC,aAAK,cAAc;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB,aAAa;AAEhC,YAAM,YAAY,YAAY,QAAQ,YAAY,EAAE;AAEpD,YAAM,cAAc,UAAU,QAAQ,QAAQ,GAAG,EAAE;AAEnD,aAAO,YAAY,SAAS,MAAM,YAAY,UAAU,GAAG,GAAG,IAAI,QAAQ;AAAA,IAC1E;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,kBAAkB,SAAS;AAE1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,QAAQ,EAAE;AAAA,QAC3C,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA,IAID,MAAM,oBAAoB;AACzB,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMK,eAAAA;AACrBL,sBAAA,MAAA,MAAA,OAAA,gCAAY,eAAe,MAAM;AAEjC,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAC3D,eAAK,eAAe,OAAO;AAC3BA,wBAAAA,MAAY,MAAA,OAAA,gCAAA,aAAa;AAAA,QAC1B;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,OAAO;AACtB,YAAM,OAAO,KAAK,aAAa,KAAK;AACpCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,IAAI;AAG7B,UAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAE9C,aAAK,gBAAgB,KAAK;AAAA,UACzB,MAAM,CAAC,GAAG,KAAK,YAAY;AAAA,UAC3B,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,QACb,CAAC;AAGD,aAAK,eAAe,KAAK;AAGzB,aAAK,eAAe,KAAK;AACzB,aAAK;AAAA,aACC;AAENA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,2CAA2C,KAAK,EAAE;AAAA,UACvD,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,KAAK,gBAAgB,SAAS,GAAG;AAEpC,cAAM,eAAe,KAAK,gBAAgB,IAAG;AAG7C,aAAK,eAAe,aAAa;AACjC,aAAK,eAAe,aAAa;AACjC,aAAK,eAAe,aAAa;AAEjC,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACR;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3XD,GAAG,WAAW,eAAe;"}