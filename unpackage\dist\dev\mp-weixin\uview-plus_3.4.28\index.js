"use strict";
const common_vendor = require("../common/vendor.js");
const uviewPlus_3_4_28_libs_mixin_mixin = require("./libs/mixin/mixin.js");
const uviewPlus_3_4_28_libs_mixin_mpMixin = require("./libs/mixin/mpMixin.js");
const uviewPlus_3_4_28_libs_util_route = require("./libs/util/route.js");
const uviewPlus_3_4_28_libs_function_colorGradient = require("./libs/function/colorGradient.js");
const uviewPlus_3_4_28_libs_function_test = require("./libs/function/test.js");
const uviewPlus_3_4_28_libs_function_debounce = require("./libs/function/debounce.js");
const uviewPlus_3_4_28_libs_function_throttle = require("./libs/function/throttle.js");
const uviewPlus_3_4_28_libs_function_calc = require("./libs/function/calc.js");
const uviewPlus_3_4_28_libs_function_index = require("./libs/function/index.js");
const uviewPlus_3_4_28_libs_config_config = require("./libs/config/config.js");
const uviewPlus_3_4_28_libs_config_props = require("./libs/config/props.js");
const uviewPlus_3_4_28_libs_config_zIndex = require("./libs/config/zIndex.js");
const uviewPlus_3_4_28_libs_config_color = require("./libs/config/color.js");
const uviewPlus_3_4_28_libs_function_platform = require("./libs/function/platform.js");
const uviewPlus_3_4_28_libs_function_http = require("./libs/function/http.js");
let themeType = ["primary", "success", "error", "warning", "info"];
function setConfig(configs) {
  uviewPlus_3_4_28_libs_function_index.index.shallowMerge(uviewPlus_3_4_28_libs_config_config.config, configs.config || {});
  uviewPlus_3_4_28_libs_function_index.index.shallowMerge(uviewPlus_3_4_28_libs_config_props.props, configs.props || {});
  uviewPlus_3_4_28_libs_function_index.index.shallowMerge(uviewPlus_3_4_28_libs_config_color.color, configs.color || {});
  uviewPlus_3_4_28_libs_function_index.index.shallowMerge(uviewPlus_3_4_28_libs_config_zIndex.zIndex, configs.zIndex || {});
}
uviewPlus_3_4_28_libs_function_index.index.setConfig = setConfig;
const $u = {
  route: uviewPlus_3_4_28_libs_util_route.route,
  date: uviewPlus_3_4_28_libs_function_index.index.timeFormat,
  // 另名date
  colorGradient: uviewPlus_3_4_28_libs_function_colorGradient.colorGradient.colorGradient,
  hexToRgb: uviewPlus_3_4_28_libs_function_colorGradient.colorGradient.hexToRgb,
  rgbToHex: uviewPlus_3_4_28_libs_function_colorGradient.colorGradient.rgbToHex,
  colorToRgba: uviewPlus_3_4_28_libs_function_colorGradient.colorGradient.colorToRgba,
  test: uviewPlus_3_4_28_libs_function_test.test,
  type: themeType,
  http: uviewPlus_3_4_28_libs_function_http.http,
  config: uviewPlus_3_4_28_libs_config_config.config,
  // uview-plus配置信息相关，比如版本号
  zIndex: uviewPlus_3_4_28_libs_config_zIndex.zIndex,
  debounce: uviewPlus_3_4_28_libs_function_debounce.debounce,
  throttle: uviewPlus_3_4_28_libs_function_throttle.throttle,
  calc: uviewPlus_3_4_28_libs_function_calc.calc,
  mixin: uviewPlus_3_4_28_libs_mixin_mixin.mixin,
  mpMixin: uviewPlus_3_4_28_libs_mixin_mpMixin.mpMixin,
  props: uviewPlus_3_4_28_libs_config_props.props,
  ...uviewPlus_3_4_28_libs_function_index.index,
  color: uviewPlus_3_4_28_libs_config_color.color,
  platform: uviewPlus_3_4_28_libs_function_platform.platform
};
const install = (Vue, upuiParams = "") => {
  if (upuiParams) {
    common_vendor.index.upuiParams = upuiParams;
    let temp = upuiParams();
    if (temp.httpIns) {
      temp.httpIns(uviewPlus_3_4_28_libs_function_http.http);
    }
    if (temp.options) {
      setConfig(temp.options);
    }
  }
  common_vendor.index.$u = $u;
  Vue.config.globalProperties.$u = $u;
  Vue.mixin(uviewPlus_3_4_28_libs_mixin_mixin.mixin);
};
const uviewPlus = {
  install
};
exports.uviewPlus = uviewPlus;
//# sourceMappingURL=../../.sourcemap/mp-weixin/uview-plus_3.4.28/index.js.map
