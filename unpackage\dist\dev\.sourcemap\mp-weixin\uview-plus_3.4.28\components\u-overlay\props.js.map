{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-overlay/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否显示遮罩\n        show: {\n            type: Boolean,\n            default: () => defProps.overlay.show\n        },\n        // 层级z-index\n        zIndex: {\n            type: [String, Number],\n            default: () => defProps.overlay.zIndex\n        },\n        // 遮罩的过渡时间，单位为ms\n        duration: {\n            type: [String, Number],\n            default: () => defProps.overlay.duration\n        },\n        // 不透明度值，当做rgba的第四个参数\n        opacity: {\n            type: [String, Number],\n            default: () => defProps.overlay.opacity\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,QAAQ;AAAA,IACnC;AAAA,EACJ;AACL,CAAC;;"}