{"version": 3, "file": "columnNotice.js", "sources": ["uview-plus_3.4.28/components/u-column-notice/columnNotice.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:16\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/columnNotice.js\n */\nexport default {\n    // columnNotice 组件\n    columnNotice: {\n        text: '',\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        fontSize: 14,\n        speed: 80,\n        step: false,\n        duration: 1500,\n        disableTouch: true,\n\t\tjustifyContent: 'flex-start'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,eAAA;AAAA;AAAA,EAEX,cAAc;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,cAAc;AAAA,IACpB,gBAAgB;AAAA,EACb;AACL;;"}