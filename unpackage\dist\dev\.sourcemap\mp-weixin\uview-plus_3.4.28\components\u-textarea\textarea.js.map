{"version": 3, "file": "textarea.js", "sources": ["uview-plus_3.4.28/components/u-textarea/textarea.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:24:32\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/textarea.js\n */\nexport default {\n\t// textarea 组件\n\ttextarea: {\n\t\tvalue: '',\n\t\tplaceholder: '',\n\t\tplaceholderClass: 'textarea-placeholder',\n\t\tplaceholderStyle: 'color: #c0c4cc',\n\t\theight: 70,\n\t\tconfirmType: 'done',\n\t\tdisabled: false,\n\t\tcount: false,\n\t\tfocus: false,\n\t\tautoHeight: false,\n\t\tfixed: false,\n\t\tcursorSpacing: 0,\n\t\tcursor: '',\n\t\tshowConfirmBar: true,\n\t\tselectionStart: -1,\n\t\tselectionEnd: -1,\n\t\tadjustPosition: true,\n\t\tdisableDefaultPadding: false,\n\t\tholdKeyboard: false,\n\t\tmaxlength: 140,\n\t\tborder: 'surround',\n\t\tformatter: null\n\t}\n}\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEd,UAAU;AAAA,IACT,OAAO;AAAA,IACP,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,eAAe;AAAA,IACf,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,IACvB,cAAc;AAAA,IACd,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,EACX;AACF;;"}