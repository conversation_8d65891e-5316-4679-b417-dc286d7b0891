"use strict";
const uviewPlus_3_4_28_components_uLine_props = require("./props.js");
const uviewPlus_3_4_28_libs_mixin_mpMixin = require("../../libs/mixin/mpMixin.js");
const uviewPlus_3_4_28_libs_mixin_mixin = require("../../libs/mixin/mixin.js");
const uviewPlus_3_4_28_libs_function_index = require("../../libs/function/index.js");
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "u-line",
  mixins: [uviewPlus_3_4_28_libs_mixin_mpMixin.mpMixin, uviewPlus_3_4_28_libs_mixin_mixin.mixin, uviewPlus_3_4_28_components_uLine_props.props],
  computed: {
    lineStyle() {
      const style = {};
      style.margin = this.margin;
      if (this.direction === "row") {
        style.borderBottomWidth = "1px";
        style.borderBottomStyle = this.dashed ? "dashed" : "solid";
        style.width = uviewPlus_3_4_28_libs_function_index.addUnit(this.length);
        if (this.hairline)
          style.transform = "scaleY(0.5)";
      } else {
        style.borderLeftWidth = "1px";
        style.borderLeftStyle = this.dashed ? "dashed" : "solid";
        style.height = uviewPlus_3_4_28_libs_function_index.addUnit(this.length);
        if (this.hairline)
          style.transform = "scaleX(0.5)";
      }
      style.borderColor = this.color;
      return uviewPlus_3_4_28_libs_function_index.deepMerge(style, uviewPlus_3_4_28_libs_function_index.addStyle(this.customStyle));
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.s($options.lineStyle)
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-15e992c5"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-line/u-line.js.map
