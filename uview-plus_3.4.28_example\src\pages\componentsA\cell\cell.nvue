<template>
	<view class="cell-page">
		<view class="u-page__item">
			<text class="u-page__item__title">基础功能</text>
			<up-cell-group>
				<up-cell
				    title="uview-plus"
				    value="内容"
				    :isLink="true"
				></up-cell>
				<up-cell
				    title="利剑出鞘,一统江湖"
				    value="内容"
				    label="挣脱束缚,向往自由"
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">自定义图标/图片</text>
			<up-cell-group>
				<up-cell
				    title="单元格"
				    icon="lock-fill"
				></up-cell>
				<up-cell
				    title="单元格"
				    icon="https://cdn.uviewui.com/uview/example/tag.png"
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">自定义大小</text>
			<up-cell-group>
				<up-cell
				    size="large"
				    title="单元格"
				    value="内容"
					isLink
				></up-cell>
				<up-cell
				    size="large"
				    title="单元格"
				    value="内容"
				    label="描述信息"
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">显示右箭头</text>
			<up-cell-group>
				<up-cell
					required
				    title="单元格"
				    value="组件"
				    isLink
				></up-cell>
				<up-cell
				    title="单元格"
				    value="工具"
				    arrow-direction="up"
				    isLink
				></up-cell>
				<up-cell
				    title="单元格"
				    value="模板"
				    arrow-direction="down"
				    isLink
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">跳转页面</text>
			<up-cell-group>
				<up-cell
				    title="打开标签页"
				    isLink
				    url="/pages/componentsB/tag/tag"
				></up-cell>
				<up-cell
				    title="打开徽标页"
				    isLink
				    url="/pages/componentsB/badge/badge"
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">右侧内容垂直居中</text>
			<up-cell-group>
				<up-cell
					required
				    title="单元格"
				    value="内容"
				    label="描述信息"
				    center
				></up-cell>
			</up-cell-group>
		</view>
		<view class="u-page__item">
			<text class="u-page__item__title">自定义插槽</text>
			<up-cell-group>
				<up-cell value="内容">
                    <template #title>
                        <view
					        class="u-slot-title"
					    >
                            <text class="u-cell-text">单元格</text>
                            <up-tag
                                text="标签"
                                plain
                                size="mini"
                                type="warning"
                            >
                            </up-tag>
                        </view>
                    </template>
				</up-cell>
				<up-cell
				    title="单元格"
					isLink
				>
					<template #value>
						<text
						    class="u-slot-value"
						>99</text>
					</template>
					<template #right-icon>
						1
					</template>
				</up-cell>
			</up-cell-group>
		</view>
		<up-gap height="30"></up-gap>
	</view>
</template>
<script>
	export default {
		data() {
			return {}
		},
		methods: {
			click() {
				console.log('Cell is clicked.');
			}
		}
	}
</script>

<style lang="scss">
	.cell-page {
		padding-bottom: 20px;
		background-color: #f3f4f6;
	}

	.cell-box {
		&__title {
			font-size: 14px;
			color: rgb(143, 156, 162);
			margin: 20px 0px 0px 15px;
		}

		&__block {
			// background-color: #fff;
			margin-top: 10px;
		}
	}

	.u-page {
		padding: 0;
		&__item {
			margin-bottom: 10px;

			&__title {
				color: $u-tips-color;
				padding: 10px 15px;
				margin-bottom: 0px;
				font-size: 15px;

				&__slot-title {
					color: $u-primary;
					font-size: 14px;
				}
			}

			:deep(.u-cell-group) {
				background-color: #fff;
			}
		}
	}

	.u-slot-title {
		@include flex;
		flex-direction: row;
		align-items: center;
	}

	.u-cell-text {
		font-size: 15px;
		line-height: 22px;
		color: #303133;
		margin-right: 5px;
	}

	.u-slot-value {
		line-height: 17px;
		text-align: center;
		font-size: 10px;
		padding: 0 5px;
		height: 17px;
		color: #FFFFFF;
		border-radius: 100px;
		background-color: #f56c6c;		/* #ifndef APP-NVUE */		margin-left: auto;		/* #endif */
	}
</style>
