{"version": 3, "file": "loadingIcon.js", "sources": ["uview-plus_3.4.28/components/u-loading-icon/loadingIcon.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:45:47\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/loadingIcon.js\n */\nimport config from '../../libs/config/config'\n\nconst {\n    color\n} = config\nexport default {\n    // loading-icon加载中图标组件\n    loadingIcon: {\n        show: true,\n        color: color['u-tips-color'],\n        textColor: color['u-tips-color'],\n        vertical: false,\n        mode: 'spinner',\n        size: 24,\n        textSize: 15,\n        text: '',\n        timingFunction: 'ease-in-out',\n        duration: 1200,\n        inactiveColor: ''\n    }\n}\n"], "names": ["config"], "mappings": ";;AAWA,MAAM;AAAA,EACF;AACJ,IAAIA,oCAAM;AACV,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM;AAAA,IACN,OAAO,MAAM,cAAc;AAAA,IAC3B,WAAW,MAAM,cAAc;AAAA,IAC/B,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,gBAAgB;AAAA,IAChB,UAAU;AAAA,IACV,eAAe;AAAA,EAClB;AACL;;"}