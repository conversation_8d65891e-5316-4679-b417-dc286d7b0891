"use strict";
const uviewPlus_3_4_28_components_uIcon_icons = require("../../uview-plus_3.4.28/components/u-icon/icons.js");
const uviewPlus_3_4_28_components_uIcon_props = require("../../uview-plus_3.4.28/components/u-icon/props.js");
const uviewPlus_3_4_28_libs_config_config = require("../../uview-plus_3.4.28/libs/config/config.js");
const uviewPlus_3_4_28_libs_mixin_mpMixin = require("../../uview-plus_3.4.28/libs/mixin/mpMixin.js");
const uviewPlus_3_4_28_libs_mixin_mixin = require("../../uview-plus_3.4.28/libs/mixin/mixin.js");
const uviewPlus_3_4_28_libs_function_index = require("../../uview-plus_3.4.28/libs/function/index.js");
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "u-icon",
  // 移除 beforeCreate 钩子，避免覆盖原组件的行为
  data() {
    return {};
  },
  emits: ["click"],
  mixins: [uviewPlus_3_4_28_libs_mixin_mpMixin.mpMixin, uviewPlus_3_4_28_libs_mixin_mixin.mixin, uviewPlus_3_4_28_components_uIcon_props.props],
  computed: {
    uClasses() {
      let classes = [];
      classes.push(this.customPrefix + "-" + this.name);
      if (this.customPrefix == "uicon") {
        classes.push("u-iconfont");
      } else {
        classes.push(this.customPrefix);
      }
      if (this.color && uviewPlus_3_4_28_libs_config_config.config.type.includes(this.color))
        classes.push("u-icon__icon--" + this.color);
      return classes;
    },
    iconStyle() {
      let style = {};
      style = {
        fontSize: uviewPlus_3_4_28_libs_function_index.addUnit(this.size),
        lineHeight: uviewPlus_3_4_28_libs_function_index.addUnit(this.size),
        fontWeight: this.bold ? "bold" : "normal",
        // 某些特殊情况需要设置一个到顶部的距离，才能更好的垂直居中
        top: uviewPlus_3_4_28_libs_function_index.addUnit(this.top)
      };
      if (this.customPrefix !== "uicon") {
        style.fontFamily = this.customPrefix;
      }
      if (this.color && !uviewPlus_3_4_28_libs_config_config.config.type.includes(this.color))
        style.color = this.color;
      return style;
    },
    // 判断传入的name属性，是否图片路径，只要带有"/"均认为是图片形式
    isImg() {
      return this.name.indexOf("/") !== -1;
    },
    imgStyle() {
      let style = {};
      style.width = this.width ? uviewPlus_3_4_28_libs_function_index.addUnit(this.width) : uviewPlus_3_4_28_libs_function_index.addUnit(this.size);
      style.height = this.height ? uviewPlus_3_4_28_libs_function_index.addUnit(this.height) : uviewPlus_3_4_28_libs_function_index.addUnit(this.size);
      return style;
    },
    // 通过图标名，查找对应的图标
    icon() {
      if (this.customPrefix !== "uicon") {
        return uviewPlus_3_4_28_libs_config_config.config.customIcons[this.name] || this.name;
      }
      return uviewPlus_3_4_28_components_uIcon_icons.icons["uicon-" + this.name] || this.name;
    }
  },
  methods: {
    addStyle: uviewPlus_3_4_28_libs_function_index.addStyle,
    addUnit: uviewPlus_3_4_28_libs_function_index.addUnit,
    clickHandler(e) {
      this.$emit("click", this.index, e);
      this.stop && this.preventEvent(e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $options.isImg
  }, $options.isImg ? {
    b: _ctx.name,
    c: _ctx.imgMode,
    d: common_vendor.s($options.imgStyle),
    e: common_vendor.s($options.addStyle(_ctx.customStyle))
  } : {
    f: common_vendor.t($options.icon),
    g: common_vendor.n($options.uClasses),
    h: common_vendor.s($options.iconStyle),
    i: common_vendor.s($options.addStyle(_ctx.customStyle)),
    j: _ctx.hoverClass
  }, {
    k: _ctx.label !== ""
  }, _ctx.label !== "" ? {
    l: common_vendor.t(_ctx.label),
    m: _ctx.labelColor,
    n: $options.addUnit(_ctx.labelSize),
    o: _ctx.labelPos == "right" ? $options.addUnit(_ctx.space) : 0,
    p: _ctx.labelPos == "bottom" ? $options.addUnit(_ctx.space) : 0,
    q: _ctx.labelPos == "left" ? $options.addUnit(_ctx.space) : 0,
    r: _ctx.labelPos == "top" ? $options.addUnit(_ctx.space) : 0
  } : {}, {
    s: common_vendor.o((...args) => $options.clickHandler && $options.clickHandler(...args)),
    t: common_vendor.n("u-icon--" + _ctx.labelPos)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e5df0531"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/u-icon/u-icon.js.map
