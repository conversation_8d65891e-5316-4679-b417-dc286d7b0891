{"version": 3, "file": "map.js", "sources": ["pages/article/map.js"], "sourcesContent": ["/**\n * 文章类型与API函数映射关系\n * 简单的JSON映射，只包含文章类型与API的对应关系\n */\nimport { articleApi, menuArticleApi, partyArticleApi } from '@/api/index.js';\n\n/**\n * 文章类型与API的映射\n * @type {Object}\n */\nexport const articleApiMap = {\n  // 新闻文章\n  news: articleApi.getArticleDetail,\n\n  // 菜单文章\n  menu: menuArticleApi.getArticleDetail,\n\n  // 党建文章\n  party: partyArticleApi.getPartyArticleDetail\n};\n\n/**\n * 获取文章类型对应的API函数\n * @param {string} type - 文章类型\n * @returns {Function} - 对应的API函数\n */\nexport function getArticleApi(type) {\n  return articleApiMap[type] || articleApiMap.news;\n}\n"], "names": ["articleApi.getArticleDetail", "menuArticleApi.getArticleDetail", "partyArticleApi.getPartyArticleDetail"], "mappings": ";;;;;AAUO,MAAM,gBAAgB;AAAA;AAAA,EAE3B,MAAMA,SAA2B;AAAA;AAAA,EAGjC,MAAMC,iBAA+B;AAAA;AAAA,EAGrC,OAAOC,kBAAqC;AAC9C;AAOO,SAAS,cAAc,MAAM;AAClC,SAAO,cAAc,IAAI,KAAK,cAAc;AAC9C;;"}