"use strict";const e=require("../common/vendor.js"),t=require("../utils/config.js"),r=r=>new Promise(((s,o)=>{const a={url:t.config.apiBaseUrl+r.url,data:r.data||r.params,method:r.method||"GET",header:{"content-type":"application/json;charset=utf-8",...r.header},timeout:1e4,dataType:"json",success:e=>{e.statusCode>=200&&e.statusCode<300?s(e.data):(console.error("请求异常:",e),o(e))},fail:e=>{console.error("请求失败:",e),o(e)}};e.index.request(a)})),s={request:r,get:(e,t={},s={})=>r({url:e,params:t,method:"GET",header:s}),post:(e,t={},s={})=>r({url:e,data:t,method:"POST",header:s})};exports.request=s;
