<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基本案列</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon></up-loading-icon>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">半圆loading</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon mode="semicircle"></up-loading-icon>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">圆形loading</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon mode="circle"></up-loading-icon>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义动画</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon mode="circle" timingFunction="linear"></up-loading-icon>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义颜色</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon color="#19be6b"></up-loading-icon>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义文字</text>
			<view class="u-demo-block__content">
				<view class="u-page__loading-item">
					<up-loading-icon
					    :vertical="true"
					    text="加载中"
					></up-loading-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		&__loading-item {
			margin-top: 5px;
		}
	}

	.u-demo-block__content {
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}
</style>
