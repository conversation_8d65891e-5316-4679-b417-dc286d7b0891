<template>
	<view class="u-page">
		<up-list
			@scrolltolower="scrolltolower"
		>
			<up-list-item
				v-for="(item, index) in indexList"
				:key="index"
			>
				<up-cell
					:title="`列表长度-${index + 1}`"
				>
					<template #icon>
						<up-avatar
							shape="square"
							size="35"
							:src="item.url"
							customStyle="margin: -3px 5px -3px 0"
						></up-avatar>
					</template>
				</up-cell>
			</up-list-item>
		</up-list>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				indexList: [],
				urls: [
					'https://uiadmin.net/uview-plus/album/1.jpg',
					'https://uiadmin.net/uview-plus/album/2.jpg',
					'https://uiadmin.net/uview-plus/album/3.jpg',
					'https://uiadmin.net/uview-plus/album/4.jpg',
					'https://uiadmin.net/uview-plus/album/5.jpg',
					'https://uiadmin.net/uview-plus/album/6.jpg',
					'https://uiadmin.net/uview-plus/album/7.jpg',
					'https://uiadmin.net/uview-plus/album/8.jpg',
					'https://uiadmin.net/uview-plus/album/9.jpg',
					'https://uiadmin.net/uview-plus/album/10.jpg',
				]
			}
		},
		onLoad() {
			this.loadmore()
		},
		methods: {
			scrolltolower() {
				this.loadmore()
			},
			loadmore() {
				for (let i = 0; i < 30; i++) {
					this.indexList.push({
						url: this.urls[uni.$u.random(0, this.urls.length - 1)]
					})
				}
			}
		},
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}
</style>
