<view class="u-notice data-v-c431354b" bindtap="{{p}}"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><view wx:if="{{a}}" class="u-notice__left-icon data-v-c431354b"><u-icon wx:if="{{b}}" class="data-v-c431354b" u-i="c431354b-0" bind:__l="__l" u-p="{{b}}"></u-icon></view></block><swiper disable-touch="{{f}}" vertical="{{g}}" circular interval="{{h}}" autoplay="{{true}}" class="u-notice__swiper data-v-c431354b" bindchange="{{i}}"><swiper-item wx:for="{{c}}" wx:for-item="item" wx:key="b" class="u-notice__swiper__item data-v-c431354b" style="{{'justify-content:' + e}}"><text class="u-notice__swiper__item__text u-line-1 data-v-c431354b" style="{{d}}">{{item.a}}</text></swiper-item></swiper><view wx:if="{{j}}" class="u-notice__right-icon data-v-c431354b"><u-icon wx:if="{{k}}" class="data-v-c431354b" u-i="c431354b-1" bind:__l="__l" u-p="{{l}}"></u-icon><u-icon wx:if="{{m}}" class="data-v-c431354b" bindclick="{{n}}" u-i="c431354b-2" bind:__l="__l" u-p="{{o}}"></u-icon></view></view>