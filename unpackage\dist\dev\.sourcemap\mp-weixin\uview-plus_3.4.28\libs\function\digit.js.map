{"version": 3, "file": "digit.js", "sources": ["uview-plus_3.4.28/libs/function/digit.js"], "sourcesContent": ["let _boundaryCheckingState = true; // 是否进行越界检查的全局开关\n\n/**\n * 把错误的数据转正\n * @private\n * @example strip(0.09999999999999998)=0.1\n */\nexport function strip(num, precision = 15) {\n  return +parseFloat(Number(num).toPrecision(precision));\n}\n\n/**\n * Return digits length of a number\n * @private\n * @param {*number} num Input number\n */\nexport function digitLength(num) {\n  // Get digit length of e\n  const eSplit = num.toString().split(/[eE]/);\n  const len = (eSplit[0].split('.')[1] || '').length - +(eSplit[1] || 0);\n  return len > 0 ? len : 0;\n}\n\n/**\n * 把小数转成整数,如果是小数则放大成整数\n * @private\n * @param {*number} num 输入数\n */\nexport function float2Fixed(num) {\n  if (num.toString().indexOf('e') === -1) {\n    return Number(num.toString().replace('.', ''));\n  }\n  const dLen = digitLength(num);\n  return dLen > 0 ? strip(Number(num) * Math.pow(10, dLen)) : Number(num);\n}\n\n/**\n * 检测数字是否越界，如果越界给出提示\n * @private\n * @param {*number} num 输入数\n */\nexport function checkBoundary(num) {\n  if (_boundaryCheckingState) {\n    if (num > Number.MAX_SAFE_INTEGER || num < Number.MIN_SAFE_INTEGER) {\n      console.warn(`${num} 超出了精度限制，结果可能不正确`);\n    }\n  }\n}\n\n/**\n * 把递归操作扁平迭代化\n * @param {number[]} arr 要操作的数字数组\n * @param {function} operation 迭代操作\n * @private\n */\nexport function iteratorOperation(arr, operation) {\n  const [num1, num2, ...others] = arr;\n  let res = operation(num1, num2);\n\n  others.forEach((num) => {\n    res = operation(res, num);\n  });\n\n  return res;\n}\n\n/**\n * 高精度乘法\n * @export\n */\nexport function times(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, times);\n  }\n\n  const [num1, num2] = nums;\n  const num1Changed = float2Fixed(num1);\n  const num2Changed = float2Fixed(num2);\n  const baseNum = digitLength(num1) + digitLength(num2);\n  const leftValue = num1Changed * num2Changed;\n\n  checkBoundary(leftValue);\n\n  return leftValue / Math.pow(10, baseNum);\n}\n\n/**\n * 高精度加法\n * @export\n */\nexport function plus(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, plus);\n  }\n\n  const [num1, num2] = nums;\n  // 取最大的小数位\n  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\n  // 把小数都转为整数然后再计算\n  return (times(num1, baseNum) + times(num2, baseNum)) / baseNum;\n}\n\n/**\n * 高精度减法\n * @export\n */\nexport function minus(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, minus);\n  }\n\n  const [num1, num2] = nums;\n  const baseNum = Math.pow(10, Math.max(digitLength(num1), digitLength(num2)));\n  return (times(num1, baseNum) - times(num2, baseNum)) / baseNum;\n}\n\n/**\n * 高精度除法\n * @export\n */\nexport function divide(...nums) {\n  if (nums.length > 2) {\n    return iteratorOperation(nums, divide);\n  }\n\n  const [num1, num2] = nums;\n  const num1Changed = float2Fixed(num1);\n  const num2Changed = float2Fixed(num2);\n  checkBoundary(num1Changed);\n  checkBoundary(num2Changed);\n  // 重要，这里必须用strip进行修正\n  return times(num1Changed / num2Changed, strip(Math.pow(10, digitLength(num2) - digitLength(num1))));\n}\n\n/**\n * 四舍五入\n * @export\n */\nexport function round(num, ratio) {\n  const base = Math.pow(10, ratio);\n  let result = divide(Math.round(Math.abs(times(num, base))), base);\n  if (num < 0 && result !== 0) {\n    result = times(result, -1);\n  }\n  // 位数不足则补0\n  return result;\n}\n\n/**\n * 是否进行边界检查，默认开启\n * @param flag 标记开关，true 为开启，false 为关闭，默认为 true\n * @export\n */\nexport function enableBoundaryChecking(flag = true) {\n  _boundaryCheckingState = flag;\n}\n\n\nexport default {\n  times,\n  plus,\n  minus,\n  divide,\n  round,\n  enableBoundaryChecking,\n};\n\n"], "names": ["uni"], "mappings": ";;AAOO,SAAS,MAAM,KAAK,YAAY,IAAI;AACzC,SAAO,CAAC,WAAW,OAAO,GAAG,EAAE,YAAY,SAAS,CAAC;AACvD;AAOO,SAAS,YAAY,KAAK;AAE/B,QAAM,SAAS,IAAI,SAAU,EAAC,MAAM,MAAM;AAC1C,QAAM,OAAO,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,IAAI,SAAS,EAAE,OAAO,CAAC,KAAK;AACpE,SAAO,MAAM,IAAI,MAAM;AACzB;AAOO,SAAS,YAAY,KAAK;AAC/B,MAAI,IAAI,SAAU,EAAC,QAAQ,GAAG,MAAM,IAAI;AACtC,WAAO,OAAO,IAAI,SAAQ,EAAG,QAAQ,KAAK,EAAE,CAAC;AAAA,EAC9C;AACD,QAAM,OAAO,YAAY,GAAG;AAC5B,SAAO,OAAO,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG;AACxE;AAOO,SAAS,cAAc,KAAK;AACL;AAC1B,QAAI,MAAM,OAAO,oBAAoB,MAAM,OAAO,kBAAkB;AAClEA,oBAAA,MAAA,MAAA,QAAA,kDAAa,GAAG,GAAG,kBAAkB;AAAA,IACtC;AAAA,EACF;AACH;AAQO,SAAS,kBAAkB,KAAK,WAAW;AAChD,QAAM,CAAC,MAAM,MAAM,GAAG,MAAM,IAAI;AAChC,MAAI,MAAM,UAAU,MAAM,IAAI;AAE9B,SAAO,QAAQ,CAAC,QAAQ;AACtB,UAAM,UAAU,KAAK,GAAG;AAAA,EAC5B,CAAG;AAED,SAAO;AACT;AAMO,SAAS,SAAS,MAAM;AAC7B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,KAAK;AAAA,EACrC;AAED,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,UAAU,YAAY,IAAI,IAAI,YAAY,IAAI;AACpD,QAAM,YAAY,cAAc;AAEhC,gBAAc,SAAS;AAEvB,SAAO,YAAY,KAAK,IAAI,IAAI,OAAO;AACzC;AAoCO,SAAS,UAAU,MAAM;AAC9B,MAAI,KAAK,SAAS,GAAG;AACnB,WAAO,kBAAkB,MAAM,MAAM;AAAA,EACtC;AAED,QAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAM,cAAc,YAAY,IAAI;AACpC,QAAM,cAAc,YAAY,IAAI;AACpC,gBAAc,WAAW;AACzB,gBAAc,WAAW;AAEzB,SAAO,MAAM,cAAc,aAAa,MAAM,KAAK,IAAI,IAAI,YAAY,IAAI,IAAI,YAAY,IAAI,CAAC,CAAC,CAAC;AACpG;AAMO,SAAS,MAAM,KAAK,OAAO;AAChC,QAAM,OAAO,KAAK,IAAI,IAAI,KAAK;AAC/B,MAAI,SAAS,OAAO,KAAK,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI;AAChE,MAAI,MAAM,KAAK,WAAW,GAAG;AAC3B,aAAS,MAAM,QAAQ,EAAE;AAAA,EAC1B;AAED,SAAO;AACT;;"}