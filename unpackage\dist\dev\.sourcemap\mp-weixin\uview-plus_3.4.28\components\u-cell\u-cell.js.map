{"version": 3, "file": "u-cell.js", "sources": ["uview-plus_3.4.28/components/u-cell/u-cell.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtY2VsbC91LWNlbGwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"u-cell\" :class=\"[customClass]\" :style=\"[addStyle(customStyle)]\"\n\t\t:hover-class=\"(!disabled && (clickable || isLink)) ? 'u-cell--clickable' : ''\" :hover-stay-time=\"250\"\n\t\t@tap=\"clickHandler\">\n\t\t<view class=\"u-cell__body\" :class=\"[ center && 'u-cell--center', size === 'large' && 'u-cell__body--large']\">\n\t\t\t<view class=\"u-cell__body__content\">\n\t\t\t\t<view class=\"u-cell__left-icon-wrap\" v-if=\"$slots.icon || icon\">\n\t\t\t\t\t<slot name=\"icon\" v-if=\"$slots.icon\">\n\t\t\t\t\t</slot>\n\t\t\t\t\t<u-icon v-else :name=\"icon\"\n\t\t\t\t\t\t:custom-style=\"iconStyle\"\n\t\t\t\t\t\t:size=\"size === 'large' ? 22 : 18\"></u-icon>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"u-cell__title\">\n                    <!-- 将slot与默认内容用if/else分开主要是因为微信小程序不支持slot嵌套传递，这样才能解决collapse组件的slot不失效问题，label暂时未用到。 -->\n\t\t\t\t\t<slot name=\"title\" v-if=\"$slots.title || !title\">\n\t\t\t\t\t</slot>\n                    <text v-else class=\"u-cell__title-text\" :style=\"[titleTextStyle]\"\n                        :class=\"[required && 'u-cell--required', disabled && 'u-cell--disabled', size === 'large' && 'u-cell__title-text--large']\">{{ title }}</text>\n\t\t\t\t\t<slot name=\"label\">\n\t\t\t\t\t\t<text class=\"u-cell__label\" v-if=\"label\"\n\t\t\t\t\t\t\t:class=\"[disabled && 'u-cell--disabled', size === 'large' && 'u-cell__label--large']\">{{ label }}</text>\n\t\t\t\t\t</slot>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<slot name=\"value\">\n\t\t\t\t<text class=\"u-cell__value\"\n\t\t\t\t\t:class=\"[disabled && 'u-cell--disabled', size === 'large' && 'u-cell__value--large']\"\n\t\t\t\t\tv-if=\"!testEmpty(value)\">{{ value }}</text>\n\t\t\t</slot>\n\t\t\t<view class=\"u-cell__right-icon-wrap\" v-if=\"$slots['right-icon'] || isLink\"\n\t\t\t\t:class=\"[`u-cell__right-icon-wrap--${arrowDirection}`]\">\n\t\t\t\t<u-icon v-if=\"rightIcon && !$slots['right-icon']\" :name=\"rightIcon\"\n\t\t\t\t\t:custom-style=\"rightIconStyle\" :color=\"disabled ? '#c8c9cc' : 'info'\"\n\t\t\t\t\t:size=\"size === 'large' ? 18 : 16\"></u-icon>\n\t\t\t\t<slot v-else name=\"right-icon\">\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t\t<view class=\"u-cell__right-icon-wrap\" v-if=\"$slots['righticon']\"\n\t\t\t\t:class=\"[`u-cell__right-icon-wrap--${arrowDirection}`]\">\n\t\t\t\t<slot name=\"righticon\">\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-line v-if=\"border\"></u-line>\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle } from '../../libs/function/index';\n\timport test from '../../libs/function/test';\n\t/**\n\t * cell  单元格\n\t * @description cell单元格一般用于一组列表的情况，比如个人中心页，设置页等。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/cell.html\n\t * @property {String | Number}\ttitle\t\t\t标题\n\t * @property {String | Number}\tlabel\t\t\t标题下方的描述信息\n\t * @property {String | Number}\tvalue\t\t\t右侧的内容\n\t * @property {String}\t\t\ticon\t\t\t左侧图标名称，或者图片链接(本地文件建议使用绝对地址)\n\t * @property {Boolean}\t\t\tdisabled\t\t是否禁用cell\t\n\t * @property {Boolean}\t\t\tborder\t\t\t是否显示下边框 (默认 true )\n\t * @property {Boolean}\t\t\tcenter\t\t\t内容是否垂直居中(主要是针对右侧的value部分) (默认 false )\n\t * @property {String}\t\t\turl\t\t\t\t点击后跳转的URL地址\n\t * @property {String}\t\t\tlinkType\t\t链接跳转的方式，内部使用的是uView封装的route方法，可能会进行拦截操作 (默认 'navigateTo' )\n\t * @property {Boolean}\t\t\tclickable\t\t是否开启点击反馈(表现为点击时加上灰色背景) （默认 false ） \n\t * @property {Boolean}\t\t\tisLink\t\t\t是否展示右侧箭头并开启点击反馈 （默认 false ）\n\t * @property {Boolean}\t\t\trequired\t\t是否显示表单状态下的必填星号(此组件可能会内嵌入input组件) （默认 false ）\n\t * @property {String}\t\t\trightIcon\t\t右侧的图标箭头 （默认 'arrow-right'）\n\t * @property {String}\t\t\tarrowDirection\t右侧箭头的方向，可选值为：left，up，down\n\t * @property {Object | String}\t\t\trightIconStyle\t右侧箭头图标的样式\n\t * @property {Object | String}\t\t\ttitleStyle\t\t标题的样式\n\t * @property {Object | String}\t\t\ticonStyle\t\t左侧图标样式\n\t * @property {String}\t\t\tsize\t\t\t单位元的大小，可选值为 large，normal，mini \n\t * @property {Boolean}\t\t\tstop\t\t\t点击cell是否阻止事件传播 (默认 true )\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\n\t * \n\t * @event {Function}\t\t\tclick\t\t\t点击cell列表时触发\n\t * @example 该组件需要搭配cell-group组件使用，见官方文档示例\n\t */\n\texport default {\n\t\tname: 'u-cell',\n\t\tdata() {\n\t\t\treturn {\n\n\t\t\t}\n\t\t},\n\t\tmixins: [mpMixin, mixin, props],\n\t\tcomputed: {\n\t\t\ttitleTextStyle() {\n\t\t\t\treturn addStyle(this.titleStyle)\n\t\t\t}\n\t\t},\n\t\temits: ['click'],\n\t\tmethods: {\n\t\t\taddStyle,\n\t\t\ttestEmpty: test.empty,\n\t\t\t// 点击cell\n\t\t\tclickHandler(e) {\n\t\t\t\tif (this.disabled) return\n\t\t\t\tthis.$emit('click', {\n\t\t\t\t\tname: this.name\n\t\t\t\t})\n\t\t\t\t// 如果配置了url(此props参数通过mixin引入)参数，跳转页面\n\t\t\t\tthis.openPage()\n\t\t\t\t// 是否阻止事件传播\n\t\t\t\tthis.stop && this.preventEvent(e)\n\t\t\t},\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t$u-cell-padding: 13px 15px !default;\n\t$u-cell-font-size: 15px !default;\n\t$u-cell-line-height: 24px !default;\n\t$u-cell-color: $u-main-color !default;\n\t$u-cell-icon-size: 16px !default;\n\t$u-cell-title-font-size: 15px !default;\n\t$u-cell-title-line-height: 22px !default;\n\t$u-cell-title-color: $u-main-color !default;\n\t$u-cell-label-font-size: 12px !default;\n\t$u-cell-label-color: $u-tips-color !default;\n\t$u-cell-label-line-height: 18px !default;\n\t$u-cell-value-font-size: 14px !default;\n\t$u-cell-value-color: $u-content-color !default;\n\t$u-cell-clickable-color: $u-bg-color !default;\n\t$u-cell-disabled-color: #c8c9cc !default;\n\t$u-cell-padding-top-large: 13px !default;\n\t$u-cell-padding-bottom-large: 13px !default;\n\t$u-cell-value-font-size-large: 15px !default;\n\t$u-cell-label-font-size-large: 14px !default;\n\t$u-cell-title-font-size-large: 16px !default;\n\t$u-cell-left-icon-wrap-margin-right: 4px !default;\n\t$u-cell-right-icon-wrap-margin-left: 4px !default;\n\t$u-cell-title-flex:1 !default;\n\t$u-cell-label-margin-top:5px !default;\n\n\n\t.u-cell {\n\t\t&__body {\n\t\t\t@include flex();\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tbox-sizing: border-box;\n\t\t\t/* #endif */\n\t\t\tpadding: $u-cell-padding;\n\t\t\tfont-size: $u-cell-font-size;\n\t\t\tcolor: $u-cell-color;\n\t\t\t// line-height: $u-cell-line-height;\n\t\t\talign-items: center;\n\n\t\t\t&__content {\n\t\t\t\t@include flex(row);\n\t\t\t\talign-items: center;\n\t\t\t\tflex: 1;\n\t\t\t}\n\n\t\t\t&--large {\n\t\t\t\tpadding-top: $u-cell-padding-top-large;\n\t\t\t\tpadding-bottom: $u-cell-padding-bottom-large;\n\t\t\t}\n\t\t}\n\n\t\t&__left-icon-wrap,\n\t\t&__right-icon-wrap {\n\t\t\t@include flex();\n\t\t\talign-items: center;\n\t\t\t// height: $u-cell-line-height;\n\t\t\tfont-size: $u-cell-icon-size;\n\t\t}\n\n\t\t&__left-icon-wrap {\n\t\t\tmargin-right: $u-cell-left-icon-wrap-margin-right;\n\t\t}\n\n\t\t&__right-icon-wrap {\n\t\t\tmargin-left: $u-cell-right-icon-wrap-margin-left;\n\t\t\ttransition: transform 0.3s;\n\n\t\t\t&--up {\n\t\t\t\ttransform: rotate(-90deg);\n\t\t\t}\n\n\t\t\t&--down {\n\t\t\t\ttransform: rotate(90deg);\n\t\t\t}\n\t\t}\n\n\t\t&__title {\n\t\t\tflex: $u-cell-title-flex;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\n\t\t\t&-text {\n\t\t\t\tfont-size: $u-cell-title-font-size;\n\t\t\t\tline-height: $u-cell-title-line-height;\n\t\t\t\tcolor: $u-cell-title-color;\n\n\t\t\t\t&--large {\n\t\t\t\t\tfont-size: $u-cell-title-font-size-large;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t&__label {\n\t\t\tmargin-top: $u-cell-label-margin-top;\n\t\t\tfont-size: $u-cell-label-font-size;\n\t\t\tcolor: $u-cell-label-color;\n\t\t\tline-height: $u-cell-label-line-height;\n\n\t\t\t&--large {\n\t\t\t\tfont-size: $u-cell-label-font-size-large;\n\t\t\t}\n\t\t}\n\n\t\t&__value {\t\n\t\t\ttext-align: right;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tmargin-left: auto;\n\t\t\t/* #endif */\t\n\t\t\tfont-size: $u-cell-value-font-size;\n\t\t\tline-height: $u-cell-line-height;\n\t\t\tcolor: $u-cell-value-color;\n\t\t\t&--large {\n\t\t\t\tfont-size: $u-cell-value-font-size-large;\n\t\t\t}\n\t\t}\n\n\t\t&--required {\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\toverflow: visible;\n\t\t\t/* #endif */\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&--required:before {\n\t\t\tposition: absolute;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tcontent: '*';\n\t\t\t/* #endif */\n\t\t\tleft: -8px;\n\t\t\tmargin-top: 4rpx;\n\t\t\tfont-size: 14px;\n\t\t\tcolor: $u-error;\n\t\t}\n\n\t\t&--clickable {\n\t\t\tbackground-color: $u-cell-clickable-color;\n\t\t}\n\n\t\t&--disabled {\n\t\t\tcolor: $u-cell-disabled-color;\n\t\t\t/* #ifndef APP-NVUE */\n\t\t\tcursor: not-allowed;\n\t\t\t/* #endif */\n\t\t}\n\n\t\t&--center {\n\t\t\talign-items: center;\n\t\t}\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-cell/u-cell.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addStyle", "test"], "mappings": ";;;;;;;AAkFC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO,CAEP;AAAA,EACA;AAAA,EACD,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,6CAAK;AAAA,EAC9B,UAAU;AAAA,IACT,iBAAiB;AAChB,aAAOC,qCAAQ,SAAC,KAAK,UAAU;AAAA,IAChC;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,UAAAA,qCAAQ;AAAA,IACR,WAAWC,oCAAI,KAAC;AAAA;AAAA,IAEhB,aAAa,GAAG;AACf,UAAI,KAAK;AAAU;AACnB,WAAK,MAAM,SAAS;AAAA,QACnB,MAAM,KAAK;AAAA,OACX;AAED,WAAK,SAAS;AAEd,WAAK,QAAQ,KAAK,aAAa,CAAC;AAAA,IAChC;AAAA,EACF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9GD,GAAG,gBAAgB,SAAS;"}