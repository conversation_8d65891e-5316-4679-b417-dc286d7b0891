{"version": 3, "file": "image.js", "sources": ["utils/image.js"], "sourcesContent": ["/**\n * 图片相关工具函数\n */\nimport config from './config.js';\nimport { getImageInfo } from '@/api/images.js';\n\n/**\n * 获取完整的图片URL\n * @param {string} url - 图片相对路径或完整URL\n * @returns {string} - 返回完整的图片URL\n */\nexport function getFullImageUrl(url) {\n  if (!url) return '';\n\n  // 如果已经是完整URL，则直接返回\n  if (url.startsWith('http://') || url.startsWith('https://')) {\n    return url;\n  }\n\n  // 规范化URL路径\n  // 1. 移除开头的斜杠（如果有）\n  const cleanUrl = url.startsWith('/') ? url.substring(1) : url;\n\n  // 2. 确保apiBaseUrl不以斜杠结尾\n  const baseUrl = config.apiBaseUrl.endsWith('/')\n    ? config.apiBaseUrl.substring(0, config.apiBaseUrl.length - 1)\n    : config.apiBaseUrl;\n\n  // 3. 拼接URL，确保中间只有一个斜杠\n  return `${baseUrl}/${cleanUrl}`;\n}\n\n/**\n * 通过图片ID获取图片信息\n * @param {number} imageId - 图片ID\n * @returns {Promise<Object|null>} - 返回图片信息对象，失败时返回null\n */\nexport async function fetchImageById(imageId) {\n  if (!imageId) return null;\n\n  try {\n    const result = await getImageInfo(imageId);\n    if (result && result.success && result.data) {\n      return result.data;\n    }\n    return null;\n  } catch (error) {\n    console.error('获取图片信息失败:', error);\n    return null;\n  }\n}\n"], "names": ["config", "getImageInfo", "uni"], "mappings": ";;;;AAWO,SAAS,gBAAgB,KAAK;AACnC,MAAI,CAAC;AAAK,WAAO;AAGjB,MAAI,IAAI,WAAW,SAAS,KAAK,IAAI,WAAW,UAAU,GAAG;AAC3D,WAAO;AAAA,EACR;AAID,QAAM,WAAW,IAAI,WAAW,GAAG,IAAI,IAAI,UAAU,CAAC,IAAI;AAG1D,QAAM,UAAUA,aAAM,OAAC,WAAW,SAAS,GAAG,IAC1CA,aAAM,OAAC,WAAW,UAAU,GAAGA,aAAAA,OAAO,WAAW,SAAS,CAAC,IAC3DA,aAAAA,OAAO;AAGX,SAAO,GAAG,OAAO,IAAI,QAAQ;AAC/B;AAOO,eAAe,eAAe,SAAS;AAC5C,MAAI,CAAC;AAAS,WAAO;AAErB,MAAI;AACF,UAAM,SAAS,MAAMC,wBAAa,OAAO;AACzC,QAAI,UAAU,OAAO,WAAW,OAAO,MAAM;AAC3C,aAAO,OAAO;AAAA,IACf;AACD,WAAO;AAAA,EACR,SAAQ,OAAO;AACdC,kBAAA,MAAA,MAAA,SAAA,wBAAc,aAAa,KAAK;AAChC,WAAO;AAAA,EACR;AACH;;;"}