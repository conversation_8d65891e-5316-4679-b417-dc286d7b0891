{"version": 3, "file": "party-articles.js", "sources": ["api/party-articles.js"], "sourcesContent": ["import request from './request.js';\n\n/**\n * 获取党建文章列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getPartyArticleList(params = {}) {\n  return request.get('/api/party-articles/', params);\n}\n\n/**\n * 获取党建文章详情\n * @param {String|Number} id - 文章ID\n * @returns {Promise} - 返回Promise对象\n */\nexport function getPartyArticleDetail(id) {\n  return request.get(`/api/party-articles/${id}`);\n}\n"], "names": ["request"], "mappings": ";;AAgBO,SAAS,sBAAsB,IAAI;AACxC,SAAOA,YAAAA,QAAQ,IAAI,uBAAuB,EAAE,EAAE;AAChD;;"}