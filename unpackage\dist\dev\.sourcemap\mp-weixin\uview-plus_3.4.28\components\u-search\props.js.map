{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-search/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 搜索框形状，round-圆形，square-方形\n        shape: {\n            type: String,\n            default: () => defProps.search.shape\n        },\n        // 搜索框背景色，默认值#f2f2f2\n        bgColor: {\n            type: String,\n            default: () => defProps.search.bgColor\n        },\n        // 占位提示文字\n        placeholder: {\n            type: String,\n            default: () => defProps.search.placeholder\n        },\n        // 是否启用清除控件\n        clearabled: {\n            type: Boolean,\n            default: () => defProps.search.clearabled\n        },\n        // 是否自动聚焦\n        focus: {\n            type: Boolean,\n            default: () => defProps.search.focus\n        },\n        // 是否在搜索框右侧显示取消按钮\n        showAction: {\n            type: Boolean,\n            default: () => defProps.search.showAction\n        },\n        // 右边控件的样式\n        actionStyle: {\n            type: Object,\n            default: () => defProps.search.actionStyle\n        },\n        // 取消按钮文字\n        actionText: {\n            type: String,\n            default: () => defProps.search.actionText\n        },\n        // 输入框内容对齐方式，可选值为 left|center|right\n        inputAlign: {\n            type: String,\n            default: () => defProps.search.inputAlign\n        },\n        // input输入框的样式，可以定义文字颜色，大小等，对象形式\n        inputStyle: {\n            type: Object,\n            default: () => defProps.search.inputStyle\n        },\n        // 是否启用输入框\n        disabled: {\n            type: Boolean,\n            default: () => defProps.search.disabled\n        },\n        // 边框颜色\n        borderColor: {\n            type: String,\n            default: () => defProps.search.borderColor\n        },\n        // 搜索图标的颜色，默认同输入框字体颜色\n        searchIconColor: {\n            type: String,\n            default: () => defProps.search.searchIconColor\n        },\n        // 输入框字体颜色\n        color: {\n            type: String,\n            default: () => defProps.search.color\n        },\n        // placeholder的颜色\n        placeholderColor: {\n            type: String,\n            default: () => defProps.search.placeholderColor\n        },\n        // 左边输入框的图标，可以为uView图标名称或图片路径\n        searchIcon: {\n            type: String,\n            default: () => defProps.search.searchIcon\n        },\n        searchIconSize: {\n            type: [Number, String],\n            default: () => defProps.search.searchIconSize\n        },\n        // 组件与其他上下左右元素之间的距离，带单位的字符串形式，如\"30px\"、\"30px 20px\"等写法\n        margin: {\n            type: String,\n            default: () => defProps.search.margin\n        },\n        // 开启showAction时，是否在input获取焦点时才显示\n        animation: {\n            type: Boolean,\n            default: () => defProps.search.animation\n        },\n        // 输入框的初始化内容\n        modelValue: {\n            type: String,\n            default: () => defProps.search.value\n        },\n\t\tvalue: {\n\t\t    type: String,\n\t\t    default: () => defProps.search.value\n\t\t},\n        // 输入框最大能输入的长度，-1为不限制长度(来自uniapp文档)\n        maxlength: {\n            type: [String, Number],\n            default: () => defProps.search.maxlength\n        },\n        // 搜索框高度，单位px\n        height: {\n            type: [String, Number],\n            default: () => defProps.search.height\n        },\n        // 搜索框左侧文本\n        label: {\n            type: [String, Number, null],\n            default: () => defProps.search.label\n        },\n        // 键盘弹起时，是否自动上推页面\t\n        adjustPosition: {\n            type: Boolean,\n            default: () => true\n        },\n        // 键盘收起时，是否自动失去焦点\t\n        autoBlur: {\n            type: Boolean,\n            default: () => false\n        },\n        iconPosition: {\n            type: String,\n            default: () => defProps.search.iconPosition\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA,IACD,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA,IACP,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEK,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,QAAQ,IAAI;AAAA,MAC3B,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA,IACD,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}