{"version": 3, "file": "radioGroup.js", "sources": ["uview-plus_3.4.28/components/u-radio-group/radioGroup.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : CPS\n * @lastTime     : 2024-11-05 16:01:12\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radioGroup.js\n */\nexport default {\n    // radio-group组件\n    radioGroup: {\n        value: '',\n        disabled: false,\n        shape: 'circle',\n        activeColor: '#2979ff',\n        inactiveColor: '#c8c9cc',\n        name: '',\n        size: 18,\n        placement: 'row',\n        label: '',\n        labelColor: '#303133',\n        labelSize: 14,\n        labelDisabled: false,\n        iconColor: '#ffffff',\n        iconSize: 12,\n        borderBottom: false,\n        iconPlacement: 'left',\n        gap: \"10px\"\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,OAAO;AAAA,IACP,UAAU;AAAA,IACV,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,cAAc;AAAA,IACd,eAAe;AAAA,IACf,KAAK;AAAA,EACR;AACL;;"}