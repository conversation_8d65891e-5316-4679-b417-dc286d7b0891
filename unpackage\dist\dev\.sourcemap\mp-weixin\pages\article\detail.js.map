{"version": 3, "file": "detail.js", "sources": ["pages/article/detail.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXJ0aWNsZS9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n\t<view class=\"common-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar :title=\"pageTitle\" :showBack=\"true\" :showHome=\"true\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 使用公共文章详情组件 -->\n\t\t<view class=\"article-container\">\n\t\t\t<template v-if=\"article\">\n\t\t\t\t<article-detail\n\t\t\t\t\t:article=\"article\"\n\t\t\t\t\t:articleType=\"articleType\"\n\t\t\t\t\t:showFooter=\"true\"\n\t\t\t\t></article-detail>\n\t\t\t</template>\n\t\t\t<template v-else>\n\t\t\t\t<view class=\"loading-container\">\n\t\t\t\t\t<u-loading-icon mode=\"circle\" size=\"28\"></u-loading-icon>\n\t\t\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\timport ArticleDetail from '@/components/article/article-detail.vue';\n\timport { fetchImageById, getFullImageUrl } from '@/utils/image.js';\n\timport { getArticleApi } from './map.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar,\n\t\t\tArticleDetail\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tarticleId: null,\n\t\t\t\tarticleType: 'news', // 默认为新闻文章，可选值：news, menu, party\n\t\t\t\tarticle: null,\n\t\t\t\tloading: true,\n\t\t\t\tpageTitle: '文章详情', // 默认页面标题\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 当前文章类型对应的API函数\n\t\t\tapiFunction() {\n\t\t\t\treturn getArticleApi(this.articleType);\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取传递过来的文章ID和类型\n\t\t\tif (options) {\n\t\t\t\tif (options.id) {\n\t\t\t\t\tthis.articleId = options.id;\n\t\t\t\t} else {\n\t\t\t\t\tthis.showError('文章ID不存在');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (options.type) {\n\t\t\t\t\tthis.articleType = options.type;\n\t\t\t\t}\n\n\t\t\t\t// 加载文章详情\n\t\t\t\tthis.fetchArticleDetail();\n\t\t\t} else {\n\t\t\t\tthis.showError('参数不存在');\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 显示错误并返回\n\t\t\tshowError(message) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t},\n\n\t\t\t// 获取文章详情\n\t\t\tasync fetchArticleDetail() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 调用API函数获取文章详情\n\t\t\t\t\tlet result;\n\t\t\t\t\tresult = await this.apiFunction(this.articleId);\n\n\t\t\t\t\tconsole.log(result)\n\n\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\tthis.article = result.data;\n\n\t\t\t\t\t\t// 设置页面标题\n\t\t\t\t\t\tthis.pageTitle = this.article.title || '文章详情';\n\n\t\t\t\t\t\t// 处理文章内容\n\t\t\t\t\t\tif (this.articleType === 'news' && !this.article.content_markdown) {\n\t\t\t\t\t\t\t// 新闻文章可能使用Markdown格式\n\t\t\t\t\t\t\tthis.article.content_markdown = `这是一篇关于\"${this.article.title}\"的文章内容。\\n\\n由于API暂未提供文章内容，这里展示的是默认内容。`;\n\t\t\t\t\t\t} else if (!this.article.content) {\n\t\t\t\t\t\t\t// 其他类型文章使用HTML格式\n\t\t\t\t\t\t\tthis.article.content = `这是一篇关于\"${this.article.title}\"的文章内容。\\n\\n由于API暂未提供完整内容，这里展示的是默认内容。`;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 处理文章内容中的图片（如果需要）\n\t\t\t\t\t\tif (this.article.content && this.article.content.includes('[pic:')) {\n\t\t\t\t\t\t\tawait this.processArticleImages(this.article);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 获取封面图片\n\t\t\t\t\t\tif (this.article.coverImageId) {\n\t\t\t\t\t\t\tawait this.fetchCoverImage(this.article.coverImageId);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.showError('获取文章失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取文章详情失败:', error);\n\t\t\t\t\tthis.showError('获取文章失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理文章内容中的图片\n\t\t\tasync processArticleImages(article) {\n\t\t\t\tif (!article.content) return;\n\n\t\t\t\t// 使用正则表达式匹配所有 [pic:id] 格式的字符串\n\t\t\t\tconst picRegex = /\\[pic:(\\d+)\\]/g;\n\t\t\t\tlet match;\n\t\t\t\tlet processedContent = article.content;\n\n\t\t\t\t// 创建一个存储所有图片获取Promise的数组\n\t\t\t\tconst imagePromises = [];\n\t\t\t\tconst imageMatches = [];\n\n\t\t\t\t// 收集所有需要处理的图片ID\n\t\t\t\twhile ((match = picRegex.exec(article.content)) !== null) {\n\t\t\t\t\tconst imageId = parseInt(match[1]);\n\t\t\t\t\timageMatches.push({\n\t\t\t\t\t\tfullMatch: match[0],\n\t\t\t\t\t\timageId: imageId\n\t\t\t\t\t});\n\n\t\t\t\t\t// 添加获取图片信息的Promise\n\t\t\t\t\timagePromises.push(fetchImageById(imageId));\n\t\t\t\t}\n\n\t\t\t\t// 等待所有图片信息获取完成\n\t\t\t\tif (imagePromises.length > 0) {\n\t\t\t\t\tconst imageResults = await Promise.all(imagePromises);\n\n\t\t\t\t\t// 替换内容中的图片标记\n\t\t\t\t\tfor (let i = 0; i < imageMatches.length; i++) {\n\t\t\t\t\t\tconst imageData = imageResults[i];\n\t\t\t\t\t\tconst { fullMatch } = imageMatches[i];\n\n\t\t\t\t\t\tif (imageData && imageData.url) {\n\t\t\t\t\t\t\tconst imageUrl = getFullImageUrl(imageData.url);\n\t\t\t\t\t\t\t// 替换为图片HTML标签\n\t\t\t\t\t\t\tprocessedContent = processedContent.replace(\n\t\t\t\t\t\t\t\tfullMatch,\n\t\t\t\t\t\t\t\t`<img src=\"${imageUrl}\" alt=\"${imageData.altText || '文章图片'}\" style=\"max-width: 100%;\">`\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果获取图片失败，则移除图片标记\n\t\t\t\t\t\t\tprocessedContent = processedContent.replace(fullMatch, '');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新文章内容\n\t\t\t\t\tarticle.content = processedContent;\n\t\t\t\t}\n\n\t\t\t\t// 处理封面图\n\t\t\t\tif (article.coverImageId && !article.coverImage) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst imageData = await fetchImageById(article.coverImageId);\n\t\t\t\t\t\tif (imageData && imageData.url) {\n\t\t\t\t\t\t\tarticle.coverImage = getFullImageUrl(imageData.url);\n\t\t\t\t\t\t}\n\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\tconsole.error('获取文章封面图失败:', err);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取封面图片\n\t\t\tasync fetchCoverImage(imageId) {\n\t\t\t\ttry {\n\t\t\t\t\tconst imageData = await fetchImageById(imageId);\n\t\t\t\t\tconsole.log('获取封面图片成功:', imageData);\n\n\t\t\t\t\tif (imageData && imageData.url) {\n\t\t\t\t\t\t// 设置封面图URL\n\t\t\t\t\t\tif (this.articleType === 'normal' || this.articleType === 'party') {\n\t\t\t\t\t\t\t// 普通文章和党建文章需要在组件中处理封面图\n\t\t\t\t\t\t\tthis.article.coverImage = getFullImageUrl(imageData.url);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取封面图片失败:', error);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t.article-container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 8rpx;\n\n\t\t.loading-container {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tpadding: 100rpx 0;\n\n\t\t\t.loading-text {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #999999;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/article/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["getArticleApi", "uni", "fetchImageById", "getFullImageUrl"], "mappings": ";;;;AA+BC,MAAK,eAAgB,MAAW;AAChC,MAAO,gBAAe,MAAW;AAIjC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA;AAAA,MACb,SAAS;AAAA,MACT,SAAS;AAAA,MACT,WAAW;AAAA;AAAA,IACZ;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,aAAOA,kBAAa,cAAC,KAAK,WAAW;AAAA,IACtC;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,SAAS;AACZ,UAAI,QAAQ,IAAI;AACf,aAAK,YAAY,QAAQ;AAAA,aACnB;AACN,aAAK,UAAU,SAAS;AACxB;AAAA,MACD;AAEA,UAAI,QAAQ,MAAM;AACjB,aAAK,cAAc,QAAQ;AAAA,MAC5B;AAGA,WAAK,mBAAkB;AAAA,WACjB;AACN,WAAK,UAAU,OAAO;AAAA,IACvB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,UAAU,SAAS;AAClBC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACP;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAC1B,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAGD,YAAI;AACJ,iBAAS,MAAM,KAAK,YAAY,KAAK,SAAS;AAE9CA,sBAAAA,MAAA,MAAA,OAAA,mCAAY,MAAM;AAElB,YAAI,UAAU,OAAO,WAAW,OAAO,MAAM;AAC5C,eAAK,UAAU,OAAO;AAGtB,eAAK,YAAY,KAAK,QAAQ,SAAS;AAGvC,cAAI,KAAK,gBAAgB,UAAU,CAAC,KAAK,QAAQ,kBAAkB;AAElE,iBAAK,QAAQ,mBAAmB,UAAU,KAAK,QAAQ,KAAK;AAAA;AAAA;AAAA,UAC7D,WAAW,CAAC,KAAK,QAAQ,SAAS;AAEjC,iBAAK,QAAQ,UAAU,UAAU,KAAK,QAAQ,KAAK;AAAA;AAAA;AAAA,UACpD;AAGA,cAAI,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ,SAAS,OAAO,GAAG;AACnE,kBAAM,KAAK,qBAAqB,KAAK,OAAO;AAAA,UAC7C;AAGA,cAAI,KAAK,QAAQ,cAAc;AAC9B,kBAAM,KAAK,gBAAgB,KAAK,QAAQ,YAAY;AAAA,UACrD;AAAA,eACM;AACN,eAAK,UAAU,QAAQ;AAAA,QACxB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,KAAK;AAChC,aAAK,UAAU,QAAQ;AAAA,MACxB,UAAU;AACTA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,qBAAqB,SAAS;AACnC,UAAI,CAAC,QAAQ;AAAS;AAGtB,YAAM,WAAW;AACjB,UAAI;AACJ,UAAI,mBAAmB,QAAQ;AAG/B,YAAM,gBAAgB,CAAA;AACtB,YAAM,eAAe,CAAA;AAGrB,cAAQ,QAAQ,SAAS,KAAK,QAAQ,OAAO,OAAO,MAAM;AACzD,cAAM,UAAU,SAAS,MAAM,CAAC,CAAC;AACjC,qBAAa,KAAK;AAAA,UACjB,WAAW,MAAM,CAAC;AAAA,UAClB;AAAA,QACD,CAAC;AAGD,sBAAc,KAAKC,2BAAe,OAAO,CAAC;AAAA,MAC3C;AAGA,UAAI,cAAc,SAAS,GAAG;AAC7B,cAAM,eAAe,MAAM,QAAQ,IAAI,aAAa;AAGpD,iBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC7C,gBAAM,YAAY,aAAa,CAAC;AAChC,gBAAM,EAAE,UAAU,IAAI,aAAa,CAAC;AAEpC,cAAI,aAAa,UAAU,KAAK;AAC/B,kBAAM,WAAWC,YAAAA,gBAAgB,UAAU,GAAG;AAE9C,+BAAmB,iBAAiB;AAAA,cACnC;AAAA,cACA,aAAa,QAAQ,UAAU,UAAU,WAAW,MAAM;AAAA;iBAErD;AAEN,+BAAmB,iBAAiB,QAAQ,WAAW,EAAE;AAAA,UAC1D;AAAA,QACD;AAGA,gBAAQ,UAAU;AAAA,MACnB;AAGA,UAAI,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;AAChD,YAAI;AACH,gBAAM,YAAY,MAAMD,YAAAA,eAAe,QAAQ,YAAY;AAC3D,cAAI,aAAa,UAAU,KAAK;AAC/B,oBAAQ,aAAaC,YAAAA,gBAAgB,UAAU,GAAG;AAAA,UACnD;AAAA,QACC,SAAO,KAAK;AACbF,gFAAc,cAAc,GAAG;AAAA,QAChC;AAAA,MACD;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,gBAAgB,SAAS;AAC9B,UAAI;AACH,cAAM,YAAY,MAAMC,2BAAe,OAAO;AAC9CD,sBAAY,MAAA,MAAA,OAAA,mCAAA,aAAa,SAAS;AAElC,YAAI,aAAa,UAAU,KAAK;AAE/B,cAAI,KAAK,gBAAgB,YAAY,KAAK,gBAAgB,SAAS;AAElE,iBAAK,QAAQ,aAAaE,YAAe,gBAAC,UAAU,GAAG;AAAA,UACxD;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfF,sBAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1ND,GAAG,WAAW,eAAe;"}