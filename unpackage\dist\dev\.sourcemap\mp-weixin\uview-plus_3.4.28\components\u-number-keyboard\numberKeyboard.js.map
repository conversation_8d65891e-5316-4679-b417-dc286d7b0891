{"version": 3, "file": "numberKeyboard.js", "sources": ["uview-plus_3.4.28/components/u-number-keyboard/numberKeyboard.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:08:05\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberKeyboard.js\n */\nexport default {\n    // 数字键盘\n    numberKeyboard: {\n        mode: 'number',\n        dotDisabled: false,\n        random: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,iBAAA;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,aAAa;AAAA,IACb,QAAQ;AAAA,EACX;AACL;;"}