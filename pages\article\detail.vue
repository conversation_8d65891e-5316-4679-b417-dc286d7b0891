<template>
	<view class="common-container">
		<!-- 自定义导航栏 -->
		<custom-navbar :title="pageTitle" :showBack="true" :showHome="true">
			<template #right>
				<view class="navbar-right">
					<u-icon name="more-dot-fill" color="#FFFFFF" size="20"></u-icon>
				</view>
			</template>
		</custom-navbar>

		<!-- 使用公共文章详情组件 -->
		<view class="article-container">
			<template v-if="article">
				<article-detail
					:article="article"
					:articleType="articleType"
					:showFooter="true"
				></article-detail>
			</template>
			<template v-else>
				<view class="loading-container">
					<u-loading-icon mode="circle" size="28"></u-loading-icon>
					<text class="loading-text">加载中...</text>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	import CustomNavbar from '@/components/common/custom-navbar.vue';
	import ArticleDetail from '@/components/article/article-detail.vue';
	import { fetchImageById, getFullImageUrl } from '@/utils/image.js';
	import { getArticleApi } from './map.js';

	export default {
		components: {
			CustomNavbar,
			ArticleDetail
		},
		data() {
			return {
				articleId: null,
				articleType: 'news', // 默认为新闻文章，可选值：news, menu, party
				article: null,
				loading: true,
				pageTitle: '文章详情', // 默认页面标题
			}
		},
		computed: {
			// 当前文章类型对应的API函数
			apiFunction() {
				return getArticleApi(this.articleType);
			}
		},
		onLoad(options) {
			// 获取传递过来的文章ID和类型
			if (options) {
				if (options.id) {
					this.articleId = options.id;
				} else {
					this.showError('文章ID不存在');
					return;
				}

				if (options.type) {
					this.articleType = options.type;
				}

				// 加载文章详情
				this.fetchArticleDetail();
			} else {
				this.showError('参数不存在');
			}
		},
		methods: {
			// 显示错误并返回
			showError(message) {
				uni.showToast({
					title: message,
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			},

			// 获取文章详情
			async fetchArticleDetail() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					// 调用API函数获取文章详情
					let result;
					result = await this.apiFunction(this.articleId);

					console.log(result)

					if (result && result.success && result.data) {
						this.article = result.data;

						// 设置页面标题
						this.pageTitle = this.article.title || '文章详情';

						// 处理文章内容
						if (this.articleType === 'news' && !this.article.content_markdown) {
							// 新闻文章可能使用Markdown格式
							this.article.content_markdown = `这是一篇关于"${this.article.title}"的文章内容。\n\n由于API暂未提供文章内容，这里展示的是默认内容。`;
						} else if (!this.article.content) {
							// 其他类型文章使用HTML格式
							this.article.content = `这是一篇关于"${this.article.title}"的文章内容。\n\n由于API暂未提供完整内容，这里展示的是默认内容。`;
						}

						// 处理文章内容中的图片（如果需要）
						if (this.article.content && this.article.content.includes('[pic:')) {
							await this.processArticleImages(this.article);
						}

						// 获取封面图片
						if (this.article.coverImageId) {
							await this.fetchCoverImage(this.article.coverImageId);
						}
					} else {
						this.showError('获取文章失败');
					}
				} catch (error) {
					console.error('获取文章详情失败:', error);
					this.showError('获取文章失败');
				} finally {
					uni.hideLoading();
					this.loading = false;
				}
			},

			// 处理文章内容中的图片
			async processArticleImages(article) {
				if (!article.content) return;

				// 使用正则表达式匹配所有 [pic:id] 格式的字符串
				const picRegex = /\[pic:(\d+)\]/g;
				let match;
				let processedContent = article.content;

				// 创建一个存储所有图片获取Promise的数组
				const imagePromises = [];
				const imageMatches = [];

				// 收集所有需要处理的图片ID
				while ((match = picRegex.exec(article.content)) !== null) {
					const imageId = parseInt(match[1]);
					imageMatches.push({
						fullMatch: match[0],
						imageId: imageId
					});

					// 添加获取图片信息的Promise
					imagePromises.push(fetchImageById(imageId));
				}

				// 等待所有图片信息获取完成
				if (imagePromises.length > 0) {
					const imageResults = await Promise.all(imagePromises);

					// 替换内容中的图片标记
					for (let i = 0; i < imageMatches.length; i++) {
						const imageData = imageResults[i];
						const { fullMatch } = imageMatches[i];

						if (imageData && imageData.url) {
							const imageUrl = getFullImageUrl(imageData.url);
							// 替换为图片HTML标签
							processedContent = processedContent.replace(
								fullMatch,
								`<img src="${imageUrl}" alt="${imageData.altText || '文章图片'}" style="max-width: 100%;">`
							);
						} else {
							// 如果获取图片失败，则移除图片标记
							processedContent = processedContent.replace(fullMatch, '');
						}
					}

					// 更新文章内容
					article.content = processedContent;
				}

				// 处理封面图
				if (article.coverImageId && !article.coverImage) {
					try {
						const imageData = await fetchImageById(article.coverImageId);
						if (imageData && imageData.url) {
							article.coverImage = getFullImageUrl(imageData.url);
						}
					} catch (err) {
						console.error('获取文章封面图失败:', err);
					}
				}
			},

			// 获取封面图片
			async fetchCoverImage(imageId) {
				try {
					const imageData = await fetchImageById(imageId);
					console.log('获取封面图片成功:', imageData);

					if (imageData && imageData.url) {
						// 设置封面图URL
						if (this.articleType === 'normal' || this.articleType === 'party') {
							// 普通文章和党建文章需要在组件中处理封面图
							this.article.coverImage = getFullImageUrl(imageData.url);
						}
					}
				} catch (error) {
					console.error('获取封面图片失败:', error);
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 使用全局样式文件中的.common-container */

	.article-container {
		padding: 30rpx;
		background-color: #FFFFFF;
		border-radius: 8rpx;

		.loading-container {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;

			.loading-text {
				margin-top: 20rpx;
				font-size: 28rpx;
				color: #999999;
			}
		}
	}
</style>
