{"version": 3, "file": "tabbarItem.js", "sources": ["uview-plus_3.4.28/components/u-tabbar-item/tabbarItem.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbarItem.js\n */\nexport default {\n    //\n    tabbarItem: {\n        name: null,\n        icon: '',\n        badge: null,\n        dot: false,\n        text: '',\n        badgeStyle: 'top: 6px;right:2px;'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,EACf;AACL;;"}