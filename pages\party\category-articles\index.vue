<template>
	<view class="common-container">
		<!-- 自定义导航栏 -->
		<custom-navbar :title="category.name || '文章列表'" :showBack="true" :showHome="true">
			<template #right>
				<view class="navbar-right">
					<u-icon name="more-dot-fill" color="#FFFFFF" size="20"></u-icon>
				</view>
			</template>
		</custom-navbar>

		<!-- 文章列表内容 -->
		<view class="articles-content">
			<view class="content-section">
				<view class="section-title">
					<u-icon name="list" color="#D9001B" size="20"></u-icon>
					<text>文章列表</text>
				</view>
				<view class="section-body">
					<u-list :enable-flex="true">
						<u-cell v-for="(item, index) in articleList" :key="index" :title="item.title"
							:titleStyle="{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}"
							isLink @click="onArticleClick(item)">
						</u-cell>
					</u-list>

					<!-- 空状态 -->
					<u-empty v-if="articleList.length === 0" mode="list" text="暂无文章"></u-empty>
				</view>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="category-footer">
			<u-button type="primary" text="返回上一页" @click="() => uni.navigateBack()" :customStyle="{backgroundColor: '#D9001B'}"></u-button>
		</view>
	</view>
</template>

<script>
	import { categoryApi } from '@/api/index.js';
	import CustomNavbar from '@/components/common/custom-navbar.vue';

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				categoryId: null,
				category: {},
				articleList: []
			}
		},
		onLoad(options) {
			// 获取路由参数中的分类ID
			if (options.id) {
				this.categoryId = options.id;
				console.log('分类ID:', this.categoryId);
				// 获取分类文章列表
				this.fetchCategoryArticles();
			} else {
				uni.showToast({
					title: '参数错误',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		},
		methods: {
			// 获取分类文章列表
			async fetchCategoryArticles() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					const result = await categoryApi.getCategoryArticles(this.categoryId);
					console.log('获取分类文章列表成功:', result);

					if (result && result.success) {
						// 设置分类信息
						if (result.data.category) {
							this.category = result.data.categoryId;
						}

						// 设置文章列表
						if (Array.isArray(result.data)) {
							this.articleList = result.data;
						}
					}
				} catch (error) {
					console.error('获取分类文章列表失败:', error);
					uni.showToast({
						title: '获取文章列表失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			// 文章点击事件
			onArticleClick(item) {
				console.log('点击了文章:', item);

				// 党建版块下所有文章都是党建文章，直接跳转到统一的文章详情页面
				console.log('跳转到党建文章详情页');
				uni.navigateTo({
					url: `/pages/article/detail?id=${item.id}&type=party`,
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},
			// 返回上一页方法已由导航栏组件自动处理
		}
	}
</script>

<style lang="scss">
	/* 使用全局样式文件中的.common-container */

	.articles-content {
		margin-bottom: 30rpx;
	}

	.section-body {
		padding: 10rpx 0;
	}

	/* 使用全局样式文件中的.category-footer */
</style>
