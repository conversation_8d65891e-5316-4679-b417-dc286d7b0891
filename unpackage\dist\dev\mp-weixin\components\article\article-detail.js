"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_image = require("../../utils/image.js");
const _sfc_main = {
  name: "ArticleDetail",
  props: {
    // 文章对象
    article: {
      type: Object,
      default: () => ({
        id: null,
        title: "",
        content: "",
        content_markdown: "",
        created_at: "",
        createdAt: "",
        category: "",
        author: "",
        coverImageId: null
      })
    },
    // 文章类型：'normal'(普通文章) 或 'menu'(菜单文章)
    articleType: {
      type: String,
      default: "normal",
      validator: (value) => ["normal", "menu"].includes(value)
    },
    // 是否显示底部按钮
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      coverImage: null,
      loading: true
    };
  },
  computed: {
    // 处理后的文章内容
    articleContent() {
      if (this.articleType === "normal") {
        return this.formatContent(this.article.content_markdown);
      } else {
        return this.article.content;
      }
    }
  },
  watch: {
    // 监听文章变化，获取封面图
    article: {
      handler(newVal) {
        if (newVal && newVal.coverImageId) {
          this.fetchCoverImage(newVal.coverImageId);
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
    },
    // 格式化Markdown内容为HTML
    formatContent(markdown) {
      if (!markdown)
        return "";
      let html = markdown.replace(/\n\n/g, '</p><p class="article-paragraph">').replace(/\n/g, "<br>").replace(/#{6}\s(.*?)\s*$/gm, "<h6>$1</h6>").replace(/#{5}\s(.*?)\s*$/gm, "<h5>$1</h5>").replace(/#{4}\s(.*?)\s*$/gm, "<h4>$1</h4>").replace(/#{3}\s(.*?)\s*$/gm, "<h3>$1</h3>").replace(/#{2}\s(.*?)\s*$/gm, "<h2>$1</h2>").replace(/#{1}\s(.*?)\s*$/gm, "<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>").replace(/\*(.*?)\*/g, "<em>$1</em>").replace(/>\s(.*?)\s*$/gm, "<blockquote>$1</blockquote>");
      if (!html.startsWith("<")) {
        html = '<p class="article-paragraph">' + html;
      }
      if (!html.endsWith(">")) {
        html = html + "</p>";
      }
      return html;
    },
    // 获取封面图片
    async fetchCoverImage(imageId) {
      try {
        const imageData = await utils_image.fetchImageById(imageId);
        common_vendor.index.__f__("log", "at components/article/article-detail.vue:164", "获取封面图片成功:", imageData);
        if (imageData) {
          this.coverImage = imageData;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at components/article/article-detail.vue:170", "获取封面图片失败:", error);
      }
    },
    // 获取完整的图片URL
    getFullImageUrl(url) {
      return utils_image.getFullImageUrl(url);
    },
    // 预览封面图片
    previewCoverImage() {
      if (this.coverImage && this.coverImage.url) {
        const imageUrl = this.getFullImageUrl(this.coverImage.url);
        common_vendor.index.__f__("log", "at components/article/article-detail.vue:183", "预览封面图片:", imageUrl);
        common_vendor.index.previewImage({
          urls: [imageUrl],
          current: imageUrl,
          success: () => {
            common_vendor.index.__f__("log", "at components/article/article-detail.vue:189", "图片预览成功");
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at components/article/article-detail.vue:192", "图片预览失败:", err);
            common_vendor.index.showToast({
              title: "图片预览失败",
              icon: "none"
            });
          }
        });
      }
    },
    // 跳转到小程序
    navigateToMiniProgram() {
      if (!this.coverImage || !this.coverImage.miniAppId || !this.coverImage.miniAppPagePath) {
        common_vendor.index.showToast({
          title: "小程序信息不完整",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at components/article/article-detail.vue:212", "准备跳转到小程序:", {
        appId: this.coverImage.miniAppId,
        path: this.coverImage.miniAppPagePath,
        title: this.coverImage.title
      });
      common_vendor.index.navigateToMiniProgram({
        appId: this.coverImage.miniAppId,
        path: this.coverImage.miniAppPagePath,
        extraData: {
          source: "article_cover_image",
          articleId: this.article.id,
          imageId: this.coverImage.id
        },
        envVersion: "release",
        // 正式版
        success: (res) => {
          common_vendor.index.__f__("log", "at components/article/article-detail.vue:228", "跳转小程序成功:", res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at components/article/article-detail.vue:231", "跳转小程序失败:", err);
          this.showMiniProgramFallback();
        }
      });
    },
    // 跳转失败时的备用方案
    showMiniProgramFallback() {
      common_vendor.index.showModal({
        title: "跳转失败",
        content: "无法直接跳转到小程序，请尝试其他方式",
        showCancel: true,
        cancelText: "取消",
        confirmText: "复制信息",
        success: (res) => {
          if (res.confirm) {
            const info = `小程序ID: ${this.coverImage.miniAppId}
页面路径: ${this.coverImage.miniAppPagePath}`;
            common_vendor.index.setClipboardData({
              data: info,
              success: () => {
                common_vendor.index.showToast({
                  title: "已复制到剪贴板",
                  icon: "success"
                });
              }
            });
          }
        }
      });
    },
    // 返回上一页
    goBack() {
      common_vendor.index.navigateBack();
    }
  }
};
if (!Array) {
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  _easycom_u_button2();
}
const _easycom_u_button = () => "../../uview-plus_3.4.28/components/u-button/u-button.js";
if (!Math) {
  _easycom_u_button();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($props.article.title || "加载中..."),
    b: $props.article.created_at || $props.article.createdAt
  }, $props.article.created_at || $props.article.createdAt ? {
    c: common_vendor.t($options.formatDate($props.article.created_at || $props.article.createdAt))
  } : {}, {
    d: $props.article.category
  }, $props.article.category ? {
    e: common_vendor.t($props.article.category)
  } : {}, {
    f: $props.article.author
  }, $props.article.author ? {
    g: common_vendor.t($props.article.author)
  } : {}, {
    h: $data.coverImage
  }, $data.coverImage ? common_vendor.e({
    i: $options.getFullImageUrl($data.coverImage.url),
    j: $data.coverImage.altText || $props.article.title,
    k: common_vendor.o((...args) => $options.previewCoverImage && $options.previewCoverImage(...args)),
    l: $data.coverImage.miniAppId && $data.coverImage.miniAppPagePath
  }, $data.coverImage.miniAppId && $data.coverImage.miniAppPagePath ? {
    m: common_vendor.t($data.coverImage.title || "相关小程序"),
    n: common_vendor.o((...args) => $options.navigateToMiniProgram && $options.navigateToMiniProgram(...args))
  } : {}) : {}, {
    o: $options.articleContent || "加载中...",
    p: common_vendor.o($options.goBack),
    q: common_vendor.p({
      type: "primary",
      text: "返回列表",
      customStyle: {
        backgroundColor: "#D9001B"
      }
    })
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/article/article-detail.js.map
