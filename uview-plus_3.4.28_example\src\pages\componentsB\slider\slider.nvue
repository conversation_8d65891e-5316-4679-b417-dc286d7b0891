<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基本案例</text>
				<view class="u-page__slide-item">
				<up-slider
					:useNative="useNative"
					v-model="value1"
				></up-slider>
			</view>
			<up-button text="前进" @click="value1 = value1 + 1"></up-button>
		</view>
			<view class="u-demo-block">
			<text class="u-demo-block__title">自定义范围(10—50)</text>
			<view class="u-page__slide-item">
				<up-slider
					:useNative="useNative"
				    v-model="value2"
					showValue
				    min="10"
				    max="50"
				></up-slider>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">指定步长(每次步进5)</text>
			<view class="u-page__slide-item">
				<up-slider
					:useNative="useNative"
				    v-model="value4"
				    :step="5"
				></up-slider>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义样式</text>
			<view class="u-page__slide-item">
				<up-slider
				    v-model="value5"
				    activeColor="#deab8a"
				    blockColor="#f47920"
					height="20px"
				></up-slider>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义样式(图片)</text>
			<view class="u-page__slide-item">
				<up-slider
				    v-model="value5"
				    activeColor="#deab8a"
				    blockColor="#f47920"
					height="4px"
				>
					<template #default>
						<view>
							<svg t="1722094047017" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11063" width="32" height="32"><path d="M965.12 469.333333c-81.493333-90.88-230.186667-149.333333-378.453333-149.333333h-6.826667a193.28 193.28 0 0 0-289.493333-109.226667 188.373333 188.373333 0 0 0-51.2 47.36 42.666667 42.666667 0 0 0-2.773334 45.653334 42.666667 42.666667 0 0 0 42.666667 21.333333A149.333333 149.333333 0 0 1 384 355.626667l-16.426667 6.4A42.666667 42.666667 0 0 0 341.333333 401.28v221.44A42.666667 42.666667 0 0 0 367.36 661.333333l16.64 7.466667a150.4 150.4 0 0 1-106.666667 30.506667 42.666667 42.666667 0 0 0-42.666666 21.333333 42.666667 42.666667 0 0 0 2.773333 45.866667 187.946667 187.946667 0 0 0 51.2 47.36 194.56 194.56 0 0 0 103.893333 29.653333A192 192 0 0 0 580.053333 704h6.613334c149.333333 0 296.96-58.666667 378.453333-149.333333a64 64 0 0 0 0-85.333334z m-535.68-130.773333a192 192 0 0 0-155.946667-55.04 146.133333 146.133333 0 0 1 39.68-36.693333 152.533333 152.533333 0 0 1 176.213334 10.24 149.333333 149.333333 0 0 1 46.293333 65.28 615.04 615.04 0 0 0-104.746667 18.346666 20.053333 20.053333 0 0 0-1.493333-2.133333zM489.173333 768a152.32 152.32 0 0 1-176.213333 10.24 135.893333 135.893333 0 0 1-38.826667-36.266667 192 192 0 0 0 155.093334-55.466666 21.333333 21.333333 0 0 0 2.133333-3.84 615.466667 615.466667 0 0 0 104.533333 18.346666A149.333333 149.333333 0 0 1 489.173333 768z m444.16-242.133333C859.52 608.213333 723.413333 661.333333 586.666667 661.333333a546.773333 546.773333 0 0 1-202.666667-38.613333V401.28A549.76 549.76 0 0 1 586.666667 362.666667c136.746667 0 272.853333 53.12 346.666666 135.466666a21.333333 21.333333 0 0 1 0 27.733334z" fill="#CE4141" p-id="11064"></path><path d="M682.666667 426.666667a85.333333 85.333333 0 1 0 85.333333 85.333333 85.333333 85.333333 0 0 0-85.333333-85.333333z m0 128a42.666667 42.666667 0 1 1 42.666666-42.666667 42.666667 42.666667 0 0 1-42.666666 42.666667zM128 448h149.333333a21.333333 21.333333 0 0 0 0-42.666667H128a21.333333 21.333333 0 0 0 0 42.666667zM298.666667 597.333333a21.333333 21.333333 0 0 0-21.333334-21.333333H192a21.333333 21.333333 0 0 0 0 42.666667h85.333333a21.333333 21.333333 0 0 0 21.333334-21.333334zM298.666667 512a21.333333 21.333333 0 0 0-21.333334-21.333333H64a21.333333 21.333333 0 0 0 0 42.666666h213.333333a21.333333 21.333333 0 0 0 21.333334-21.333333z" fill="#CE4141" p-id="11065"></path><path d="M448 426.666667m-21.333333 0a21.333333 21.333333 0 1 0 42.666666 0 21.333333 21.333333 0 1 0-42.666666 0Z" fill="#CE4141" p-id="11066"></path><path d="M448 512m-21.333333 0a21.333333 21.333333 0 1 0 42.666666 0 21.333333 21.333333 0 1 0-42.666666 0Z" fill="#CE4141" p-id="11067"></path><path d="M448 597.333333m-21.333333 0a21.333333 21.333333 0 1 0 42.666666 0 21.333333 21.333333 0 1 0-42.666666 0Z" fill="#CE4141" p-id="11068"></path></svg>
						</view>
					</template>
				</up-slider>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">区间选择(双滑块)</text>
			<view class="u-page__slide-item">
				<up-slider
					isRange
					showValue
					step="2"
				    v-model:rangeValue="value6"
					height="2px"
				></up-slider>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">在Modal弹窗中使用</text>
			<view class="u-page__slide-item">
				<up-text @click="modelShow = true" text="打开弹窗"></up-text>
				<up-modal v-model:show="modelShow">
				    <view class="slot-content" style="width: 100%">
				      <up-slider v-if="modelShow" v-model="sliderValue" min="1" max="4" showValue></up-slider>
				    </view>
				</up-modal>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">在popup弹窗中使用</text>
			<view class="u-page__slide-item">
				<up-text @click="popupShow = true" text="打开弹窗"></up-text>
				<up-popup v-model:show="popupShow">
				    <view class="slot-content" style="width: 100%">
				      <up-slider v-if="popupShow" v-model="sliderValue" min="1" max="4" showValue></up-slider>
				    </view>
				</up-popup>
			</view>
		</view>
	</view>
</template>

<script setup> 
import { ref } from "vue";
const modelShow = ref(false);
const popupShow = ref(false);
const sliderValue = ref(4);
</script>

<script>
	export default {
		data() {
			return {
				useNative: false,
				value1: 30,
				value2: 30,
				value3: 30,
				value4: 30,
				value5: 30,
				value6: [10, 20]
			}
		},
		watch: {
			value1(n) {
				// console.log(n);
			}
		},
		methods: {
			moving(value) {
				// console.log('moving', value)
			},
			click(value) {
				// console.log('click', value)
			},
			end(value) {
				// console.log('end', value)
			},
			start(value) {
				// console.log('start', value)
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		
	}
</style>
