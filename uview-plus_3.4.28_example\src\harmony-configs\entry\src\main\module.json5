{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ets", "description": "$string:EntryAbility_desc", "icon": "$media:layered_image", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:startIcon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.APPROXIMATELY_LOCATION", "reason": "$string:location_tips", "usedScene": {}}, {"name": "ohos.permission.LOCATION", "reason": "$string:location_tips", "usedScene": {}}, {"name": "ohos.permission.GET_WIFI_INFO", "reason": "$string:get_networkinfo_tips", "usedScene": {"when": "inuse"}}, {"name": "ohos.permission.GET_NETWORK_INFO", "reason": "$string:get_wifiinfo_tips", "usedScene": {"when": "inuse"}}, {"name": "ohos.permission.MICROPHONE", "reason": "$string:microphone", "usedScene": {"when": "inuse"}}, {"name": "ohos.permission.ACCESS_BIOMETRIC", "reason": "$string:access_biometric", "usedScene": {"when": "inuse"}}]}}