{"version": 3, "file": "index.js", "sources": ["pages/index/bottom/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvYm90dG9tL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"common-container bottom-layout\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar :title=\"parentMenuName\" :showBack=\"true\" :showHome=\"true\" @leftClick=\"goBack\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 顶部标签页 -->\n\t\t<view class=\"tabs-container\">\n\t\t\t<u-tabs\n\t\t\t\tv-if=\"tabList && tabList.length > 0\"\n\t\t\t\t:list=\"tabList\"\n\t\t\t\tv-model:current=\"current\"\n\t\t\t\t@change=\"onTabChange\"\n\t\t\t\tlineColor=\"#D9001B\"\n\t\t\t\tlineWidth=\"30\"\n\t\t\t\titemStyle=\"padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;\"\n\t\t\t\t:activeStyle=\"{\n\t\t\t\t\tcolor: '#D9001B',\n\t\t\t\t\tfontWeight: 'bold',\n\t\t\t\t\tfontSize: '28rpx'\n\t\t\t\t}\"\n\t\t\t\t:inactiveStyle=\"{\n\t\t\t\t\tcolor: '#333333',\n\t\t\t\t\tfontSize: '28rpx'\n\t\t\t\t}\"\n\t\t\t></u-tabs>\n\t\t</view>\n\n\t\t<!-- 内容区域 -->\n\t\t<view class=\"content-container\">\n\t\t\t<view v-if=\"articleList.length > 0\">\n\t\t\t\t<view\n\t\t\t\t\tclass=\"article-item\"\n\t\t\t\t\tv-for=\"(item, index) in articleList\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t@click=\"goToArticleDetail(item)\"\n\t\t\t\t>\n\t\t\t\t\t<view class=\"article-content\">\n\t\t\t\t\t\t<view class=\"article-title u-line-2\">{{ item.title }}</view>\n\t\t\t\t\t\t<view class=\"article-summary u-line-2\" v-if=\"item.content\">\n\t\t\t\t\t\t\t{{ getPlainTextSummary(item.content) }}\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"article-info\">\n\t\t\t\t\t\t\t<text class=\"article-date\">{{ formatDate(item.createdAt) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"article-image\" v-if=\"item.coverImage\">\n\t\t\t\t\t\t<image :src=\"item.coverImage\" mode=\"aspectFill\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else class=\"empty-container\">\n\t\t\t\t<u-empty mode=\"data\" text=\"暂无数据\"></u-empty>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { menuApi, menuArticleApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\timport { fetchImageById, getFullImageUrl } from '@/utils/image.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tparentMenuId: null, // 父级菜单ID\n\t\t\t\tparentMenuName: '组织工作', // 父级菜单名称\n\t\t\t\ttabList: [], // 标签列表\n\t\t\t\tcurrent: 0, // 当前选中的标签索引\n\t\t\t\tarticleList: [], // 文章列表\n\t\t\t\tstatusBarHeight: 20 // 状态栏高度，默认值\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算顶部安全区域的高度\n\t\t\tsafeAreaTop() {\n\t\t\t\treturn this.statusBarHeight + 45; // 状态栏高度 + 导航栏高度(90rpx转为px的近似值)\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取状态栏高度\n\t\t\tthis.getStatusBarHeight();\n\n\t\t\t// 获取父级菜单ID\n\t\t\tif (options.id) {\n\t\t\t\tthis.parentMenuId = Number(options.id);\n\t\t\t\tthis.loadSubMenus();\n\t\t\t}\n\n\t\t\t// 如果有菜单名称，则设置\n\t\t\tif (options.name) {\n\t\t\t\tthis.parentMenuName = decodeURIComponent(options.name);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取状态栏高度\n\t\t\tgetStatusBarHeight() {\n\t\t\t\ttry {\n\t\t\t\t\t// 使用uni-app提供的API获取窗口信息\n\t\t\t\t\tconst windowInfo = uni.getWindowInfo();\n\t\t\t\t\tthis.statusBarHeight = windowInfo.statusBarHeight || 20;\n\n\t\t\t\t\tconsole.log('状态栏高度:', this.statusBarHeight);\n\t\t\t\t\t// 在小程序中不能直接设置CSS变量，我们使用计算属性来处理\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取状态栏高度失败:', e);\n\t\t\t\t\t// 设置默认值\n\t\t\t\t\tthis.statusBarHeight = 20;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack();\n\t\t\t},\n\n\t\t\t// 加载子菜单\n\t\t\tasync loadSubMenus() {\n\t\t\t\ttry {\n\t\t\t\t\t// 获取所有菜单\n\t\t\t\t\tconst result = await menuApi.getMenuList();\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\t// 过滤出指定父ID的二级菜单\n\t\t\t\t\t\tconst subMenus = result.data.filter(item => item.parentId === this.parentMenuId);\n\n\t\t\t\t\t\t// 转换为标签格式\n\t\t\t\t\t\tthis.tabList = subMenus.map(item => {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tname: item.name,\n\t\t\t\t\t\t\t\tid: item.id\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 如果有子菜单，加载第一个子菜单的文章\n\t\t\t\t\t\tif (this.tabList.length > 0) {\n\t\t\t\t\t\t\t// 设置当前索引为0，通过双向绑定会自动触发change事件\n\t\t\t\t\t\t\tthis.current = 0;\n\t\t\t\t\t\t\t// 直接加载第一个子菜单的文章\n\t\t\t\t\t\t\tthis.loadArticles(this.tabList[0].id);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取子菜单列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取菜单失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 标签切换事件\n\t\t\tonTabChange(tabItem) {\n\t\t\t\t// 使用tabItem对象，它包含index和其他属性\n\t\t\t\tif (tabItem && tabItem.id) {\n\t\t\t\t\tthis.loadArticles(tabItem.id);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载文章列表\n\t\t\tasync loadArticles(menuId) {\n\t\t\t\ttry {\n\t\t\t\t\t// 使用新的API接口获取菜单文章\n\t\t\t\t\tconst result = await menuArticleApi.getMenuArticles(menuId);\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\t// 处理文章列表数据\n\t\t\t\t\t\tconst articles = result.data;\n\n\t\t\t\t\t\t// 只处理每篇文章的封面图\n\t\t\t\t\t\tfor (let article of articles) {\n\t\t\t\t\t\t\t// 处理封面图\n\t\t\t\t\t\t\tif (article.coverImageId && !article.coverImage) {\n\t\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\t\tconst imageData = await fetchImageById(article.coverImageId);\n\t\t\t\t\t\t\t\t\tif (imageData && imageData.url) {\n\t\t\t\t\t\t\t\t\t\tarticle.coverImage = getFullImageUrl(imageData.url);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t} catch (err) {\n\t\t\t\t\t\t\t\t\tconsole.error('获取文章封面图失败:', err);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthis.articleList = articles;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.articleList = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取菜单文章列表失败:', error);\n\t\t\t\t\tthis.articleList = [];\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取文章失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取纯文本摘要\n\t\t\tgetPlainTextSummary(htmlContent) {\n\t\t\t\t// 移除HTML标签\n\t\t\t\tconst plainText = htmlContent.replace(/<[^>]+>/g, '');\n\t\t\t\t// 移除多余空格和换行\n\t\t\t\tconst trimmedText = plainText.replace(/\\s+/g, ' ').trim();\n\t\t\t\t// 截取前100个字符作为摘要\n\t\t\t\treturn trimmedText.length > 100 ? trimmedText.substring(0, 100) + '...' : trimmedText;\n\t\t\t},\n\n\t\t\t// 格式化日期\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '';\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\tconst year = date.getFullYear();\n\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\treturn `${year}-${month}-${day}`;\n\t\t\t},\n\n\t\t\t// 跳转到文章详情\n\t\t\tgoToArticleDetail(article) {\n\t\t\t\t// 跳转到统一的文章详情页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/article/detail?id=${article.id}&type=menu`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t.tabs-container {\n\t\tbackground-color: #FFF1F0;\n\t\tpadding: 15rpx 20rpx;\n\t\tposition: fixed;\n\t\ttop: 85px; /* 导航栏高度 + 额外空间，减少间距 */\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 998;\n\t\t// border-bottom: 1px solid #f0f0f0;\n\t}\n\n\t/* 使用全局样式文件中的.navbar-right */\n\n\t.content-container {\n\t\tflex: 1;\n\t\tpadding: 15rpx;\n\t\tmargin-top: 40px; /* 减少顶部间距，使布局更紧凑 */\n\n\t\t.article-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\tbackground-color: #FFFFFF;\n\t\t\tpadding: 20rpx;\n\t\t\tmargin-bottom: 15rpx;\n\t\t\tborder-radius: 4rpx;\n\n\t\t\t.article-content {\n\t\t\t\tflex: 1;\n\t\t\t\tmargin-right: 20rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tmin-height: 120rpx; /* 与图片高度一致 */\n\n\t\t\t\t.article-title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t}\n\n\t\t\t\t.article-summary {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #666666;\n\t\t\t\t\tmargin-bottom: 10rpx;\n\t\t\t\t\tline-height: 1.4;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t}\n\n\t\t\t\t.article-info {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-top: auto; /* 推到底部 */\n\n\t\t\t\t\t.article-date {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.article-image {\n\t\t\t\twidth: 180rpx;\n\t\t\t\theight: 120rpx;\n\t\t\t\tborder-radius: 4rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t\tflex-shrink: 0;\n\t\t\t\talign-self: center; /* 垂直居中 */\n\n\t\t\t\timage {\n\t\t\t\t\twidth: 100%;\n\t\t\t\t\theight: 100%;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.empty-container {\n\t\t\tpadding: 100rpx 0;\n\t\t}\n\t}\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/index/bottom/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "menuApi.getMenuList", "menuArticleApi.getMenuArticles", "fetchImageById", "getFullImageUrl"], "mappings": ";;;;;AAiEC,MAAK,eAAgB,MAAW;AAGhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,cAAc;AAAA;AAAA,MACd,gBAAgB;AAAA;AAAA,MAChB,SAAS,CAAE;AAAA;AAAA,MACX,SAAS;AAAA;AAAA,MACT,aAAa,CAAE;AAAA;AAAA,MACf,iBAAiB;AAAA;AAAA,IAClB;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,cAAc;AACb,aAAO,KAAK,kBAAkB;AAAA,IAC/B;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,SAAK,mBAAkB;AAGvB,QAAI,QAAQ,IAAI;AACf,WAAK,eAAe,OAAO,QAAQ,EAAE;AACrC,WAAK,aAAY;AAAA,IAClB;AAGA,QAAI,QAAQ,MAAM;AACjB,WAAK,iBAAiB,mBAAmB,QAAQ,IAAI;AAAA,IACtD;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB;AACpB,UAAI;AAEH,cAAM,aAAaA,oBAAI;AACvB,aAAK,kBAAkB,WAAW,mBAAmB;AAErDA,sBAAA,MAAA,MAAA,OAAA,uCAAY,UAAU,KAAK,eAAe;AAAA,MAE3C,SAAS,GAAG;AACXA,kFAAc,cAAc,CAAC;AAE7B,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA;AAAA,IAGD,SAAS;AACRA,oBAAG,MAAC,aAAY;AAAA,IAChB;AAAA;AAAA,IAGD,MAAM,eAAe;AACpB,UAAI;AAEH,cAAM,SAAS,MAAMC,UAAAA;AACrB,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAE3D,gBAAM,WAAW,OAAO,KAAK,OAAO,UAAQ,KAAK,aAAa,KAAK,YAAY;AAG/E,eAAK,UAAU,SAAS,IAAI,UAAQ;AACnC,mBAAO;AAAA,cACN,MAAM,KAAK;AAAA,cACX,IAAI,KAAK;AAAA;UAEX,CAAC;AAGD,cAAI,KAAK,QAAQ,SAAS,GAAG;AAE5B,iBAAK,UAAU;AAEf,iBAAK,aAAa,KAAK,QAAQ,CAAC,EAAE,EAAE;AAAA,UACrC;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfD,sBAAA,MAAA,MAAA,SAAA,uCAAc,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,SAAS;AAEpB,UAAI,WAAW,QAAQ,IAAI;AAC1B,aAAK,aAAa,QAAQ,EAAE;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,aAAa,QAAQ;AAC1B,UAAI;AAEH,cAAM,SAAS,MAAME,iCAA+B,MAAM;AAC1D,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAE3D,gBAAM,WAAW,OAAO;AAGxB,mBAAS,WAAW,UAAU;AAE7B,gBAAI,QAAQ,gBAAgB,CAAC,QAAQ,YAAY;AAChD,kBAAI;AACH,sBAAM,YAAY,MAAMC,YAAAA,eAAe,QAAQ,YAAY;AAC3D,oBAAI,aAAa,UAAU,KAAK;AAC/B,0BAAQ,aAAaC,YAAAA,gBAAgB,UAAU,GAAG;AAAA,gBACnD;AAAA,cACC,SAAO,KAAK;AACbJ,8BAAA,MAAA,MAAA,SAAA,uCAAc,cAAc,GAAG;AAAA,cAChC;AAAA,YACD;AAAA,UACD;AAEA,eAAK,cAAc;AAAA,eACb;AACN,eAAK,cAAc;QACpB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,eAAe,KAAK;AAClC,aAAK,cAAc;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,oBAAoB,aAAa;AAEhC,YAAM,YAAY,YAAY,QAAQ,YAAY,EAAE;AAEpD,YAAM,cAAc,UAAU,QAAQ,QAAQ,GAAG,EAAE;AAEnD,aAAO,YAAY,SAAS,MAAM,YAAY,UAAU,GAAG,GAAG,IAAI,QAAQ;AAAA,IAC1E;AAAA;AAAA,IAGD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK;AAClB,YAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,YAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,aAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,kBAAkB,SAAS;AAE1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,QAAQ,EAAE;AAAA,QAC3C,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,uCAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/OD,GAAG,WAAW,eAAe;"}