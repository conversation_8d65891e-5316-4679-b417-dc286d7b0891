{"version": 3, "file": "test.js", "sources": ["uview-plus_3.4.28/libs/function/test.js"], "sourcesContent": ["/**\r\n * 验证电子邮箱格式\r\n */\r\nexport function email(value) {\r\n    return /^\\w+((-\\w+)|(\\.\\w+))*\\@[A-Za-z0-9]+((\\.|-)[A-Za-z0-9]+)*\\.[A-Za-z0-9]+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证手机格式\r\n */\r\nexport function mobile(value) {\r\n    return /^1[23456789]\\d{9}$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证URL格式\r\n */\r\nexport function url(value) {\r\n    return /^((https|http|ftp|rtsp|mms):\\/\\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\\/?)|(\\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\\/?)$/\r\n        .test(value)\r\n}\r\n\r\n/**\r\n * 验证日期格式\r\n * @param {number | string} value yyyy-mm-dd hh:mm:ss 或 时间戳\r\n */\r\nexport function date(value) {\r\n    if (!value) return false;\r\n    // number类型，判断是否是时间戳\r\n    if (typeof value === \"number\") {\r\n        // len === 10 秒级时间戳 len === 13 毫秒级时间戳\r\n        if (value.toString().length !== 10 && value.toString().length !== 13) {\r\n            return false;\r\n        }\r\n        return !isNaN(new Date(value).getTime());\r\n    }\r\n    if (typeof value === \"string\") {\r\n        // 是否为string类型时间戳\r\n        const numV = Number(value);\r\n        if (!isNaN(numV)) {\r\n            if (\r\n                numV.toString().length === 10 ||\r\n                numV.toString().length === 13\r\n            ) {\r\n                return !isNaN(new Date(numV).getTime());\r\n            }\r\n        }\r\n        // 非时间戳，且长度在yyyy-mm-dd 至 yyyy-mm-dd hh:mm:ss 之间\r\n        if (value.length < 10 || value.length > 19) {\r\n            return false;\r\n        }\r\n        const dateRegex =\r\n            /^\\d{4}[-\\/]\\d{2}[-\\/]\\d{2}( \\d{1,2}:\\d{2}(:\\d{2})?)?$/;\r\n        if (!dateRegex.test(value)) {\r\n            return false;\r\n        }\r\n        // 检查是否为有效日期\r\n        const dateValue = new Date(value);\r\n        return !isNaN(dateValue.getTime());\r\n    }\r\n    // 非number和string类型，不做校验\r\n    return false;\r\n}\r\n\r\n/**\r\n * 验证ISO类型的日期格式\r\n */\r\nexport function dateISO(value) {\r\n    return /^\\d{4}[\\/\\-](0?[1-9]|1[012])[\\/\\-](0?[1-9]|[12][0-9]|3[01])$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证十进制数字\r\n */\r\nexport function number(value) {\r\n    return /^[\\+-]?(\\d+\\.?\\d*|\\.\\d+|\\d\\.\\d+e\\+\\d+)$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证字符串\r\n */\r\nexport function string(value) {\r\n    return typeof value === 'string'\r\n}\r\n\r\n/**\r\n * 验证整数\r\n */\r\nexport function digits(value) {\r\n    return /^\\d+$/.test(value)\r\n}\r\n\r\n/**\r\n * 验证身份证号码\r\n */\r\nexport function idCard(value) {\r\n    return /^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$/.test(\r\n        value\r\n    )\r\n}\r\n\r\n/**\r\n * 是否车牌号\r\n */\r\nexport function carNo(value) {\r\n    // 新能源车牌\r\n    const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/\r\n    // 旧车牌\r\n    const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/\r\n    if (value.length === 7) {\r\n        return creg.test(value)\r\n    } if (value.length === 8) {\r\n        return xreg.test(value)\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 金额,只允许2位小数\r\n */\r\nexport function amount(value) {\r\n    // 金额，只允许保留两位小数\r\n    return /^[1-9]\\d*(,\\d{3})*(\\.\\d{1,2})?$|^0\\.\\d{1,2}$/.test(value)\r\n}\r\n\r\n/**\r\n * 中文\r\n */\r\nexport function chinese(value) {\r\n    const reg = /^[\\u4e00-\\u9fa5]+$/gi\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 只能输入字母\r\n */\r\nexport function letter(value) {\r\n    return /^[a-zA-Z]*$/.test(value)\r\n}\r\n\r\n/**\r\n * 只能是字母或者数字\r\n */\r\nexport function enOrNum(value) {\r\n    // 英文或者数字\r\n    const reg = /^[0-9a-zA-Z]*$/g\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 验证是否包含某个值\r\n */\r\nexport function contains(value, param) {\r\n    return value.indexOf(param) >= 0\r\n}\r\n\r\n/**\r\n * 验证一个值范围[min, max]\r\n */\r\nexport function range(value, param) {\r\n    return value >= param[0] && value <= param[1]\r\n}\r\n\r\n/**\r\n * 验证一个长度范围[min, max]\r\n */\r\nexport function rangeLength(value, param) {\r\n    return value.length >= param[0] && value.length <= param[1]\r\n}\r\n\r\n/**\r\n * 是否固定电话\r\n */\r\nexport function landline(value) {\r\n    const reg = /^\\d{3,4}-\\d{7,8}(-\\d{3,4})?$/\r\n    return reg.test(value)\r\n}\r\n\r\n/**\r\n * 判断是否为空\r\n */\r\nexport function empty(value) {\r\n    switch (typeof value) {\r\n    case 'undefined':\r\n        return true\r\n    case 'string':\r\n        if (value.replace(/(^[ \\t\\n\\r]*)|([ \\t\\n\\r]*$)/g, '').length == 0) return true\r\n        break\r\n    case 'boolean':\r\n        if (!value) return true\r\n        break\r\n    case 'number':\r\n        if (value === 0 || isNaN(value)) return true\r\n        break\r\n    case 'object':\r\n        if (value === null || value.length === 0) return true\r\n        for (const i in value) {\r\n            return false\r\n        }\r\n        return true\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否json字符串\r\n */\r\nexport function jsonString(value) {\r\n    if (typeof value === 'string') {\r\n        try {\r\n            const obj = JSON.parse(value)\r\n            if (typeof obj === 'object' && obj) {\r\n                return true\r\n            }\r\n            return false\r\n        } catch (e) {\r\n            return false\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\n/**\r\n * 是否数组\r\n */\r\nexport function array(value) {\r\n    if (typeof Array.isArray === 'function') {\r\n        return Array.isArray(value)\r\n    }\r\n    return Object.prototype.toString.call(value) === '[object Array]'\r\n}\r\n\r\n/**\r\n * 是否对象\r\n */\r\nexport function object(value) {\r\n    return Object.prototype.toString.call(value) === '[object Object]'\r\n}\r\n\r\n/**\r\n * 是否短信验证码\r\n */\r\nexport function code(value, len = 6) {\r\n    return new RegExp(`^\\\\d{${len}}$`).test(value)\r\n}\r\n\r\n/**\r\n * 是否函数方法\r\n * @param {Object} value\r\n */\r\nexport function func(value) {\r\n    return typeof value === 'function'\r\n}\r\n\r\n/**\r\n * 是否promise对象\r\n * @param {Object} value\r\n */\r\nexport function promise(value) {\r\n    return object(value) && func(value.then) && func(value.catch)\r\n}\r\n\r\n/** 是否图片格式\r\n * @param {Object} value\r\n */\r\nexport function image(value) {\r\n    const newValue = value.split('?')[0]\r\n    const IMAGE_REGEXP = /\\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i\r\n    return IMAGE_REGEXP.test(newValue)\r\n}\r\n\r\n/**\r\n * 是否视频格式\r\n * @param {Object} value\r\n */\r\nexport function video(value) {\r\n    const VIDEO_REGEXP = /\\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i\r\n    return VIDEO_REGEXP.test(value)\r\n}\r\n\r\n/**\r\n * 是否为正则对象\r\n * @param {Object}\r\n * @return {Boolean}\r\n */\r\nexport function regExp(o) {\r\n    return o && Object.prototype.toString.call(o) === '[object RegExp]'\r\n}\r\n\r\nexport default {\r\n    email,\r\n    mobile,\r\n    url,\r\n    date,\r\n    dateISO,\r\n    number,\r\n    digits,\r\n    idCard,\r\n    carNo,\r\n    amount,\r\n    chinese,\r\n    letter,\r\n    enOrNum,\r\n    contains,\r\n    range,\r\n    rangeLength,\r\n    empty,\r\n    isEmpty: empty,\r\n    jsonString,\r\n    landline,\r\n    object,\r\n    array,\r\n    code,\r\n    func,\r\n    promise,\r\n    video,\r\n    image,\r\n    regExp,\r\n    string\r\n}\r\n"], "names": [], "mappings": ";AAGO,SAAS,MAAM,OAAO;AACzB,SAAO,0EAA0E,KAAK,KAAK;AAC/F;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,qBAAqB,KAAK,KAAK;AAC1C;AAKO,SAAS,IAAI,OAAO;AACvB,SAAO,8QACF,KAAK,KAAK;AACnB;AAMO,SAAS,KAAK,OAAO;AACxB,MAAI,CAAC;AAAO,WAAO;AAEnB,MAAI,OAAO,UAAU,UAAU;AAE3B,QAAI,MAAM,SAAU,EAAC,WAAW,MAAM,MAAM,SAAQ,EAAG,WAAW,IAAI;AAClE,aAAO;AAAA,IACV;AACD,WAAO,CAAC,MAAM,IAAI,KAAK,KAAK,EAAE,QAAO,CAAE;AAAA,EAC1C;AACD,MAAI,OAAO,UAAU,UAAU;AAE3B,UAAM,OAAO,OAAO,KAAK;AACzB,QAAI,CAAC,MAAM,IAAI,GAAG;AACd,UACI,KAAK,WAAW,WAAW,MAC3B,KAAK,WAAW,WAAW,IAC7B;AACE,eAAO,CAAC,MAAM,IAAI,KAAK,IAAI,EAAE,QAAO,CAAE;AAAA,MACzC;AAAA,IACJ;AAED,QAAI,MAAM,SAAS,MAAM,MAAM,SAAS,IAAI;AACxC,aAAO;AAAA,IACV;AACD,UAAM,YACF;AACJ,QAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AACxB,aAAO;AAAA,IACV;AAED,UAAM,YAAY,IAAI,KAAK,KAAK;AAChC,WAAO,CAAC,MAAM,UAAU,QAAS,CAAA;AAAA,EACpC;AAED,SAAO;AACX;AAKO,SAAS,QAAQ,OAAO;AAC3B,SAAO,+DAA+D,KAAK,KAAK;AACpF;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,0CAA0C,KAAK,KAAK;AAC/D;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,OAAO,UAAU;AAC5B;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,QAAQ,KAAK,KAAK;AAC7B;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,2EAA2E;AAAA,IAC9E;AAAA,EACH;AACL;AAKO,SAAS,MAAM,OAAO;AAEzB,QAAM,OAAO;AAEb,QAAM,OAAO;AACb,MAAI,MAAM,WAAW,GAAG;AACpB,WAAO,KAAK,KAAK,KAAK;AAAA,EAC9B;AAAM,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,KAAK,KAAK,KAAK;AAAA,EACzB;AACD,SAAO;AACX;AAKO,SAAS,OAAO,OAAO;AAE1B,SAAO,+CAA+C,KAAK,KAAK;AACpE;AAKO,SAAS,QAAQ,OAAO;AAC3B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,cAAc,KAAK,KAAK;AACnC;AAKO,SAAS,QAAQ,OAAO;AAE3B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,SAAS,OAAO,OAAO;AACnC,SAAO,MAAM,QAAQ,KAAK,KAAK;AACnC;AAKO,SAAS,MAAM,OAAO,OAAO;AAChC,SAAO,SAAS,MAAM,CAAC,KAAK,SAAS,MAAM,CAAC;AAChD;AAKO,SAAS,YAAY,OAAO,OAAO;AACtC,SAAO,MAAM,UAAU,MAAM,CAAC,KAAK,MAAM,UAAU,MAAM,CAAC;AAC9D;AAKO,SAAS,SAAS,OAAO;AAC5B,QAAM,MAAM;AACZ,SAAO,IAAI,KAAK,KAAK;AACzB;AAKO,SAAS,MAAM,OAAO;AACzB,UAAQ,OAAO,OAAK;AAAA,IACpB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,UAAI,MAAM,QAAQ,gCAAgC,EAAE,EAAE,UAAU;AAAG,eAAO;AAC1E;AAAA,IACJ,KAAK;AACD,UAAI,CAAC;AAAO,eAAO;AACnB;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,KAAK,MAAM,KAAK;AAAG,eAAO;AACxC;AAAA,IACJ,KAAK;AACD,UAAI,UAAU,QAAQ,MAAM,WAAW;AAAG,eAAO;AACjD,iBAAW,KAAK,OAAO;AACnB,eAAO;AAAA,MACV;AACD,aAAO;AAAA,EACV;AACD,SAAO;AACX;AAKO,SAAS,WAAW,OAAO;AAC9B,MAAI,OAAO,UAAU,UAAU;AAC3B,QAAI;AACA,YAAM,MAAM,KAAK,MAAM,KAAK;AAC5B,UAAI,OAAO,QAAQ,YAAY,KAAK;AAChC,eAAO;AAAA,MACV;AACD,aAAO;AAAA,IACV,SAAQ,GAAG;AACR,aAAO;AAAA,IACV;AAAA,EACJ;AACD,SAAO;AACX;AAKO,SAAS,MAAM,OAAO;AACzB,MAAI,OAAO,MAAM,YAAY,YAAY;AACrC,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC7B;AACD,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKO,SAAS,OAAO,OAAO;AAC1B,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACrD;AAKO,SAAS,KAAK,OAAO,MAAM,GAAG;AACjC,SAAO,IAAI,OAAO,QAAQ,GAAG,IAAI,EAAE,KAAK,KAAK;AACjD;AAMO,SAAS,KAAK,OAAO;AACxB,SAAO,OAAO,UAAU;AAC5B;AAMO,SAAS,QAAQ,OAAO;AAC3B,SAAO,OAAO,KAAK,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM,KAAK;AAChE;AAKO,SAAS,MAAM,OAAO;AACzB,QAAM,WAAW,MAAM,MAAM,GAAG,EAAE,CAAC;AACnC,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,QAAQ;AACrC;AAMO,SAAS,MAAM,OAAO;AACzB,QAAM,eAAe;AACrB,SAAO,aAAa,KAAK,KAAK;AAClC;AAOO,SAAS,OAAO,GAAG;AACtB,SAAO,KAAK,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AACtD;AAEA,MAAe,OAAA;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;;;"}