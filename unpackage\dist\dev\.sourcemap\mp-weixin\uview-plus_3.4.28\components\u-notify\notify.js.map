{"version": 3, "file": "notify.js", "sources": ["uview-plus_3.4.28/components/u-notify/notify.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:10:21\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/notify.js\n */\nexport default {\n    // notify组件\n    notify: {\n        top: 0,\n        type: 'primary',\n        color: '#ffffff',\n        bgColor: '',\n        message: '',\n        duration: 3000,\n        fontSize: 15,\n        safeAreaInsetTop: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,UAAU;AAAA,IACV,UAAU;AAAA,IACV,kBAAkB;AAAA,EACrB;AACL;;"}