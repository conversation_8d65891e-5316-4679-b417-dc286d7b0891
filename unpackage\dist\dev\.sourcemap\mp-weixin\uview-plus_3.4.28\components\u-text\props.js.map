{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-text/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 主题颜色\n        type: {\n            type: String,\n            default: () => defProps.text.type\n        },\n        // 是否显示\n        show: {\n            type: Boolean,\n            default: () => defProps.text.show\n        },\n        // 显示的值\n        text: {\n            type: [String, Number],\n            default: () => defProps.text.text\n        },\n        // 前置图标\n        prefixIcon: {\n            type: String,\n            default: () => defProps.text.prefixIcon\n        },\n        // 后置图标\n        suffixIcon: {\n            type: String,\n            default: () => defProps.text.suffixIcon\n        },\n        // 文本处理的匹配模式\n        // text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\n        mode: {\n            type: String,\n            default: () => defProps.text.mode\n        },\n        // mode=link下，配置的链接\n        href: {\n            type: String,\n            default: () => defProps.text.href\n        },\n        // 格式化规则\n        format: {\n            type: [String, Function],\n            default: () => defProps.text.format\n        },\n        // mode=phone时，点击文本是否拨打电话\n        call: {\n            type: Boolean,\n            default: () => defProps.text.call\n        },\n        // 小程序的打开方式\n        openType: {\n            type: String,\n            default: () => defProps.text.openType\n        },\n        // 是否粗体，默认normal\n        bold: {\n            type: Boolean,\n            default: () => defProps.text.bold\n        },\n        // 是否块状\n        block: {\n            type: Boolean,\n            default: () => defProps.text.block\n        },\n        // 文本显示的行数，如果设置，超出此行数，将会显示省略号\n        lines: {\n            type: [String, Number],\n            default: () => defProps.text.lines\n        },\n        // 文本颜色\n        color: {\n            type: String,\n            default: () => defProps.text.color\n        },\n        // 字体大小\n        size: {\n            type: [String, Number],\n            default: () => defProps.text.size\n        },\n        // 图标的样式\n        iconStyle: {\n            type: [Object, String],\n            default: () => defProps.text.iconStyle\n        },\n        // 文字装饰，下划线，中划线等，可选值 none|underline|line-through\n        decoration: {\n            tepe: String,\n            default: () => defProps.text.decoration\n        },\n        // 外边距，对象、字符串，数值形式均可\n        margin: {\n            type: [Object, String, Number],\n            default: () => defProps.text.margin\n        },\n        // 文本行高\n        lineHeight: {\n            type: [String, Number],\n            default: () => defProps.text.lineHeight\n        },\n        // 文本对齐方式，可选值left|center|right\n        align: {\n            type: String,\n            default: () => defProps.text.align\n        },\n        // 文字换行，可选值break-word|normal|anywhere\n        wordWrap: {\n            type: String,\n            default: () => defProps.text.wordWrap\n        },\n\t\t// 占满剩余空间\n\t\tflex1: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.text.flex1\n\t\t}\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,QAAQ;AAAA,MACvB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,QAAQ,MAAM;AAAA,MAC7B,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAEP,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAC7B;AAAA,EACE;AACL,CAAC;;"}