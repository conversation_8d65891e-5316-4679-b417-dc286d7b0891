{"version": 3, "file": "navbar.js", "sources": ["uview-plus_3.4.28/components/u-navbar/navbar.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:16:18\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/navbar.js\n */\nimport color from '../../libs/config/color'\nexport default {\n    // navbar 组件\n    navbar: {\n        safeAreaInsetTop: true,\n        placeholder: false,\n        fixed: true,\n        border: false,\n        leftIcon: 'arrow-left',\n        leftText: '',\n        rightText: '',\n        rightIcon: '',\n        title: '',\n        titleColor: '',\n        bgColor: '#ffffff',\n        titleWidth: '400rpx',\n        height: '44px',\n\t\tleftIconSize: 20,\n\t\tleftIconColor: color.mainColor,\n\t\tautoBack: false,\n\t\ttitleStyle: ''\n    }\n\n}\n"], "names": ["color"], "mappings": ";;AAUA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,QAAQ;AAAA,IACd,cAAc;AAAA,IACd,eAAeA,mCAAK,MAAC;AAAA,IACrB,UAAU;AAAA,IACV,YAAY;AAAA,EACT;AAEL;;"}