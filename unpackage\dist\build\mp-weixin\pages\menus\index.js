"use strict";const t=require("../../common/vendor.js"),e=require("../../api/categories.js"),o=require("../../api/menus.js"),i=require("../../api/menu-articles.js"),r=require("../../utils/image.js"),n={components:{CustomNavbar:()=>"../../components/common/custom-navbar.js"},data:()=>({parentMenuId:null,parentMenuName:"组织工作",tabList:[],current:0,articleList:[],statusBarHeight:20,showPartyComponent:!1,categoryList:[],categoryHistory:[],currentLevel:1,currentTitle:"基层党组织"}),computed:{safeAreaTop(){return this.statusBarHeight+45}},onLoad(t){if(this.getStatusBarHeight(),t.id){this.parentMenuId=Number(t.id);const e=t.subMenuId?Number(t.subMenuId):null;this.loadSubMenus(e)}t.name&&(this.parentMenuName=decodeURIComponent(t.name))},methods:{getStatusBarHeight(){try{const e=t.index.getWindowInfo();this.statusBarHeight=e.statusBarHeight||20,console.log("状态栏高度:",this.statusBarHeight)}catch(e){console.error("获取状态栏高度失败:",e),this.statusBarHeight=20}},async loadSubMenus(e=null){try{const t=await o.getMenuList();if(t&&t.success&&Array.isArray(t.data)){const o=t.data.filter((t=>t.parentId===this.parentMenuId));if(this.tabList=o.map((t=>({name:t.name,id:t.id}))),this.tabList.length>0){if(e){const t=this.tabList.findIndex((t=>t.id===e));if(-1!==t)return this.current=t,void this.loadArticles(e)}this.current=0,this.loadArticles(this.tabList[0].id)}}}catch(i){console.error("获取子菜单列表失败:",i),t.index.showToast({title:"获取菜单失败",icon:"none"})}},onTabChange(t){t&&t.id&&this.loadArticles(t.id)},async loadArticles(e){const o=this.tabList.find((t=>t.id===e));if(o&&"组织架构"===o.name)return this.showPartyComponent=!0,void this.fetchCategoryList();this.showPartyComponent=!1;try{const t=await i.getMenuArticles(e);if(t&&t.success&&Array.isArray(t.data)){const e=t.data;for(let t of e)if(t.coverImageId&&!t.coverImage)try{const e=await r.fetchImageById(t.coverImageId);e&&e.url&&(t.coverImage=r.getFullImageUrl(e.url))}catch(n){console.error("获取文章封面图失败:",n)}this.articleList=e}else this.articleList=[]}catch(s){console.error("获取菜单文章列表失败:",s),this.articleList=[],t.index.showToast({title:"获取文章失败",icon:"none"})}},getPlainTextSummary(t){const e=t.replace(/<[^>]+>/g,"").replace(/\s+/g," ").trim();return e.length>100?e.substring(0,100)+"...":e},formatDate(t){if(!t)return"";const e=new Date(t);return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`},goToArticleDetail(e){t.index.navigateTo({url:`/pages/article/detail?id=${e.id}&type=menu`,fail:e=>{console.error("跳转失败:",e),t.index.showToast({title:"跳转失败",icon:"none"})}})},async fetchCategoryList(){try{t.index.showLoading({title:"加载中..."});const o=await e.getCategoryTreeList();console.log("获取分类树形列表成功:",o),o&&o.success&&Array.isArray(o.data)&&(this.categoryList=o.data,console.log("分类树形列表数据已更新"))}catch(o){console.error("获取分类树形列表失败:",o),t.index.showToast({title:"获取分类数据失败",icon:"none"})}finally{t.index.hideLoading()}},onCategoryClick(e){const o=this.categoryList[e];console.log("点击了基层党组织:",o),o.children&&o.children.length>0?(this.categoryHistory.push({list:[...this.categoryList],title:this.currentTitle,level:this.currentLevel}),this.categoryList=o.children,this.currentTitle=o.name,this.currentLevel++):t.index.navigateTo({url:`/pages/party/category-articles/index?id=${o.id}`,fail:e=>{console.error("跳转失败:",e),t.index.showToast({title:"跳转失败",icon:"none"})}})},goBackToParent(){if(this.categoryHistory.length>0){const t=this.categoryHistory.pop();return this.categoryList=t.list,this.currentTitle=t.title,this.currentLevel=t.level,!0}return!1}}};if(!Array){(t.resolveComponent("u-icon")+t.resolveComponent("custom-navbar")+t.resolveComponent("u-tabs")+t.resolveComponent("u-cell")+t.resolveComponent("u-list")+t.resolveComponent("u-button")+t.resolveComponent("u-empty"))()}Math||((()=>"../../components/u-icon/u-icon.js")+(()=>"../../uview-plus_3.4.28/components/u-tabs/u-tabs.js")+(()=>"../../uview-plus_3.4.28/components/u-cell/u-cell.js")+(()=>"../../uview-plus_3.4.28/components/u-list/u-list.js")+(()=>"../../uview-plus_3.4.28/components/u-button/u-button.js")+(()=>"../../uview-plus_3.4.28/components/u-empty/u-empty.js"))();const s=t._export_sfc(n,[["render",function(e,o,i,r,n,s){return t.e({a:t.p({name:"more-dot-fill",color:"#FFFFFF",size:"20"}),b:t.p({title:n.parentMenuName,showBack:!0,showHome:!0}),c:n.tabList&&n.tabList.length>0},n.tabList&&n.tabList.length>0?{d:t.o(s.onTabChange),e:t.o((t=>n.current=t)),f:t.p({list:n.tabList,lineColor:"#D9001B",lineWidth:"30",itemStyle:"padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;",activeStyle:{color:"#D9001B",fontWeight:"bold",fontSize:"28rpx"},inactiveStyle:{color:"#333333",fontSize:"28rpx"},current:n.current})}:{},{g:n.showPartyComponent},n.showPartyComponent?t.e({h:t.p({name:"grid-fill",color:"#D9001B",size:"20"}),i:t.t(n.currentTitle),j:t.f(n.categoryList,((e,o,i)=>({a:o,b:t.o((t=>s.onCategoryClick(o)),o),c:"6dc8f1b5-5-"+i+",6dc8f1b5-4",d:t.p({title:e.name,titleStyle:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},isLink:!0})}))),k:t.p({"enable-flex":!0}),l:n.categoryHistory.length>0},n.categoryHistory.length>0?{m:t.o(s.goBackToParent),n:t.p({type:"primary",text:"返回上一级",customStyle:{backgroundColor:"#D9001B"}})}:{}):t.e({o:n.articleList.length>0},n.articleList.length>0?{p:t.f(n.articleList,((e,o,i)=>t.e({a:t.t(e.title),b:e.content},e.content?{c:t.t(s.getPlainTextSummary(e.content))}:{},{d:t.t(s.formatDate(e.createdAt)),e:e.coverImage},e.coverImage?{f:e.coverImage}:{},{g:o,h:t.o((t=>s.goToArticleDetail(e)),o)})))}:{q:t.p({mode:"data",text:"暂无数据"})}))}]]);wx.createPage(s);
