"use strict";const e=require("../../common/vendor.js"),t={name:"custom-navbar",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!0},showHome:{type:Boolean,default:!1},rightIcon:{type:String,default:""},rightText:{type:String,default:""},autoBack:{type:<PERSON>olean,default:!0}},data:()=>({statusBarHeight:20}),created(){this.getStatusBarHeight()},methods:{getStatusBarHeight(){try{const t=e.index.getWindowInfo();this.statusBarHeight=t.statusBarHeight||20}catch(t){console.error("获取状态栏高度失败:",t),this.statusBarHeight=20}},onBackClick(){this.$emit("leftClick");getCurrentPages().length<=1?e.index.switchTab({url:"/pages/index/index"}):e.index.navigateBack({delta:1,fail:()=>{e.index.switchTab({url:"/pages/index/index"})}})},onHomeClick(){this.$emit("homeClick"),e.index.navigateTo({url:"/pages/index/index",fail:t=>{console.error("跳转到首页失败:",t),e.index.reLaunch({url:"/pages/index/index",fail:t=>{console.error("reLaunch跳转到首页也失败:",t),e.index.showToast({title:"跳转到首页失败",icon:"none"})}})}})},onRightClick(){this.$emit("rightClick")}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-line"))()}Math||((()=>"../u-icon/u-icon.js")+(()=>"../../uview-plus_3.4.28/components/u-line/u-line.js"))();const o=e._export_sfc(t,[["render",function(t,o,i,n,a,r){return e.e({a:a.statusBarHeight+"px",b:i.showBack},i.showBack?{c:e.p({name:"arrow-left",color:"#FFFFFF",size:"20"}),d:e.o(((...e)=>r.onBackClick&&r.onBackClick(...e)))}:{},{e:i.showBack&&i.showHome},i.showBack&&i.showHome?{f:e.p({direction:"column",hairline:!1,length:"16",color:"#FFFFFF",width:"1"})}:{},{g:i.showHome},i.showHome?{h:e.p({name:"home",color:"#FFFFFF",size:"20"}),i:e.o(((...e)=>r.onHomeClick&&r.onHomeClick(...e)))}:{},{j:e.t(i.title),k:i.rightIcon},i.rightIcon?{l:e.p({name:i.rightIcon,color:"#FFFFFF",size:"20"})}:{},{m:i.rightText},i.rightText?{n:e.t(i.rightText)}:{},{o:e.o(((...e)=>r.onRightClick&&r.onRightClick(...e)))})}]]);wx.createComponent(o);
