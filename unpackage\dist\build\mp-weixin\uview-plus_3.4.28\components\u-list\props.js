"use strict";const e=require("../../libs/vue.js"),r=require("../../libs/config/props.js"),l=e.defineMixin({props:{showScrollbar:{type:Bo<PERSON>an,default:()=>r.props.list.showScrollbar},lowerThreshold:{type:[String,Number],default:()=>r.props.list.lowerThreshold},upperThreshold:{type:[String,Number],default:()=>r.props.list.upperThreshold},scrollTop:{type:[String,Number],default:()=>r.props.list.scrollTop},offsetAccuracy:{type:[String,Number],default:()=>r.props.list.offsetAccuracy},enableFlex:{type:Boolean,default:()=>r.props.list.enableFlex},pagingEnabled:{type:Boolean,default:()=>r.props.list.pagingEnabled},scrollable:{type:<PERSON><PERSON><PERSON>,default:()=>r.props.list.scrollable},scrollIntoView:{type:String,default:()=>r.props.list.scrollIntoView},scrollWithAnimation:{type:<PERSON>olean,default:()=>r.props.list.scrollWithAnimation},enableBackToTop:{type:Boolean,default:()=>r.props.list.enableBackToTop},height:{type:[String,Number],default:()=>r.props.list.height},width:{type:[String,Number],default:()=>r.props.list.width},preLoadScreen:{type:[String,Number],default:()=>r.props.list.preLoadScreen},refresherEnabled:{type:Boolean,default:()=>!1},refresherThreshold:{type:Number,default:()=>45},refresherDefaultStyle:{type:String,default:()=>"black"},refresherBackground:{type:String,default:()=>"#FFF"},refresherTriggered:{type:Boolean,default:()=>!1}}});exports.props=l;
