{"version": 3, "file": "colorGradient.js", "sources": ["uview-plus_3.4.28/libs/function/colorGradient.js"], "sourcesContent": ["/**\r\n * 求两个颜色之间的渐变值\r\n * @param {string} startColor 开始的颜色\r\n * @param {string} endColor 结束的颜色\r\n * @param {number} step 颜色等分的份额\r\n * */\r\nexport function colorGradient(startColor = 'rgb(0, 0, 0)', endColor = 'rgb(255, 255, 255)', step = 10) {\r\n    const startRGB = hexToRgb(startColor, false) // 转换为rgb数组模式\r\n    const startR = startRGB[0]\r\n    const startG = startRGB[1]\r\n    const startB = startRGB[2]\r\n\r\n    const endRGB = hexToRgb(endColor, false)\r\n    const endR = endRGB[0]\r\n    const endG = endRGB[1]\r\n    const endB = endRGB[2]\r\n\r\n    const sR = (endR - startR) / step // 总差值\r\n    const sG = (endG - startG) / step\r\n    const sB = (endB - startB) / step\r\n    const colorArr = []\r\n    for (let i = 0; i < step; i++) {\r\n        // 计算每一步的hex值\r\n        let hex = rgbToHex(`rgb(${Math.round((sR * i + startR))},${Math.round((sG * i + startG))},${Math.round((sB\r\n\t\t\t* i + startB))})`)\r\n        // 确保第一个颜色值为startColor的值\r\n        if (i === 0) hex = rgbToHex(startColor)\r\n        // 确保最后一个颜色值为endColor的值\r\n        if (i === step - 1) hex = rgbToHex(endColor)\r\n        colorArr.push(hex)\r\n    }\r\n    return colorArr\r\n}\r\n\r\n// 将hex表示方式转换为rgb表示方式(这里返回rgb数组模式)\r\nexport function hexToRgb(sColor, str = true) {\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    sColor = String(sColor).toLowerCase()\r\n    if (sColor && reg.test(sColor)) {\r\n        if (sColor.length === 4) {\r\n            let sColorNew = '#'\r\n            for (let i = 1; i < 4; i += 1) {\r\n                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n            }\r\n            sColor = sColorNew\r\n        }\r\n        // 处理六位的颜色值\r\n        const sColorChange = []\r\n        for (let i = 1; i < 7; i += 2) {\r\n            sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))\r\n        }\r\n        if (!str) {\r\n            return sColorChange\r\n        }\r\n        return `rgb(${sColorChange[0]},${sColorChange[1]},${sColorChange[2]})`\r\n    } if (/^(rgb|RGB)/.test(sColor)) {\r\n        const arr = sColor.replace(/(?:\\(|\\)|rgb|RGB)*/g, '').split(',')\r\n        return arr.map((val) => Number(val))\r\n    }\r\n    return sColor\r\n}\r\n\r\n// 将rgb表示方式转换为hex表示方式\r\nexport function rgbToHex(rgb) {\r\n    const _this = rgb\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    if (/^(rgb|RGB)/.test(_this)) {\r\n        const aColor = _this.replace(/(?:\\(|\\)|rgb|RGB)*/g, '').split(',')\r\n        let strHex = '#'\r\n        for (let i = 0; i < aColor.length; i++) {\r\n            let hex = Number(aColor[i]).toString(16)\r\n            hex = String(hex).length == 1 ? `${0}${hex}` : hex // 保证每个rgb的值为2位\r\n            if (hex === '0') {\r\n                hex += hex\r\n            }\r\n            strHex += hex\r\n        }\r\n        if (strHex.length !== 7) {\r\n            strHex = _this\r\n        }\r\n        return strHex\r\n    } if (reg.test(_this)) {\r\n        const aNum = _this.replace(/#/, '').split('')\r\n        if (aNum.length === 6) {\r\n            return _this\r\n        } if (aNum.length === 3) {\r\n            let numHex = '#'\r\n            for (let i = 0; i < aNum.length; i += 1) {\r\n                numHex += (aNum[i] + aNum[i])\r\n            }\r\n            return numHex\r\n        }\r\n    } else {\r\n        return _this\r\n    }\r\n}\r\n\r\n/**\r\n* JS颜色十六进制转换为rgb或rgba,返回的格式为 rgba（255，255，255，0.5）字符串\r\n* sHex为传入的十六进制的色值\r\n* alpha为rgba的透明度\r\n*/\r\nexport function colorToRgba(color, alpha) {\r\n    color = rgbToHex(color)\r\n    // 十六进制颜色值的正则表达式\r\n    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/\r\n    /* 16进制颜色转为RGB格式 */\r\n    let sColor = String(color).toLowerCase()\r\n    if (sColor && reg.test(sColor)) {\r\n        if (sColor.length === 4) {\r\n            let sColorNew = '#'\r\n            for (let i = 1; i < 4; i += 1) {\r\n                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1))\r\n            }\r\n            sColor = sColorNew\r\n        }\r\n        // 处理六位的颜色值\r\n        const sColorChange = []\r\n        for (let i = 1; i < 7; i += 2) {\r\n            sColorChange.push(parseInt(`0x${sColor.slice(i, i + 2)}`))\r\n        }\r\n        // return sColorChange.join(',')\r\n        return `rgba(${sColorChange.join(',')},${alpha})`\r\n    }\r\n\r\n    return sColor\r\n}\r\n\r\nexport default {\r\n    colorGradient,\r\n    hexToRgb,\r\n    rgbToHex,\r\n    colorToRgba\r\n}\r\n"], "names": [], "mappings": ";AAMO,SAAS,cAAc,aAAa,gBAAgB,WAAW,sBAAsB,OAAO,IAAI;AACnG,QAAM,WAAW,SAAS,YAAY,KAAK;AAC3C,QAAM,SAAS,SAAS,CAAC;AACzB,QAAM,SAAS,SAAS,CAAC;AACzB,QAAM,SAAS,SAAS,CAAC;AAEzB,QAAM,SAAS,SAAS,UAAU,KAAK;AACvC,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AACrB,QAAM,OAAO,OAAO,CAAC;AAErB,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,MAAM,OAAO,UAAU;AAC7B,QAAM,WAAW,CAAE;AACnB,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAE3B,QAAI,MAAM,SAAS,OAAO,KAAK,MAAO,KAAK,IAAI,MAAQ,CAAA,IAAI,KAAK,MAAO,KAAK,IAAI,MAAM,CAAE,IAAI,KAAK,MAAO,KAC3G,IAAI,OAAQ,GAAG;AAEZ,QAAI,MAAM;AAAG,YAAM,SAAS,UAAU;AAEtC,QAAI,MAAM,OAAO;AAAG,YAAM,SAAS,QAAQ;AAC3C,aAAS,KAAK,GAAG;AAAA,EACpB;AACD,SAAO;AACX;AAGO,SAAS,SAAS,QAAQ,MAAM,MAAM;AACzC,QAAM,MAAM;AACZ,WAAS,OAAO,MAAM,EAAE,YAAa;AACrC,MAAI,UAAU,IAAI,KAAK,MAAM,GAAG;AAC5B,QAAI,OAAO,WAAW,GAAG;AACrB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,qBAAa,OAAO,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MACpE;AACD,eAAS;AAAA,IACZ;AAED,UAAM,eAAe,CAAE;AACvB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,mBAAa,KAAK,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,IAC5D;AACD,QAAI,CAAC,KAAK;AACN,aAAO;AAAA,IACV;AACD,WAAO,OAAO,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC;AAAA,EACtE;AAAC,MAAI,aAAa,KAAK,MAAM,GAAG;AAC7B,UAAM,MAAM,OAAO,QAAQ,uBAAuB,EAAE,EAAE,MAAM,GAAG;AAC/D,WAAO,IAAI,IAAI,CAAC,QAAQ,OAAO,GAAG,CAAC;AAAA,EACtC;AACD,SAAO;AACX;AAGO,SAAS,SAAS,KAAK;AAC1B,QAAM,QAAQ;AACd,QAAM,MAAM;AACZ,MAAI,aAAa,KAAK,KAAK,GAAG;AAC1B,UAAM,SAAS,MAAM,QAAQ,uBAAuB,EAAE,EAAE,MAAM,GAAG;AACjE,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,MAAM,OAAO,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE;AACvC,YAAM,OAAO,GAAG,EAAE,UAAU,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK;AAC/C,UAAI,QAAQ,KAAK;AACb,eAAO;AAAA,MACV;AACD,gBAAU;AAAA,IACb;AACD,QAAI,OAAO,WAAW,GAAG;AACrB,eAAS;AAAA,IACZ;AACD,WAAO;AAAA,EACV;AAAC,MAAI,IAAI,KAAK,KAAK,GAAG;AACnB,UAAM,OAAO,MAAM,QAAQ,KAAK,EAAE,EAAE,MAAM,EAAE;AAC5C,QAAI,KAAK,WAAW,GAAG;AACnB,aAAO;AAAA,IACnB;AAAU,QAAI,KAAK,WAAW,GAAG;AACrB,UAAI,SAAS;AACb,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK,GAAG;AACrC,kBAAW,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,MAC9B;AACD,aAAO;AAAA,IACV;AAAA,EACT,OAAW;AACH,WAAO;AAAA,EACV;AACL;AAOO,SAAS,YAAY,OAAO,OAAO;AACtC,UAAQ,SAAS,KAAK;AAEtB,QAAM,MAAM;AAEZ,MAAI,SAAS,OAAO,KAAK,EAAE,YAAa;AACxC,MAAI,UAAU,IAAI,KAAK,MAAM,GAAG;AAC5B,QAAI,OAAO,WAAW,GAAG;AACrB,UAAI,YAAY;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,qBAAa,OAAO,MAAM,GAAG,IAAI,CAAC,EAAE,OAAO,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,MACpE;AACD,eAAS;AAAA,IACZ;AAED,UAAM,eAAe,CAAE;AACvB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC3B,mBAAa,KAAK,SAAS,KAAK,OAAO,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC;AAAA,IAC5D;AAED,WAAO,QAAQ,aAAa,KAAK,GAAG,CAAC,IAAI,KAAK;AAAA,EACjD;AAED,SAAO;AACX;AAEA,MAAe,kBAAA;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;;;"}