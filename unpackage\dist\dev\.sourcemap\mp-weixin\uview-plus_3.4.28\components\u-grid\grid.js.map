{"version": 3, "file": "grid.js", "sources": ["uview-plus_3.4.28/components/u-grid/grid.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:05:57\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/grid.js\n */\nexport default {\n    // grid组件\n    grid: {\n        col: 3,\n        border: false,\n        align: 'left'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,OAAO;AAAA,EACV;AACL;;"}