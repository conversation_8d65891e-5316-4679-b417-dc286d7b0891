"use strict";const e=require("../../../common/vendor.js"),t=require("../../../api/categories.js"),o={components:{CustomNavbar:()=>"../../../components/common/custom-navbar.js"},data:()=>({categoryId:null,category:{},articleList:[]}),onLoad(t){t.id?(this.categoryId=t.id,console.log("分类ID:",this.categoryId),this.fetchCategoryArticles()):(e.index.showToast({title:"参数错误",icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500))},methods:{async fetchCategoryArticles(){try{e.index.showLoading({title:"加载中..."});const o=await t.getCategoryArticles(this.categoryId);console.log("获取分类文章列表成功:",o),o&&o.success&&(o.data.category&&(this.category=o.data.categoryId),Array.isArray(o.data)&&(this.articleList=o.data))}catch(o){console.error("获取分类文章列表失败:",o),e.index.showToast({title:"获取文章列表失败",icon:"none"})}finally{e.index.hideLoading()}},onArticleClick(t){console.log("点击了文章:",t),console.log("跳转到党建文章详情页"),e.index.navigateTo({url:`/pages/article/detail?id=${t.id}&type=party`,fail:t=>{console.error("跳转失败:",t),e.index.showToast({title:"跳转失败",icon:"none"})}})}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("custom-navbar")+e.resolveComponent("u-cell")+e.resolveComponent("u-list")+e.resolveComponent("u-empty")+e.resolveComponent("u-button"))()}Math||((()=>"../../../components/u-icon/u-icon.js")+(()=>"../../../uview-plus_3.4.28/components/u-cell/u-cell.js")+(()=>"../../../uview-plus_3.4.28/components/u-list/u-list.js")+(()=>"../../../uview-plus_3.4.28/components/u-empty/u-empty.js")+(()=>"../../../uview-plus_3.4.28/components/u-button/u-button.js"))();const i=e._export_sfc(o,[["render",function(t,o,i,n,s,a){return e.e({a:e.p({name:"more-dot-fill",color:"#FFFFFF",size:"20"}),b:e.p({title:s.category.name||"文章列表",showBack:!0,showHome:!0}),c:e.p({name:"list",color:"#D9001B",size:"20"}),d:e.f(s.articleList,((t,o,i)=>({a:o,b:e.o((e=>a.onArticleClick(t)),o),c:"feafa478-4-"+i+",feafa478-3",d:e.p({title:t.title,titleStyle:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},isLink:!0})}))),e:e.p({"enable-flex":!0}),f:0===s.articleList.length},0===s.articleList.length?{g:e.p({mode:"list",text:"暂无文章"})}:{},{h:e.o((()=>t.uni.navigateBack())),i:e.p({type:"primary",text:"返回上一页",customStyle:{backgroundColor:"#D9001B"}})})}]]);wx.createPage(i);
