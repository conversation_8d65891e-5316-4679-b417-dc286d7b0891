/* 全局样式文件 */

/* 页面容器基础样式 */
.common-container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-top: 200rpx; /* 为顶部导航栏留出更多空间 + 增加间距 */

  &.red-theme {
    background-color: #D9001B;
    padding-bottom: 120rpx; /* 为底部导航留出空间 */
  }

  &.bottom-layout {
    display: flex;
    flex-direction: column;
    background-color: #F5F5F5;
  }
}

/* 导航栏右侧区域 */
.navbar-right {
  display: flex;
  align-items: center;
}

/* 内容区块样式 */
.content-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

/* 区块标题样式 */
.section-title {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;

  text {
    font-size: 30rpx;
    font-weight: bold;
    color: #333333;
    margin-left: 10rpx;
  }
}

/* 区块内容样式 */
.section-content {
  padding: 10rpx 0;
}

/* 底部操作栏 */
.article-footer, .party-footer, .category-footer {
  padding: 20rpx 0;
  display: flex;
  justify-content: center;
}

/* 文章头部样式 */
.article-header {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 文章标题样式 */
.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

/* 文章元信息样式 */
.article-meta {
  font-size: 24rpx;
  color: #999999;

  view {
    margin-bottom: 8rpx;
  }
}

/* 文章内容样式 */
.article-content {
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  min-height: 300rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  rich-text {
    font-size: 30rpx;
    line-height: 1.6;
    color: #333333;
  }
}

/* 党建头部样式 */
.party-header {
  background-color: #D9001B;
  padding: 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10rpx;
  }

  .header-desc {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
  }
}
