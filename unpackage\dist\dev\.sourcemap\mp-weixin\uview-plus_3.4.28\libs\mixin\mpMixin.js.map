{"version": 3, "file": "mpMixin.js", "sources": ["uview-plus_3.4.28/libs/mixin/mpMixin.js"], "sourcesContent": ["import { defineMixin } from '../vue'\n\nexport const mpMixin = defineMixin({\n    // #ifdef MP-WEIXIN\n    // 将自定义节点设置成虚拟的，更加接近Vue组件的表现，能更好的使用flex属性\n    options: {\n        virtualHost: true\n    }\n    // #endif\n})\n\nexport default mpMixin\n\n"], "names": ["defineMixin"], "mappings": ";;AAEY,MAAC,UAAUA,0BAAAA,YAAY;AAAA;AAAA,EAG/B,SAAS;AAAA,IACL,aAAa;AAAA,EAChB;AAEL,CAAC;;"}