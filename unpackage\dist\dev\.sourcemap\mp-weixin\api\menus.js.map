{"version": 3, "file": "menus.js", "sources": ["api/menus.js"], "sourcesContent": ["import request from './request.js';\n\n/**\n * 获取菜单列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getMenuList(params = {}) {\n  return request.get('/api/menus/', params);\n}\n\n/**\n * 获取子菜单列表\n * @param {String|Number} parentId - 父菜单ID\n * @returns {Promise} - 返回Promise对象\n */\nexport function getSubMenus(parentId) {\n  return request.get(`/api/menus/?parentId=${parentId}`);\n}\n"], "names": ["request"], "mappings": ";;AAOO,SAAS,YAAY,SAAS,IAAI;AACvC,SAAOA,oBAAQ,IAAI,eAAe,MAAM;AAC1C;;"}