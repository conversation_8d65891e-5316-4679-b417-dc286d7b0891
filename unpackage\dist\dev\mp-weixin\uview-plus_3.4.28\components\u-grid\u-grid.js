"use strict";
const uviewPlus_3_4_28_components_uGrid_props = require("./props.js");
const uviewPlus_3_4_28_libs_mixin_mpMixin = require("../../libs/mixin/mpMixin.js");
const uviewPlus_3_4_28_libs_mixin_mixin = require("../../libs/mixin/mixin.js");
const uviewPlus_3_4_28_libs_function_index = require("../../libs/function/index.js");
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  name: "u-grid",
  mixins: [uviewPlus_3_4_28_libs_mixin_mpMixin.mpMixin, uviewPlus_3_4_28_libs_mixin_mixin.mixin, uviewPlus_3_4_28_components_uGrid_props.props],
  data() {
    return {
      index: 0,
      width: 0
    };
  },
  watch: {
    // 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件
    parentData() {
      if (this.children.length) {
        this.children.map((child) => {
          typeof child.updateParentData == "function" && child.updateParentData();
        });
      }
    }
  },
  created() {
    this.children = [];
  },
  computed: {
    // 计算父组件的值是否发生变化
    parentData() {
      return [this.hoverClass, this.col, this.size, this.border];
    },
    // 宫格对齐方式
    gridStyle() {
      let style = {};
      switch (this.align) {
        case "left":
          style.justifyContent = "flex-start";
          break;
        case "center":
          style.justifyContent = "center";
          break;
        case "right":
          style.justifyContent = "flex-end";
          break;
        default:
          style.justifyContent = "flex-start";
      }
      return uviewPlus_3_4_28_libs_function_index.deepMerge(style, uviewPlus_3_4_28_libs_function_index.addStyle(this.customStyle));
    }
  },
  emits: ["click"],
  // 防止事件执行两次
  // 20240409发现抖音小程序如果开启virtualHost会出现严重问题，几乎所有事件包括created等生命周期事件全部失效。
  options: {
    // virtualHost: true ,//将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等
  },
  methods: {
    // 此方法由u-grid-item触发，用于在u-grid发出事件
    childClick(name) {
      this.$emit("click", name);
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "35579a60": _ctx.gap,
    "35578d0a": _ctx.col
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.s($options.gridStyle),
    b: common_vendor.s(_ctx.__cssVars())
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b399388b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-grid/u-grid.js.map
