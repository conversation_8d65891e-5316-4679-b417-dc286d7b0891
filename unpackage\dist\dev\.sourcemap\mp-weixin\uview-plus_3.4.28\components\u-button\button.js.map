{"version": 3, "file": "button.js", "sources": ["uview-plus_3.4.28/components/u-button/button.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:51:27\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/button.js\n */\nexport default {\n    // button组件\n    button: {\n        hairline: false,\n        type: 'info',\n        size: 'normal',\n        shape: 'square',\n        plain: false,\n        disabled: false,\n        loading: false,\n        loadingText: '',\n        loadingMode: 'spinner',\n        loadingSize: 15,\n        openType: '',\n        formType: '',\n        appParameter: '',\n        hoverStopPropagation: true,\n        lang: 'en',\n        sessionFrom: '',\n        sendMessageTitle: '',\n        sendMessagePath: '',\n        sendMessageImg: '',\n        showMessageCard: false,\n        dataName: '',\n        throttleTime: 0,\n        hoverStartTime: 0,\n        hoverStayTime: 200,\n        text: '',\n        icon: '',\n        iconColor: '',\n        color: '',\n        stop: true,\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,UAAU;AAAA,IACV,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,OAAO;AAAA,IACP,UAAU;AAAA,IACV,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,sBAAsB;AAAA,IACtB,MAAM;AAAA,IACN,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,MAAM;AAAA,IACN,MAAM;AAAA,IACN,WAAW;AAAA,IACX,OAAO;AAAA,IACP,MAAM;AAAA,EACT;AACL;;"}