export default [
	{
		groupName: '部件',
		groupName_en: 'Parts',
		list: [
			{
				path: 'coupon',
				icon: 'coupon',
				title: 'Coupon 优惠券',
				title_en: 'Coupon',
			}
		]
	},
	{
		groupName: '页面',
		groupName_en: 'Page',
		list: [
			{
				path: '/pages/template/wxCenter/index',
				icon: 'wxCenter',
				title: 'WxCenter 仿微信个人中心',
				title_en: 'WxCenter',
			},
			// {
			// 	path: '/pages/template/douyin/index',
			// 	icon: 'douyin',
			// 	title: 'Douyin 仿抖音',
			// },
			{
				path: '/pages/template/keyboardPay/index',
				icon: 'keyboardPay',
				title: 'KeyboardPay 自定义键盘支付模板',
				title_en: 'KeyboardPay',
			},
			{
				path: '/pages/template/mallMenu/index1',
				icon: 'mall_menu_1',
				title: 'MallMenu 垂直分类(左右独立)',
				title_en: 'MallMenu 1',
			},{
				path: '/pages/template/mallMenu/index2',
				icon: 'mall_menu_2',
				title: 'MallMenu 垂直分类(左右联动)',
				title_en: 'MallMenu 2',
			},{
				path: 'submitBar',
				icon: 'submitBar',
				title: 'SubmitBar 提交订单栏',
				title_en: 'SubmitBar',
			},{
				path: 'comment',
				icon: 'comment',
				title: 'Comment 评论列表',
				title_en: 'Comment',
			},{
				path: 'order',
				icon: 'order',
				title: 'Order 订单列表',
				title_en: 'Order',
			},{
				path: 'login',
				icon: 'login',
				title: 'Login 登录界面',
				title_en: 'Login',
			},{
				path: 'address',
				icon: 'address',
				title: 'Address 收货地址',
				title_en: 'Address',
			},{
				path: 'citySelect',
				icon: 'citySelect',
				title: 'CitySelect 城市选择',
				title_en: 'CitySelect',
			}
		]
	}
]