"use strict";const e=require("../../../common/vendor.js"),o=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),c={name:"u-search",mixins:[i.mpMixin,t.mixin,o.props],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword(e){this.$emit("update:modelValue",e),this.$emit("change",e)},modelValue:{immediate:!0,handler(e){this.keyword=e}}},computed:{showActionBtn(){return!this.animation&&this.showAction}},emits:["clear","search","custom","focus","blur","click","clickIcon","update:modelValue","change"],methods:{addStyle:s.addStyle,addUnit:s.addUnit,inputChange(e){this.keyword=e.detail.value},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")}))},search(o){this.$emit("search",o.detail.value);try{e.index.hideKeyboard()}catch(i){}},custom(){this.$emit("custom",this.keyword);try{e.index.hideKeyboard()}catch(o){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(o){this.$emit("clickIcon",this.keyword);try{e.index.hideKeyboard()}catch(i){}}}};if(!Array){e.resolveComponent("u-icon")()}Math;const r=e._export_sfc(c,[["render",function(o,i,t,s,c,r){return e.e({a:o.$slots.label||null!==o.label},o.$slots.label||null!==o.label?{b:e.t(o.label)}:{},{c:e.o(r.clickIcon),d:e.p({size:o.searchIconSize,name:o.searchIcon,color:o.searchIconColor?o.searchIconColor:o.color}),e:e.o(((...e)=>r.blur&&r.blur(...e))),f:c.keyword,g:e.o(((...e)=>r.search&&r.search(...e))),h:e.o(((...e)=>r.inputChange&&r.inputChange(...e))),i:o.disabled,j:e.o(((...e)=>r.getFocus&&r.getFocus(...e))),k:o.focus,l:o.maxlength,m:o.adjustPosition,n:o.autoBlur,o:o.placeholder,p:`color: ${o.placeholderColor}`,q:e.s({pointerEvents:o.disabled?"none":"auto",textAlign:o.inputAlign,color:o.color,backgroundColor:o.bgColor,height:r.addUnit(o.height)}),r:e.s(o.inputStyle),s:c.keyword&&o.clearabled&&c.focused},c.keyword&&o.clearabled&&c.focused?{t:e.p({name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"}),v:e.o(((...e)=>r.clear&&r.clear(...e)))}:{},{w:o.bgColor,x:"round"==o.shape?"100px":"4px",y:o.borderColor,z:e.t(o.actionText),A:e.s(o.actionStyle),B:e.n((r.showActionBtn||c.show)&&"u-search__action--active"),C:e.o(((...e)=>r.custom&&r.custom(...e))),D:e.n("right"===o.iconPosition&&"u-search__reverse"),E:e.o(((...e)=>r.clickHandler&&r.clickHandler(...e))),F:e.s({margin:o.margin}),G:e.s(r.addStyle(o.customStyle))})}],["__scopeId","data-v-15132e0f"]]);wx.createComponent(r);
