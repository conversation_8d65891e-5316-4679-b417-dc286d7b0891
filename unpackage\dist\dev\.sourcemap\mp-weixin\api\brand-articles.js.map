{"version": 3, "file": "brand-articles.js", "sources": ["api/brand-articles.js"], "sourcesContent": ["import request from './request.js';\n\n/**\n * 获取品牌文章列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getBrandArticleList(params = {}) {\n  return request.get('/api/brand-articles/', params);\n}\n\n/**\n * 获取品牌文章详情\n * @param {String|Number} id - 文章ID\n * @returns {Promise} - 返回Promise对象\n */\nexport function getBrandArticleDetail(id) {\n  return request.get(`/api/brand-articles/${id}`);\n}\n"], "names": ["request"], "mappings": ";;AAgBO,SAAS,sBAAsB,IAAI;AACxC,SAAOA,YAAAA,QAAQ,IAAI,uBAAuB,EAAE,EAAE;AAChD;;"}