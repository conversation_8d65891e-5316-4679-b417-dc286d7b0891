{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-grid/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 分成几列\n        col: {\n            type: [String, Number],\n            default: () => defProps.grid.col\n        },\n        // 是否显示边框\n        border: {\n            type: Boolean,\n            default: () => defProps.grid.border\n        },\n        // 宫格对齐方式，表现为数量少的时候，靠左，居中，还是靠右\n        align: {\n            type: String,\n            default: () => defProps.grid.align\n        },\n        // 间隔\n        gap: {\n            type: String,\n            default: '0px'\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,EACJ;AACL,CAAC;;"}