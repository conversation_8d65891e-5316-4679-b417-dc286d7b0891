{"version": 3, "file": "u-image.js", "sources": ["uview-plus_3.4.28/components/u-image/u-image.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtaW1hZ2UvdS1pbWFnZS52dWU"], "sourcesContent": ["<template>\n\t<u-transition\n\t\tmode=\"fade\"\n\t\t:show=\"show\"\n\t\t:style=\"transStyle\"\n\t\t:duration=\"fade ? 1000 : 0\"\n\t>\n\t\t<view\n\t\t\tclass=\"u-image box-border\"\n\t\t\t@tap=\"onClick\"\n\t\t\t:style=\"[wrapStyle, backgroundStyle]\"\n\t\t>\n\t\t\t<image\n\t\t\t\tv-if=\"!isError\"\n\t\t\t\t:src=\"src\"\n\t\t\t\t:mode=\"mode\"\n\t\t\t\t@error=\"onErrorHandler\"\n\t\t\t\t@load=\"onLoadHandler\"\n\t\t\t\t:show-menu-by-longpress=\"showMenuByLongpress\"\n\t\t\t\t:lazy-load=\"lazyLoad\"\n\t\t\t\tclass=\"u-image__image\"\n\t\t\t\t:style=\"{\n\t\t\t\t\twidth: addUnit(width),\n\t\t\t\t\theight: addUnit(height),\n\t\t\t\t\tborderRadius: shape == 'circle' ? '10000px' : addUnit(radius)\n\t\t\t\t}\"\n\t\t\t></image>\n\t\t\t<view\n\t\t\t\tv-if=\"showLoading && loading\"\n\t\t\t\tclass=\"u-image__loading\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : addUnit(radius),\n\t\t\t\t\tbackgroundColor: this.bgColor,\n\t\t\t\t\twidth: addUnit(width),\n\t\t\t\t\theight: addUnit(height)\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<slot name=\"loading\">\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\t:name=\"loadingIcon\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t\t<view\n\t\t\t\tv-if=\"showError && isError && !loading\"\n\t\t\t\tclass=\"u-image__error\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : addUnit(radius)\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<slot name=\"error\">\n\t\t\t\t\t<u-icon\n\t\t\t\t\t\t:name=\"errorIcon\"\n\t\t\t\t\t></u-icon>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t</view>\n\t</u-transition>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * Image 图片\n\t * @description 此组件为uni-app的image组件的加强版，在继承了原有功能外，还支持淡入动画、加载中、加载失败提示、圆角值和形状等。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/image.html\n\t * @property {String}\t\t\tsrc \t\t\t\t图片地址\n\t * @property {String}\t\t\tmode \t\t\t\t裁剪模式，见官网说明 （默认 'aspectFill' ）\n\t * @property {String | Number}\twidth \t\t\t\t宽度，单位任意，如果为数值，则为px单位 （默认 '300' ）\n\t * @property {String | Number}\theight \t\t\t\t高度，单位任意，如果为数值，则为px单位 （默认 '225' ）\n\t * @property {String}\t\t\tshape \t\t\t\t图片形状，circle-圆形，square-方形 （默认 'square' ）\n\t * @property {String | Number}\tradius\t\t \t\t圆角值，单位任意，如果为数值，则为px单位 （默认 0 ）\n\t * @property {Boolean}\t\t\tlazyLoad\t\t\t是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效 （默认 true ）\n\t * @property {Boolean}\t\t\tshowMenuByLongpress\t是否开启长按图片显示识别小程序码菜单，仅微信小程序有效 （默认 true ）\n\t * @property {String}\t\t\tloadingIcon \t\t加载中的图标，或者小图片 （默认 'photo' ）\n\t * @property {String}\t\t\terrorIcon \t\t\t加载失败的图标，或者小图片 （默认 'error-circle' ）\n\t * @property {Boolean}\t\t\tshowLoading \t\t是否显示加载中的图标或者自定义的slot （默认 true ）\n\t * @property {Boolean}\t\t\tshowError \t\t\t是否显示加载错误的图标或者自定义的slot （默认 true ）\n\t * @property {Boolean}\t\t\tfade \t\t\t\t是否需要淡入效果 （默认 true ）\n\t * @property {Boolean}\t\t\twebp \t\t\t\t只支持网络资源，只对微信小程序有效 （默认 false ）\n\t * @property {String | Number}\tduration \t\t\t搭配fade参数的过渡时间，单位ms （默认 500 ）\n\t * @property {String}\t\t\tbgColor \t\t\t背景颜色，用于深色页面加载图片时，为了和背景色融合  (默认 '#f3f4f6' )\n\t * @property {Object}\t\t\tcustomStyle  \t\t定义需要用到的外部样式\n\t * @event {Function}\tclick\t点击图片时触发\n\t * @event {Function}\terror\t图片加载失败时触发\n\t * @event {Function} load 图片加载成功时触发\n\t * @example <u-image width=\"100%\" height=\"300px\" :src=\"src\"></u-image>\n\t */\n\texport default {\n\t\tname: 'u-image',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 图片是否加载错误，如果是，则显示错误占位图\n\t\t\t\tisError: false,\n\t\t\t\t// 初始化组件时，默认为加载中状态\n\t\t\t\tloading: true,\n\t\t\t\t// 不透明度，为了实现淡入淡出的效果\n\t\t\t\topacity: 1,\n\t\t\t\t// 过渡时间，因为props的值无法修改，故需要一个中间值\n\t\t\t\tdurationTime: this.duration,\n\t\t\t\t// 图片加载完成时，去掉背景颜色，因为如果是png图片，就会显示灰色的背景\n\t\t\t\tbackgroundStyle: {},\n\t\t\t\t// 用于fade模式的控制组件显示与否\n\t\t\t\tshow: false\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\tsrc: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(n) {\n\t\t\t\t\tif (!n) {\n\t\t\t\t\t\t// 如果传入null或者''，或者false，或者undefined，标记为错误状态\n\t\t\t\t\t\tthis.isError = true\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.isError = false;\n\t\t\t\t\t\tthis.loading = true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ttransStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// 通过调用addUnit()方法，如果有单位，如百分比，px单位等，直接返回，如果是纯粹的数值，则加上rpx单位\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tstyle.width = addUnit(this.width);\n\t\t\t\tstyle.height = addUnit(this.height);\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tif (this.loading || this.isError || this.width == '100%' || this.mode != 'heightFix') {\n\t\t\t\t\tstyle.width = addUnit(this.width);\n\t\t\t\t} else {\n\t\t\t\t\tstyle.width = 'fit-content';\n\t\t\t\t}\n\t\t\t\tif (this.loading || this.isError || this.height == '100%' || this.mode != 'widthFix') {\n\t\t\t\t\tstyle.height = addUnit(this.height);\n\t\t\t\t} else {\n\t\t\t\t\tstyle.height = 'fit-content';\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\treturn style;\n\t\t\t},\n\t\t\twrapStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\t// 通过调用addUnit()方法，如果有单位，如百分比，px单位等，直接返回，如果是纯粹的数值，则加上rpx单位\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tstyle.width = addUnit(this.width);\n\t\t\t\tstyle.height = addUnit(this.height);\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tif (this.loading || this.isError || this.width == '100%' || this.mode != 'heightFix') {\n\t\t\t\t\tstyle.width = addUnit(this.width);\n\t\t\t\t} else {\n\t\t\t\t\tstyle.width = 'fit-content';\n\t\t\t\t}\n\t\t\t\tif (this.loading || this.isError || this.height == '100%' || this.mode != 'widthFix') {\n\t\t\t\t\tstyle.height = addUnit(this.height);\n\t\t\t\t} else {\n\t\t\t\t\tstyle.height = 'fit-content';\n\t\t\t\t}\n\t\t\t\t// #endif\n\t\t\t\t// 如果是显示圆形，设置一个很多的半径值即可\n\t\t\t\tstyle.borderRadius = this.shape == 'circle' ? '10000px' : addUnit(this.radius)\n\t\t\t\t// 如果设置圆角，必须要有hidden，否则可能圆角无效\n\t\t\t\tstyle.overflow = this.radius > 0 ? 'hidden' : 'visible'\n\t\t\t\t// if (this.fade) {\n\t\t\t\t// \tstyle.opacity = this.opacity\n\t\t\t\t// \t// nvue下，这几个属性必须要分开写\n\t\t\t\t// \tstyle.transitionDuration = `${this.durationTime}ms`\n\t\t\t\t// \tstyle.transitionTimingFunction = 'ease-in-out'\n\t\t\t\t// \tstyle.transitionProperty = 'opacity'\n\t\t\t\t// }\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle));\n\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.show = true\n\t\t},\n\t\temits: ['click', 'error', 'load'],\n\t\tmethods: {\n\t\t\taddUnit,\n\t\t\t// 点击图片\n\t\t\tonClick(e) {\n\t\t\t\tthis.$emit('click', e)\n\t\t\t},\n\t\t\t// 图片加载失败\n\t\t\tonErrorHandler(err) {\n\t\t\t\tthis.loading = false\n\t\t\t\tthis.isError = true\n\t\t\t\tthis.$emit('error', err)\n\t\t\t},\n\t\t\t// 图片加载完成，标记loading结束\n\t\t\tonLoadHandler(event) {\n\t\t\t\tthis.loading = false\n\t\t\t\tthis.isError = false\n\t\t\t\tthis.$emit('load', event)\n\t\t\t\tthis.removeBgColor()\n\t\t\t\t// 如果不需要动画效果，就不执行下方代码，同时移除加载时的背景颜色\n\t\t\t\t// 否则无需fade效果时，png图片依然能看到下方的背景色\n\t\t\t\t// if (!this.fade) return this.removeBgColor();\n\t\t\t\t// // 原来opacity为1(不透明，是为了显示占位图)，改成0(透明，意味着该元素显示的是背景颜色，默认的灰色)，再改成1，是为了获得过渡效果\n\t\t\t\t// this.opacity = 0;\n\t\t\t\t// // 这里设置为0，是为了图片展示到背景全透明这个过程时间为0，延时之后延时之后重新设置为duration，是为了获得背景透明(灰色)\n\t\t\t\t// // 到图片展示的过程中的淡入效果\n\t\t\t\t// this.durationTime = 0;\n\t\t\t\t// // 延时50ms，否则在浏览器H5，过渡效果无效\n\t\t\t\t// setTimeout(() => {\n\t\t\t\t// \tthis.durationTime = this.duration;\n\t\t\t\t// \tthis.opacity = 1;\n\t\t\t\t// \tsetTimeout(() => {\n\t\t\t\t// \t\tthis.removeBgColor();\n\t\t\t\t// \t}, this.durationTime);\n\t\t\t\t// }, 50);\n\t\t\t},\n\t\t\t// 移除图片的背景色\n\t\t\tremoveBgColor() {\n\t\t\t\t// 淡入动画过渡完成后，将背景设置为透明色，否则png图片会看到灰色的背景\n\t\t\t\tthis.backgroundStyle = {\n\t\t\t\t\tbackgroundColor: this.bgColor || '#ffffff'\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import '../../libs/css/components.scss';\n\n\t$u-image-error-top:0px !default;\n\t$u-image-error-left:0px !default;\n\t$u-image-error-width:100% !default;\n\t$u-image-error-hight:100% !default;\n\t$u-image-error-background-color:$u-bg-color !default;\n\t$u-image-error-color:$u-tips-color !default;\n\t$u-image-error-font-size: 46rpx !default;\n\n\t.u-image {\n\t\tposition: relative;\n\t\ttransition: opacity 0.5s ease-in-out;\n\n\t\t&__image {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t}\n\n\t\t&__loading,\n\t\t&__error {\n\t\t\tposition: absolute;\n\t\t\ttop: $u-image-error-top;\n\t\t\tleft: $u-image-error-left;\n\t\t\twidth: $u-image-error-width;\n\t\t\theight: $u-image-error-hight;\n\t\t\t@include flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: center;\n\t\t\tbackground-color: $u-image-error-background-color;\n\t\t\tcolor: $u-image-error-color;\n\t\t\tfont-size: $u-image-error-font-size;\n\t\t}\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-image/u-image.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AA2FC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,8CAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,SAAS;AAAA;AAAA,MAET,cAAc,KAAK;AAAA;AAAA,MAEnB,iBAAiB,CAAE;AAAA;AAAA,MAEnB,MAAM;AAAA;EAEP;AAAA,EACD,OAAO;AAAA,IACN,KAAK;AAAA,MACJ,WAAW;AAAA,MACX,QAAQ,GAAG;AACV,YAAI,CAAC,GAAG;AAEP,eAAK,UAAU;AAAA,eACT;AACN,eAAK,UAAU;AACf,eAAK,UAAU;AAAA,QAChB;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,aAAa;AACZ,UAAI,QAAQ,CAAA;AAOZ,UAAI,KAAK,WAAW,KAAK,WAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,aAAa;AACrF,cAAM,QAAQC,qCAAAA,QAAQ,KAAK,KAAK;AAAA,aAC1B;AACN,cAAM,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,WAAW,KAAK,WAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,YAAY;AACrF,cAAM,SAASA,qCAAAA,QAAQ,KAAK,MAAM;AAAA,aAC5B;AACN,cAAM,SAAS;AAAA,MAChB;AAEA,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,UAAI,QAAQ,CAAA;AAOZ,UAAI,KAAK,WAAW,KAAK,WAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,aAAa;AACrF,cAAM,QAAQA,qCAAAA,QAAQ,KAAK,KAAK;AAAA,aAC1B;AACN,cAAM,QAAQ;AAAA,MACf;AACA,UAAI,KAAK,WAAW,KAAK,WAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,YAAY;AACrF,cAAM,SAASA,qCAAAA,QAAQ,KAAK,MAAM;AAAA,aAC5B;AACN,cAAM,SAAS;AAAA,MAChB;AAGA,YAAM,eAAe,KAAK,SAAS,WAAW,YAAYA,qCAAAA,QAAQ,KAAK,MAAM;AAE7E,YAAM,WAAW,KAAK,SAAS,IAAI,WAAW;AAQ9C,aAAOC,qCAAAA,UAAU,OAAOC,qCAAAA,SAAS,KAAK,WAAW,CAAC;AAAA,IAEnD;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,OAAO;AAAA,EACZ;AAAA,EACD,OAAO,CAAC,SAAS,SAAS,MAAM;AAAA,EAChC,SAAS;AAAA,IACR,SAAAF,qCAAO;AAAA;AAAA,IAEP,QAAQ,GAAG;AACV,WAAK,MAAM,SAAS,CAAC;AAAA,IACrB;AAAA;AAAA,IAED,eAAe,KAAK;AACnB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,MAAM,SAAS,GAAG;AAAA,IACvB;AAAA;AAAA,IAED,cAAc,OAAO;AACpB,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,MAAM,QAAQ,KAAK;AACxB,WAAK,cAAc;AAAA,IAiBnB;AAAA;AAAA,IAED,gBAAgB;AAEf,WAAK,kBAAkB;AAAA,QACtB,iBAAiB,KAAK,WAAW;AAAA;IAEnC;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjOF,GAAG,gBAAgB,SAAS;"}