{"version": 3, "file": "button.js", "sources": ["uview-plus_3.4.28/libs/mixin/button.js"], "sourcesContent": ["import { defineMixin } from '../vue'\r\n\r\nexport const buttonMixin = defineMixin({\r\n    props: {\r\n        lang: String,\r\n        sessionFrom: String,\r\n        sendMessageTitle: String,\r\n        sendMessagePath: String,\r\n        sendMessageImg: String,\r\n        showMessageCard: Boolean,\r\n        appParameter: String,\r\n        formType: String,\r\n        openType: String\r\n    }\r\n})\r\n\r\nexport default buttonMixin\r\n\r\n"], "names": ["defineMixin"], "mappings": ";;AAEY,MAAC,cAAcA,0BAAAA,YAAY;AAAA,EACnC,OAAO;AAAA,IACH,MAAM;AAAA,IACN,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,IACV,UAAU;AAAA,EACb;AACL,CAAC;;"}