"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),t=require("../../libs/mixin/mixin.js"),o=require("../../libs/function/index.js"),r=require("../../libs/function/colorGradient.js"),n=require("../../../common/vendor.js"),d={name:"u-loading-icon",mixins:[i.mpMixin,t.mixin,e.props],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const e=r.colorGradient$1(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){}},mounted(){this.init()},methods:{addUnit:o.addUnit,addStyle:o.addStyle,init(){setTimeout((()=>{}),20)},addEventListenerToWebview(){const e=getCurrentPages(),i=e[e.length-1].$getAppWebview();i.addEventListener("hide",(()=>{this.webviewHide=!0})),i.addEventListener("show",(()=>{this.webviewHide=!1}))}}};const s=n._export_sfc(d,[["render",function(e,i,t,o,r,d){return n.e({a:e.show},e.show?n.e({b:!r.webviewHide},r.webviewHide?{}:n.e({c:"spinner"===e.mode},"spinner"===e.mode?{d:n.f(r.array12,((e,i,t)=>({a:i})))}:{},{e:n.n(`u-loading-icon__spinner--${e.mode}`),f:e.color,g:d.addUnit(e.size),h:d.addUnit(e.size),i:e.color,j:d.otherBorderColor,k:d.otherBorderColor,l:d.otherBorderColor,m:`${e.duration}ms`,n:"semicircle"===e.mode||"circle"===e.mode?e.timingFunction:""}),{o:e.text},e.text?{p:n.t(e.text),q:d.addUnit(e.textSize),r:e.textColor}:{},{s:n.s(d.addStyle(e.customStyle)),t:n.n(e.vertical&&"u-loading-icon--vertical")}):{})}],["__scopeId","data-v-795dbdc8"]]);wx.createComponent(s);
