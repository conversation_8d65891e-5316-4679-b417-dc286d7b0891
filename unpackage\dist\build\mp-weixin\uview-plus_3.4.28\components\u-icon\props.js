"use strict";const e=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),p=e.defineMixin({props:{name:{type:String,default:()=>o.props.icon.name},color:{type:String,default:()=>o.props.icon.color},size:{type:[String,Number],default:()=>o.props.icon.size},bold:{type:Boolean,default:()=>o.props.icon.bold},index:{type:[String,Number],default:()=>o.props.icon.index},hoverClass:{type:String,default:()=>o.props.icon.hoverClass},customPrefix:{type:String,default:()=>o.props.icon.customPrefix},label:{type:[String,Number],default:()=>o.props.icon.label},labelPos:{type:String,default:()=>o.props.icon.labelPos},labelSize:{type:[String,Number],default:()=>o.props.icon.labelSize},labelColor:{type:String,default:()=>o.props.icon.labelColor},space:{type:[String,Number],default:()=>o.props.icon.space},imgMode:{type:String,default:()=>o.props.icon.imgMode},width:{type:[String,Number],default:()=>o.props.icon.width},height:{type:[String,Number],default:()=>o.props.icon.height},top:{type:[String,Number],default:()=>o.props.icon.top},stop:{type:Boolean,default:()=>o.props.icon.stop}}});exports.props=p;
