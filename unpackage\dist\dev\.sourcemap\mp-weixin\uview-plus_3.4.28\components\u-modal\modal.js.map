{"version": 3, "file": "modal.js", "sources": ["uview-plus_3.4.28/components/u-modal/modal.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:15:59\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/modal.js\n */\nexport default {\n    // modal 组件\n    modal: {\n        show: false,\n        title: '',\n        content: '',\n        confirmText: '确认',\n        cancelText: '取消',\n        showConfirmButton: true,\n        showCancelButton: false,\n        confirmColor: '#2979ff',\n        cancelColor: '#606266',\n        buttonReverse: false,\n        zoom: true,\n        asyncClose: false,\n        closeOnClickOverlay: false,\n        negativeTop: 0,\n        width: '650rpx',\n        confirmButtonShape: '',\n        contentTextAlign: 'left',\n        asyncCloseTip: '操作中...',\n        asyncCancelClose: false,\n        contentStyle: {}\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,OAAO;AAAA,IACP,oBAAoB;AAAA,IACpB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,cAAc,CAAE;AAAA,EACnB;AACL;;"}