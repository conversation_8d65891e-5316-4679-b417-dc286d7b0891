"use strict";const e=require("../../common/vendor.js"),t=require("../../api/categories.js"),o={components:{CustomNavbar:()=>"../../components/common/custom-navbar.js"},data:()=>({categoryList:[],categoryHistory:[],currentLevel:1,currentTitle:"基层党组织"}),onLoad(){console.log("党建工作页面加载"),this.fetchCategoryList()},methods:{async fetchCategoryList(){try{e.index.showLoading({title:"加载中..."});const o=await t.getCategoryTreeList();console.log("获取分类树形列表成功:",o),o&&o.success&&Array.isArray(o.data)&&(this.categoryList=o.data,console.log("分类树形列表数据已更新"))}catch(o){console.error("获取分类树形列表失败:",o),e.index.showToast({title:"获取分类数据失败",icon:"none"})}finally{e.index.hideLoading()}},onCategoryClick(t){const o=this.categoryList[t];console.log("点击了基层党组织:",o),o.children&&o.children.length>0?(this.categoryHistory.push({list:[...this.categoryList],title:this.currentTitle,level:this.currentLevel}),this.categoryList=o.children,this.currentTitle=o.name,this.currentLevel++,e.index.setNavigationBarTitle({title:o.name})):e.index.navigateTo({url:`/pages/party/category-articles/index?id=${o.id}`,fail:t=>{console.error("跳转失败:",t),e.index.showToast({title:"跳转失败",icon:"none"})}})},goBackToParent(){if(this.categoryHistory.length>0){const t=this.categoryHistory.pop();return this.categoryList=t.list,this.currentTitle=t.title,this.currentLevel=t.level,e.index.setNavigationBarTitle({title:this.currentTitle}),!0}return!1},goBack(){this.goBackToParent()||e.index.navigateBack({delta:1})}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("custom-navbar")+e.resolveComponent("u-cell")+e.resolveComponent("u-list")+e.resolveComponent("u-button"))()}Math||((()=>"../../components/u-icon/u-icon.js")+(()=>"../../uview-plus_3.4.28/components/u-cell/u-cell.js")+(()=>"../../uview-plus_3.4.28/components/u-list/u-list.js")+(()=>"../../uview-plus_3.4.28/components/u-button/u-button.js"))();const i=e._export_sfc(o,[["render",function(t,o,i,n,r,s){return{a:e.p({name:"more-dot-fill",color:"#FFFFFF",size:"20"}),b:e.o(s.goBack),c:e.p({title:"党建工作",showBack:!0,showHome:!0}),d:e.p({name:"grid-fill",color:"#D9001B",size:"20"}),e:e.t(r.currentTitle),f:e.f(r.categoryList,((t,o,i)=>({a:o,b:e.o((e=>s.onCategoryClick(o)),o),c:"239aaf27-4-"+i+",239aaf27-3",d:e.p({title:t.name,titleStyle:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},isLink:!0})}))),g:e.p({"enable-flex":!0}),h:e.o(s.goBack),i:e.p({type:"primary",text:r.categoryHistory.length>0?"返回上一级":"返回首页",customStyle:{backgroundColor:"#D9001B"}})}}]]);wx.createPage(i);
