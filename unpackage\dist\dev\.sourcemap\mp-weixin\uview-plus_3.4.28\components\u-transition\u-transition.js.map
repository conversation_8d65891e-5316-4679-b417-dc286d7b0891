{"version": 3, "file": "u-transition.js", "sources": ["uview-plus_3.4.28/components/u-transition/u-transition.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtdHJhbnNpdGlvbi91LXRyYW5zaXRpb24udnVl"], "sourcesContent": ["<template>\n\t<view\n\t\tv-if=\"inited\"\n\t\tclass=\"u-transition\"\n\t\tref=\"u-transition\"\n\t\t@tap=\"clickHandler\"\n\t\t:class=\"classes\"\n\t\t:style=\"[mergeStyle]\"\n\t\t@touchmove=\"noop\"\n\t>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\nimport { props } from './props';\nimport { mpMixin } from '../../libs/mixin/mpMixin';\nimport { mixin } from '../../libs/mixin/mixin';\nimport { addStyle } from '../../libs/function/index';\n// 组件的methods方法，由于内容较长，写在外部文件中通过mixin引入\nimport transitionMixin from \"./transitionMixin.js\";\n/**\n * transition  动画组件\n * @description\n * @tutorial\n * @property {String}\t\t\tshow\t\t\t是否展示组件 （默认 false ）\n * @property {String}\t\t\tmode\t\t\t使用的动画模式 （默认 'fade' ）\n * @property {String | Number}\tduration\t\t动画的执行时间，单位ms （默认 '300' ）\n * @property {String}\t\t\ttimingFunction\t使用的动画过渡函数 （默认 'ease-out' ）\n * @property {Object}\t\t\tcustomStyle\t\t自定义样式\n * @event {Function} before-enter\t进入前触发\n * @event {Function} enter\t\t\t进入中触发\n * @event {Function} after-enter\t进入后触发\n * @event {Function} before-leave\t离开前触发\n * @event {Function} leave\t\t\t离开中触发\n * @event {Function} after-leave\t离开后触发\n * @example\n */\nexport default {\n\tname: 'u-transition',\n\tdata() {\n\t\treturn {\n\t\t\tinited: false, // 是否显示/隐藏组件\n\t\t\tviewStyle: {}, // 组件内部的样式\n\t\t\tstatus: '', // 记录组件动画的状态\n\t\t\ttransitionEnded: false, // 组件是否结束的标记\n\t\t\tdisplay: false, // 组件是否展示\n\t\t\tclasses: '', // 应用的类名\n\t\t}\n\t},\n\temits: ['click', 'beforeEnter', 'enter', 'afterEnter', 'beforeLeave', 'leave', 'afterLeave'],\n\tcomputed: {\n\t    mergeStyle() {\n\t        const { viewStyle, customStyle } = this\n\t        return {\n\t            // #ifndef APP-NVUE\n\t            transitionDuration: `${this.duration}ms`,\n\t            // display: `${this.display ? '' : 'none'}`,\n\t\t\t\ttransitionTimingFunction: this.timingFunction,\n\t            // #endif\n\t\t\t\t// 避免自定义样式影响到动画属性，所以写在viewStyle前面\n\t            ...addStyle(customStyle),\n\t            ...viewStyle\n\t        }\n\t    }\n\t},\n\t// 将mixin挂在到组件中，实际上为一个vue格式对象。\n\tmixins: [mpMixin, mixin, transitionMixin, props],\n\twatch: {\n\t\tshow: {\n\t\t\thandler(newVal) {\n\t\t\t\t// vue和nvue分别执行不同的方法\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tnewVal ? this.nvueEnter() : this.nvueLeave()\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tnewVal ? this.vueEnter() : this.vueLeave()\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 表示同时监听初始化时的props的show的意思\n\t\t\timmediate: true\n\t\t}\n\t}\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../libs/css/components.scss';\n\n/* #ifndef APP-NVUE */\n// vue版本动画相关的样式抽离在外部文件\n@import './vue.ani-style.scss';\n/* #endif */\n\n.u-transition {}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-transition/u-transition.vue'\nwx.createComponent(Component)"], "names": ["addStyle", "mpMixin", "mixin", "transitionMixin", "props"], "mappings": ";;;;;;;AAsCA,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AACN,WAAO;AAAA,MACN,QAAQ;AAAA;AAAA,MACR,WAAW,CAAE;AAAA;AAAA,MACb,QAAQ;AAAA;AAAA,MACR,iBAAiB;AAAA;AAAA,MACjB,SAAS;AAAA;AAAA,MACT,SAAS;AAAA;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO,CAAC,SAAS,eAAe,SAAS,cAAc,eAAe,SAAS,YAAY;AAAA,EAC3F,UAAU;AAAA,IACN,aAAa;AACT,YAAM,EAAE,WAAW,YAAY,IAAI;AACnC,aAAO;AAAA,QAEH,oBAAoB,GAAG,KAAK,QAAQ;AAAA;AAAA,QAE7C,0BAA0B,KAAK;AAAA;AAAA,QAGtB,GAAGA,qCAAAA,SAAS,WAAW;AAAA,QACvB,GAAG;AAAA,MACP;AAAA,IACJ;AAAA,EACH;AAAA;AAAA,EAED,QAAQ,CAACC,oCAAO,SAAEC,yCAAOC,wDAAAA,iBAAiBC,8CAAAA,KAAK;AAAA,EAC/C,OAAO;AAAA,IACN,MAAM;AAAA,MACL,QAAQ,QAAQ;AAMf,iBAAS,KAAK,aAAa,KAAK,SAAS;AAAA,MAEzC;AAAA;AAAA,MAED,WAAW;AAAA,IACZ;AAAA,EACD;AACD;;;;;;;;;;;;AClFA,GAAG,gBAAgB,SAAS;"}