{"version": 3, "file": "combineURLs.js", "sources": ["uview-plus_3.4.28/libs/luch-request/helpers/combineURLs.js"], "sourcesContent": ["'use strict'\r\n\r\n/**\r\n * Creates a new URL by combining the specified URLs\r\n *\r\n * @param {string} baseURL The base URL\r\n * @param {string} relativeURL The relative URL\r\n * @returns {string} The combined URL\r\n */\r\nexport default function combineURLs(baseURL, relativeURL) {\r\n    return relativeURL\r\n        ? `${baseURL.replace(/\\/+$/, '')}/${relativeURL.replace(/^\\/+/, '')}`\r\n        : baseURL\r\n}\r\n"], "names": [], "mappings": ";AASe,SAAS,YAAY,SAAS,aAAa;AACtD,SAAO,cACD,GAAG,QAAQ,QAAQ,QAAQ,EAAE,CAAC,IAAI,YAAY,QAAQ,QAAQ,EAAE,CAAC,KACjE;AACV;;"}