{"version": 3, "file": "custom-navbar.js", "sources": ["components/common/custom-navbar.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby9jb21wb25lbnRzL2NvbW1vbi9jdXN0b20tbmF2YmFyLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"custom-navbar\">\n\t\t<!-- 状态栏占位 -->\n\t\t<view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n\n\t\t<!-- 导航栏内容 -->\n\t\t<view class=\"navbar-content\">\n\t\t\t<!-- 左侧区域 -->\n\t\t\t<view class=\"left-area\">\n\t\t\t\t<slot name=\"left\">\n\t\t\t\t\t<view class=\"left-slot\">\n\t\t\t\t\t\t<view v-if=\"showBack\" class=\"back-icon\" @click=\"onBackClick\">\n\t\t\t\t\t\t\t<u-icon name=\"arrow-left\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"showBack && showHome\" class=\"divider-line\">\n\t\t\t\t\t\t\t<u-line direction=\"column\" :hairline=\"false\" length=\"16\" color=\"#FFFFFF\" width=\"1\"></u-line>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view v-if=\"showHome\" class=\"home-icon\" @click=\"onHomeClick\">\n\t\t\t\t\t\t\t<u-icon name=\"home\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</slot>\n\t\t\t</view>\n\n\t\t\t<!-- 标题区域 -->\n\t\t\t<view class=\"title-area\">\n\t\t\t\t<slot name=\"center\">\n\t\t\t\t\t<text class=\"title-text\">{{ title }}</text>\n\t\t\t\t</slot>\n\t\t\t</view>\n\n\t\t\t<!-- 右侧区域 -->\n\t\t\t<view class=\"right-area\" @click=\"onRightClick\">\n\t\t\t\t<slot name=\"right\">\n\t\t\t\t\t<view class=\"right-slot\">\n\t\t\t\t\t\t<u-icon v-if=\"rightIcon\" :name=\"rightIcon\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t\t\t<text v-if=\"rightText\" class=\"right-text\">{{ rightText }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</slot>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname: 'custom-navbar',\n\t\tprops: {\n\t\t\t// 标题\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 是否显示返回图标\n\t\t\tshowBack: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 是否显示返回首页图标\n\t\t\tshowHome: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 右侧图标\n\t\t\trightIcon: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 右侧文字\n\t\t\trightText: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 是否自动返回上一页\n\t\t\tautoBack: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tstatusBarHeight: 20 // 状态栏高度，默认值\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// 获取状态栏高度\n\t\t\tthis.getStatusBarHeight();\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取状态栏高度\n\t\t\tgetStatusBarHeight() {\n\t\t\t\ttry {\n\t\t\t\t\t// 使用uni-app提供的API获取窗口信息\n\t\t\t\t\tconst windowInfo = uni.getWindowInfo();\n\t\t\t\t\tthis.statusBarHeight = windowInfo.statusBarHeight || 20;\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('获取状态栏高度失败:', e);\n\t\t\t\t\t// 设置默认值\n\t\t\t\t\tthis.statusBarHeight = 20;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回按钮点击事件\n\t\t\tonBackClick() {\n\t\t\t\t// 触发事件，让父组件可以监听\n\t\t\t\tthis.$emit('leftClick');\n\n\t\t\t\t// 如果是首页，不执行返回操作\n\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\tif (pages.length <= 1) {\n\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 正常返回上一页\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1,\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t// 如果返回失败，跳转到首页\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 首页按钮点击事件\n\t\t\tonHomeClick() {\n\t\t\t\t// 触发事件，让父组件可以监听\n\t\t\t\tthis.$emit('homeClick');\n\n\t\t\t\t// 直接跳转到首页\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转到首页失败:', err);\n\t\t\t\t\t\t// 如果当前已经在首页，可能会失败，此时尝试重新加载首页\n\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\turl: '/pages/index/index',\n\t\t\t\t\t\t\tfail: (err2) => {\n\t\t\t\t\t\t\t\tconsole.error('reLaunch跳转到首页也失败:', err2);\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '跳转到首页失败',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 右侧点击事件\n\t\t\tonRightClick() {\n\t\t\t\tthis.$emit('rightClick');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t.custom-navbar {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tz-index: 999;\n\t\tbackground-color: #D9001B;\n\n\t\t.status-bar {\n\t\t\twidth: 100%;\n\t\t}\n\n\t\t.navbar-content {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tjustify-content: space-between;\n\t\t\theight: 50px; /* 增加导航栏高度 */\n\t\t\tpadding: 0 10px;\n\n\t\t\t.left-area {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: flex-start;\n\t\t\t\tbackground-color: #C7250D;\n\t\t\t\tborder-radius: 20px;\n\t\t\t\tpadding: 4px 8px;\n\n\t\t\t\t.left-slot {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\twidth: 100%;\n\t\t\t\t}\n\n\t\t\t\t.back-icon {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\twidth: 30px;\n\t\t\t\t}\n\n\t\t\t\t.divider-line {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\tpadding: 0 5px;\n\t\t\t\t}\n\n\t\t\t\t.home-icon {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tjustify-content: center;\n\t\t\t\t\twidth: 30px;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.title-area {\n\t\t\t\tflex: 1;\n\t\t\t\ttext-align: center;\n\n\t\t\t\t.title-text {\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont-size: 18px;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.right-area {\n\t\t\t\twidth: 80px;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: flex-end;\n\n\t\t\t\t.right-slot {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t}\n\n\t\t\t\t.right-text {\n\t\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\tfont-size: 14px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/components/common/custom-navbar.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AA6CC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,iBAAiB;AAAA;AAAA,IAClB;AAAA,EACA;AAAA,EACD,UAAU;AAET,SAAK,mBAAkB;AAAA,EACvB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,qBAAqB;AACpB,UAAI;AAEH,cAAM,aAAaA,oBAAI;AACvB,aAAK,kBAAkB,WAAW,mBAAmB;AAAA,MACtD,SAAS,GAAG;AACXA,wFAAc,cAAc,CAAC;AAE7B,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA;AAAA,IAGD,cAAc;AAEb,WAAK,MAAM,WAAW;AAGtB,YAAM,QAAQ;AACd,UAAI,MAAM,UAAU,GAAG;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,KAAK;AAAA,QACN,CAAC;AACD;AAAA,MACD;AAGAA,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,QACP,MAAM,MAAM;AAEXA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAK;AAAA,UACN,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,cAAc;AAEb,WAAK,MAAM,WAAW;AAGtBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,QACL,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,8CAAA,YAAY,GAAG;AAE7BA,wBAAAA,MAAI,SAAS;AAAA,YACZ,KAAK;AAAA,YACL,MAAM,CAAC,SAAS;AACfA,4BAAc,MAAA,MAAA,SAAA,8CAAA,qBAAqB,IAAI;AACvCA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,eAAe;AACd,WAAK,MAAM,YAAY;AAAA,IACxB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7JD,GAAG,gBAAgB,SAAS;"}