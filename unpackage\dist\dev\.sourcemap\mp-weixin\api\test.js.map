{"version": 3, "file": "test.js", "sources": ["api/test.js"], "sourcesContent": ["import { getArticleList } from './news.js';\n\n/**\n * 测试获取文章列表\n */\nexport function testGetArticleList() {\n  console.log('开始测试获取文章列表...');\n  \n  getArticleList()\n    .then(res => {\n      console.log('获取文章列表成功:', res);\n      return res;\n    })\n    .catch(err => {\n      console.error('获取文章列表失败:', err);\n    });\n}\n"], "names": ["uni", "getArticleList"], "mappings": ";;;AAKO,SAAS,qBAAqB;AACnCA,gBAAAA,MAAY,MAAA,OAAA,oBAAA,eAAe;AAE3BC,0BAAgB,EACb,KAAK,SAAO;AACXD,kBAAY,MAAA,MAAA,OAAA,qBAAA,aAAa,GAAG;AAC5B,WAAO;AAAA,EACb,CAAK,EACA,MAAM,SAAO;AACZA,kBAAc,MAAA,MAAA,SAAA,qBAAA,aAAa,GAAG;AAAA,EACpC,CAAK;AACL;;"}