import request from './request.js';

/**
 * 获取菜单列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getMenuList(params = {}) {
  return request.get('/api/menus/', params);
}

/**
 * 获取子菜单列表
 * @param {String|Number} parentId - 父菜单ID
 * @returns {Promise} - 返回Promise对象
 */
export function getSubMenus(parentId) {
  return request.get(`/api/menus/?parentId=${parentId}`);
}
