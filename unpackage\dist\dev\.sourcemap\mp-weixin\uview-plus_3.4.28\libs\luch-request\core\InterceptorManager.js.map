{"version": 3, "file": "InterceptorManager.js", "sources": ["uview-plus_3.4.28/libs/luch-request/core/InterceptorManager.js"], "sourcesContent": ["'use strict'\r\n\r\nfunction InterceptorManager() {\r\n    this.handlers = []\r\n}\r\n\r\n/**\r\n * Add a new interceptor to the stack\r\n *\r\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\r\n * @param {Function} rejected The function to handle `reject` for a `Promise`\r\n *\r\n * @return {Number} An ID used to remove interceptor later\r\n */\r\nInterceptorManager.prototype.use = function use(fulfilled, rejected) {\r\n    this.handlers.push({\r\n        fulfilled,\r\n        rejected\r\n    })\r\n    return this.handlers.length - 1\r\n}\r\n\r\n/**\r\n * Remove an interceptor from the stack\r\n *\r\n * @param {Number} id The ID that was returned by `use`\r\n */\r\nInterceptorManager.prototype.eject = function eject(id) {\r\n    if (this.handlers[id]) {\r\n        this.handlers[id] = null\r\n    }\r\n}\r\n\r\n/**\r\n * Iterate over all the registered interceptors\r\n *\r\n * This method is particularly useful for skipping over any\r\n * interceptors that may have become `null` calling `eject`.\r\n *\r\n * @param {Function} fn The function to call for each interceptor\r\n */\r\nInterceptorManager.prototype.forEach = function forEach(fn) {\r\n    this.handlers.forEach((h) => {\r\n        if (h !== null) {\r\n            fn(h)\r\n        }\r\n    })\r\n}\r\n\r\nexport default InterceptorManager\r\n"], "names": [], "mappings": ";AAEA,SAAS,qBAAqB;AAC1B,OAAK,WAAW,CAAE;AACtB;AAUA,mBAAmB,UAAU,MAAM,SAAS,IAAI,WAAW,UAAU;AACjE,OAAK,SAAS,KAAK;AAAA,IACf;AAAA,IACA;AAAA,EACR,CAAK;AACD,SAAO,KAAK,SAAS,SAAS;AAClC;AAOA,mBAAmB,UAAU,QAAQ,SAAS,MAAM,IAAI;AACpD,MAAI,KAAK,SAAS,EAAE,GAAG;AACnB,SAAK,SAAS,EAAE,IAAI;AAAA,EACvB;AACL;AAUA,mBAAmB,UAAU,UAAU,SAAS,QAAQ,IAAI;AACxD,OAAK,SAAS,QAAQ,CAAC,MAAM;AACzB,QAAI,MAAM,MAAM;AACZ,SAAG,CAAC;AAAA,IACP;AAAA,EACT,CAAK;AACL;;"}