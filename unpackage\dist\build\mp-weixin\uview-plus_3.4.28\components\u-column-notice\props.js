"use strict";const e=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),t=e.defineMixin({props:{text:{type:[Array],default:()=>o.props.columnNotice.text},icon:{type:String,default:()=>o.props.columnNotice.icon},mode:{type:String,default:()=>o.props.columnNotice.mode},color:{type:String,default:()=>o.props.columnNotice.color},bgColor:{type:String,default:()=>o.props.columnNotice.bgColor},fontSize:{type:[String,Number],default:()=>o.props.columnNotice.fontSize},speed:{type:[String,Number],default:()=>o.props.columnNotice.speed},step:{type:Boolean,default:()=>o.props.columnNotice.step},duration:{type:[String,Number],default:()=>o.props.columnNotice.duration},disableTouch:{type:Bo<PERSON>an,default:()=>o.props.columnNotice.disableTouch},justifyContent:{type:String,default:()=>o.props.columnNotice.justifyContent}}});exports.props=t;
