"use strict";
const common_vendor = require("../../common/vendor.js");
const api_categories = require("../../api/categories.js");
const api_menus = require("../../api/menus.js");
const api_menuArticles = require("../../api/menu-articles.js");
const utils_image = require("../../utils/image.js");
const CustomNavbar = () => "../../components/common/custom-navbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      parentMenuId: null,
      // 父级菜单ID
      parentMenuName: "组织工作",
      // 父级菜单名称
      tabList: [],
      // 标签列表
      current: 0,
      // 当前选中的标签索引
      articleList: [],
      // 文章列表
      statusBarHeight: 20,
      // 状态栏高度，默认值
      showPartyComponent: false,
      // 是否显示组织架构组件
      // 组织架构相关数据
      categoryList: [],
      // 基层党组织数据
      categoryHistory: [],
      // 分类历史记录，用于返回上一级
      currentLevel: 1,
      // 当前分类层级
      currentTitle: "基层党组织"
      // 当前分类标题
    };
  },
  computed: {
    // 计算顶部安全区域的高度
    safeAreaTop() {
      return this.statusBarHeight + 45;
    }
  },
  onLoad(options) {
    this.getStatusBarHeight();
    if (options.id) {
      this.parentMenuId = Number(options.id);
      const subMenuId = options.subMenuId ? Number(options.subMenuId) : null;
      this.loadSubMenus(subMenuId);
    }
    if (options.name) {
      this.parentMenuName = decodeURIComponent(options.name);
    }
  },
  methods: {
    // 获取状态栏高度
    getStatusBarHeight() {
      try {
        const windowInfo = common_vendor.index.getWindowInfo();
        this.statusBarHeight = windowInfo.statusBarHeight || 20;
        common_vendor.index.__f__("log", "at pages/menus/index.vue:153", "状态栏高度:", this.statusBarHeight);
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/menus/index.vue:156", "获取状态栏高度失败:", e);
        this.statusBarHeight = 20;
      }
    },
    // 返回上一页方法已由导航栏组件自动处理
    // 加载子菜单
    async loadSubMenus(subMenuId = null) {
      try {
        const result = await api_menus.getMenuList();
        if (result && result.success && Array.isArray(result.data)) {
          const subMenus = result.data.filter((item) => item.parentId === this.parentMenuId);
          this.tabList = subMenus.map((item) => {
            return {
              name: item.name,
              id: item.id
            };
          });
          if (this.tabList.length > 0) {
            if (subMenuId) {
              const index = this.tabList.findIndex((item) => item.id === subMenuId);
              if (index !== -1) {
                this.current = index;
                this.loadArticles(subMenuId);
                return;
              }
            }
            this.current = 0;
            this.loadArticles(this.tabList[0].id);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/menus/index.vue:202", "获取子菜单列表失败:", error);
        common_vendor.index.showToast({
          title: "获取菜单失败",
          icon: "none"
        });
      }
    },
    // 标签切换事件
    onTabChange(tabItem) {
      if (tabItem && tabItem.id) {
        this.loadArticles(tabItem.id);
      }
    },
    // 加载文章列表
    async loadArticles(menuId) {
      const currentTab = this.tabList.find((tab) => tab.id === menuId);
      if (currentTab && currentTab.name === "组织架构") {
        this.showPartyComponent = true;
        this.fetchCategoryList();
        return;
      } else {
        this.showPartyComponent = false;
      }
      try {
        const result = await api_menuArticles.getMenuArticles(menuId);
        if (result && result.success && Array.isArray(result.data)) {
          const articles = result.data;
          for (let article of articles) {
            if (article.coverImageId && !article.coverImage) {
              try {
                const imageData = await utils_image.fetchImageById(article.coverImageId);
                if (imageData && imageData.url) {
                  article.coverImage = utils_image.getFullImageUrl(imageData.url);
                }
              } catch (err) {
                common_vendor.index.__f__("error", "at pages/menus/index.vue:250", "获取文章封面图失败:", err);
              }
            }
          }
          this.articleList = articles;
        } else {
          this.articleList = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/menus/index.vue:260", "获取菜单文章列表失败:", error);
        this.articleList = [];
        common_vendor.index.showToast({
          title: "获取文章失败",
          icon: "none"
        });
      }
    },
    // 获取纯文本摘要
    getPlainTextSummary(htmlContent) {
      const plainText = htmlContent.replace(/<[^>]+>/g, "");
      const trimmedText = plainText.replace(/\s+/g, " ").trim();
      return trimmedText.length > 100 ? trimmedText.substring(0, 100) + "..." : trimmedText;
    },
    // 格式化日期
    formatDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },
    // 跳转到文章详情
    goToArticleDetail(article) {
      common_vendor.index.navigateTo({
        url: `/pages/article/detail?id=${article.id}&type=menu`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/menus/index.vue:295", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 以下是组织架构相关方法
    // 获取分类列表
    async fetchCategoryList() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const result = await api_categories.getCategoryTreeList();
        common_vendor.index.__f__("log", "at pages/menus/index.vue:313", "获取分类树形列表成功:", result);
        if (result && result.success && Array.isArray(result.data)) {
          this.categoryList = result.data;
          common_vendor.index.__f__("log", "at pages/menus/index.vue:317", "分类树形列表数据已更新");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/menus/index.vue:320", "获取分类树形列表失败:", error);
        common_vendor.index.showToast({
          title: "获取分类数据失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 分类点击事件
    onCategoryClick(index) {
      const item = this.categoryList[index];
      common_vendor.index.__f__("log", "at pages/menus/index.vue:333", "点击了基层党组织:", item);
      if (item.children && item.children.length > 0) {
        this.categoryHistory.push({
          list: [...this.categoryList],
          title: this.currentTitle,
          level: this.currentLevel
        });
        this.categoryList = item.children;
        this.currentTitle = item.name;
        this.currentLevel++;
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/party/category-articles/index?id=${item.id}`,
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/menus/index.vue:355", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      }
    },
    // 返回上一级分类
    goBackToParent() {
      if (this.categoryHistory.length > 0) {
        const prevCategory = this.categoryHistory.pop();
        this.categoryList = prevCategory.list;
        this.currentTitle = prevCategory.title;
        this.currentLevel = prevCategory.level;
        return true;
      }
      return false;
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_u_cell2 = common_vendor.resolveComponent("u-cell");
  const _easycom_u_list2 = common_vendor.resolveComponent("u-list");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  (_easycom_u_icon2 + _component_custom_navbar + _easycom_u_tabs2 + _easycom_u_cell2 + _easycom_u_list2 + _easycom_u_button2 + _easycom_u_empty2)();
}
const _easycom_u_icon = () => "../../components/u-icon/u-icon.js";
const _easycom_u_tabs = () => "../../uview-plus_3.4.28/components/u-tabs/u-tabs.js";
const _easycom_u_cell = () => "../../uview-plus_3.4.28/components/u-cell/u-cell.js";
const _easycom_u_list = () => "../../uview-plus_3.4.28/components/u-list/u-list.js";
const _easycom_u_button = () => "../../uview-plus_3.4.28/components/u-button/u-button.js";
const _easycom_u_empty = () => "../../uview-plus_3.4.28/components/u-empty/u-empty.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_tabs + _easycom_u_cell + _easycom_u_list + _easycom_u_button + _easycom_u_empty)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      name: "more-dot-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    b: common_vendor.p({
      title: $data.parentMenuName,
      showBack: true,
      showHome: true
    }),
    c: $data.tabList && $data.tabList.length > 0
  }, $data.tabList && $data.tabList.length > 0 ? {
    d: common_vendor.o($options.onTabChange),
    e: common_vendor.o(($event) => $data.current = $event),
    f: common_vendor.p({
      list: $data.tabList,
      lineColor: "#D9001B",
      lineWidth: "30",
      itemStyle: "padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;",
      activeStyle: {
        color: "#D9001B",
        fontWeight: "bold",
        fontSize: "28rpx"
      },
      inactiveStyle: {
        color: "#333333",
        fontSize: "28rpx"
      },
      current: $data.current
    })
  } : {}, {
    g: $data.showPartyComponent
  }, $data.showPartyComponent ? common_vendor.e({
    h: common_vendor.p({
      name: "grid-fill",
      color: "#D9001B",
      size: "20"
    }),
    i: common_vendor.t($data.currentTitle),
    j: common_vendor.f($data.categoryList, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.onCategoryClick(index), index),
        c: "6dc8f1b5-5-" + i0 + ",6dc8f1b5-4",
        d: common_vendor.p({
          title: item.name,
          titleStyle: {
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          },
          isLink: true
        })
      };
    }),
    k: common_vendor.p({
      ["enable-flex"]: true
    }),
    l: $data.categoryHistory.length > 0
  }, $data.categoryHistory.length > 0 ? {
    m: common_vendor.o($options.goBackToParent),
    n: common_vendor.p({
      type: "primary",
      text: "返回上一级",
      customStyle: {
        backgroundColor: "#D9001B"
      }
    })
  } : {}) : common_vendor.e({
    o: $data.articleList.length > 0
  }, $data.articleList.length > 0 ? {
    p: common_vendor.f($data.articleList, (item, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.title),
        b: item.content
      }, item.content ? {
        c: common_vendor.t($options.getPlainTextSummary(item.content))
      } : {}, {
        d: common_vendor.t($options.formatDate(item.createdAt)),
        e: item.coverImage
      }, item.coverImage ? {
        f: item.coverImage
      } : {}, {
        g: index,
        h: common_vendor.o(($event) => $options.goToArticleDetail(item), index)
      });
    })
  } : {
    q: common_vendor.p({
      mode: "data",
      text: "暂无数据"
    })
  }));
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/menus/index.js.map
