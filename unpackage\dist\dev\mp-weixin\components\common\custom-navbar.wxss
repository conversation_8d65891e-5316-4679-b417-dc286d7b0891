/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #D9001B;
}
.custom-navbar .status-bar {
  width: 100%;
}
.custom-navbar .navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  /* 增加导航栏高度 */
  padding: 0 10px;
}
.custom-navbar .navbar-content .left-area {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  background-color: #C7250D;
  border-radius: 20px;
  padding: 4px 8px;
}
.custom-navbar .navbar-content .left-area .left-slot {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.custom-navbar .navbar-content .left-area .back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
}
.custom-navbar .navbar-content .left-area .divider-line {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}
.custom-navbar .navbar-content .left-area .home-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
}
.custom-navbar .navbar-content .title-area {
  flex: 1;
  text-align: center;
}
.custom-navbar .navbar-content .title-area .title-text {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: 500;
}
.custom-navbar .navbar-content .right-area {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.custom-navbar .navbar-content .right-area .right-slot {
  display: flex;
  align-items: center;
}
.custom-navbar .navbar-content .right-area .right-text {
  color: #FFFFFF;
  font-size: 14px;
}