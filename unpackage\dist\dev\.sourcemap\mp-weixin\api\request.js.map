{"version": 3, "file": "request.js", "sources": ["api/request.js"], "sourcesContent": ["import config from '@/utils/config.js';\n\n/**\n * 封装uni.request网络请求\n * @param {Object} options - 请求配置\n * @returns {Promise} - 返回Promise对象\n */\nconst request = (options) => {\n  return new Promise((resolve, reject) => {\n    const requestOptions = {\n      url: config.apiBaseUrl + options.url,\n      data: options.data || options.params,\n      method: options.method || 'GET',\n      header: {\n        'content-type': 'application/json;charset=utf-8',\n        ...options.header\n      },\n      timeout: 10000, // 请求超时时间，单位ms\n      dataType: 'json',\n      success: (res) => {\n        // 请求成功\n        if (res.statusCode >= 200 && res.statusCode < 300) {\n          resolve(res.data);\n        } else {\n          // 请求成功但状态码异常\n          console.error('请求异常:', res);\n          reject(res);\n        }\n      },\n      fail: (err) => {\n        // 请求失败\n        console.error('请求失败:', err);\n        reject(err);\n      }\n    };\n\n    // 发起请求\n    uni.request(requestOptions);\n  });\n};\n\n/**\n * GET请求\n * @param {String} url - 请求地址\n * @param {Object} params - 请求参数\n * @param {Object} header - 请求头\n * @returns {Promise} - 返回Promise对象\n */\nconst get = (url, params = {}, header = {}) => {\n  return request({\n    url,\n    params,\n    method: 'GET',\n    header\n  });\n};\n\n/**\n * POST请求\n * @param {String} url - 请求地址\n * @param {Object} data - 请求数据\n * @param {Object} header - 请求头\n * @returns {Promise} - 返回Promise对象\n */\nconst post = (url, data = {}, header = {}) => {\n  return request({\n    url,\n    data,\n    method: 'POST',\n    header\n  });\n};\n\nexport default {\n  request,\n  get,\n  post\n};\n"], "names": ["config", "uni"], "mappings": ";;;AAOA,MAAM,UAAU,CAAC,YAAY;AAC3B,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAM,iBAAiB;AAAA,MACrB,KAAKA,aAAM,OAAC,aAAa,QAAQ;AAAA,MACjC,MAAM,QAAQ,QAAQ,QAAQ;AAAA,MAC9B,QAAQ,QAAQ,UAAU;AAAA,MAC1B,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,GAAG,QAAQ;AAAA,MACZ;AAAA,MACD,SAAS;AAAA;AAAA,MACT,UAAU;AAAA,MACV,SAAS,CAAC,QAAQ;AAEhB,YAAI,IAAI,cAAc,OAAO,IAAI,aAAa,KAAK;AACjD,kBAAQ,IAAI,IAAI;AAAA,QAC1B,OAAe;AAELC,wBAAc,MAAA,MAAA,SAAA,wBAAA,SAAS,GAAG;AAC1B,iBAAO,GAAG;AAAA,QACX;AAAA,MACF;AAAA,MACD,MAAM,CAAC,QAAQ;AAEbA,mEAAc,SAAS,GAAG;AAC1B,eAAO,GAAG;AAAA,MACX;AAAA,IACP;AAGIA,wBAAI,QAAQ,cAAc;AAAA,EAC9B,CAAG;AACH;AASA,MAAM,MAAM,CAAC,KAAK,SAAS,CAAA,GAAI,SAAS,CAAA,MAAO;AAC7C,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AASA,MAAM,OAAO,CAAC,KAAK,OAAO,CAAA,GAAI,SAAS,CAAA,MAAO;AAC5C,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACJ,CAAG;AACH;AAEA,MAAe,YAAA;AAAA,EACb;AAAA,EACA;AAAA,EACA;AACF;;"}