{"version": 3, "file": "picker.js", "sources": ["uview-plus_3.4.28/components/u-picker/picker.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:18:20\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/picker.js\n */\nexport default {\n    // picker\n    picker: {\n        show: false,\n\t\tpopupMode: 'bottom',\n        showToolbar: true,\n        title: '',\n        columns: [],\n        loading: false,\n        itemHeight: 44,\n        cancelText: '取消',\n        confirmText: '确定',\n        cancelColor: '#909193',\n        confirmColor: '',\n        visibleItemCount: 5,\n        keyName: 'text',\n        closeOnClickOverlay: false,\n        defaultIndex: [],\n\t\timmediateChange: true,\n\t\tzIndex: 10076,\n        disabled: false,\n        disabledColor: '',\n        placeholder: '请选择',\n        inputProps: {},\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,SAAS,CAAE;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,cAAc,CAAE;AAAA,IACtB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACF,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,EACjB;AACL;;"}