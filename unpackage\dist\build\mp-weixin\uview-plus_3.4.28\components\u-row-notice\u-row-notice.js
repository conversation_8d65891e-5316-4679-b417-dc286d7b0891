"use strict";const t=require("./props.js"),e=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),n=require("../../libs/function/index.js"),o=require("../../libs/function/test.js"),s=require("../../../common/vendor.js"),a={name:"u-row-notice",mixins:[e.mpMixin,i.mixin,t.props],data:()=>({animationDuration:"0",animationPlayState:"paused",nvueInit:!0,show:!0}),watch:{text:{immediate:!0,handler(t,e){this.vue(),o.test.string(t)}},fontSize(){this.vue()},speed(){this.vue()}},computed:{textStyle(){let t={whiteSpace:"nowrap !important"};return t.color=this.color,t.fontSize=n.addUnit(this.fontSize),t},animationStyle(){let t={};return t.animationDuration=this.animationDuration,t.animationPlayState=this.animationPlayState,t},innerText(){let t=[];const e=this.text.split("");for(let i=0;i<e.length;i+=20)t.push(e.slice(i,i+20).join(""));return t}},mounted(){this.init()},emits:["click","close"],methods:{init(){this.vue(),o.test.string(this.text)},async vue(){let t=0;await n.sleep(),t=(await this.$uGetRect(".u-notice__content__text")).width,(await this.$uGetRect(".u-notice__content")).width,this.animationDuration=t/n.getPx(this.speed)+"s",this.animationPlayState="paused",setTimeout((()=>{this.animationPlayState="running"}),10)},async nvue(){},loopAnimation(t,e){},getNvueRect(t){},clickHandler(t){this.$emit("click")},close(){this.$emit("close")}},beforeUnmount(){this.stopAnimation=!0}};if(!Array){s.resolveComponent("u-icon")()}Math;const l=s._export_sfc(a,[["render",function(t,e,i,n,o,a){return s.e({a:t.icon},t.icon?{b:s.p({name:t.icon,color:t.color,size:"19"})}:{},{c:s.f(a.innerText,((t,e,i)=>({a:s.t(t),b:e}))),d:s.s(a.textStyle),e:s.s(a.animationStyle),f:["link","closable"].includes(t.mode)},["link","closable"].includes(t.mode)?s.e({g:"link"===t.mode},"link"===t.mode?{h:s.p({name:"arrow-right",size:17,color:t.color})}:{},{i:"closable"===t.mode},"closable"===t.mode?{j:s.o(a.close),k:s.p({name:"close",size:16,color:t.color})}:{}):{},{l:s.o(((...t)=>a.clickHandler&&a.clickHandler(...t)))})}],["__scopeId","data-v-69566d44"]]);wx.createComponent(l);
