/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.u-empty.data-v-f0399ea5,
.u-empty__wrap.data-v-f0399ea5,
.u-tabs.data-v-f0399ea5,
.u-tabs__wrapper.data-v-f0399ea5,
.u-tabs__wrapper__scroll-view-wrapper.data-v-f0399ea5,
.u-tabs__wrapper__scroll-view.data-v-f0399ea5,
.u-tabs__wrapper__nav.data-v-f0399ea5,
.u-tabs__wrapper__nav__line.data-v-f0399ea5,
.up-empty.data-v-f0399ea5,
.up-empty__wrap.data-v-f0399ea5,
.up-tabs.data-v-f0399ea5,
.up-tabs__wrapper.data-v-f0399ea5,
.up-tabs__wrapper__scroll-view-wrapper.data-v-f0399ea5,
.up-tabs__wrapper__scroll-view.data-v-f0399ea5,
.up-tabs__wrapper__nav.data-v-f0399ea5,
.up-tabs__wrapper__nav__line.data-v-f0399ea5 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-cell__body.data-v-f0399ea5 {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  padding: 13px 15px;
  font-size: 15px;
  color: #303133;
  align-items: center;
}
.u-cell__body__content.data-v-f0399ea5 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-cell__body--large.data-v-f0399ea5 {
  padding-top: 13px;
  padding-bottom: 13px;
}
.u-cell__left-icon-wrap.data-v-f0399ea5, .u-cell__right-icon-wrap.data-v-f0399ea5 {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 16px;
}
.u-cell__left-icon-wrap.data-v-f0399ea5 {
  margin-right: 4px;
}
.u-cell__right-icon-wrap.data-v-f0399ea5 {
  margin-left: 4px;
  transition: transform 0.3s;
}
.u-cell__right-icon-wrap--up.data-v-f0399ea5 {
  transform: rotate(-90deg);
}
.u-cell__right-icon-wrap--down.data-v-f0399ea5 {
  transform: rotate(90deg);
}
.u-cell__title.data-v-f0399ea5 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.u-cell__title-text.data-v-f0399ea5 {
  font-size: 15px;
  line-height: 22px;
  color: #303133;
}
.u-cell__title-text--large.data-v-f0399ea5 {
  font-size: 16px;
}
.u-cell__label.data-v-f0399ea5 {
  margin-top: 5px;
  font-size: 12px;
  color: #909193;
  line-height: 18px;
}
.u-cell__label--large.data-v-f0399ea5 {
  font-size: 14px;
}
.u-cell__value.data-v-f0399ea5 {
  text-align: right;
  margin-left: auto;
  font-size: 14px;
  line-height: 24px;
  color: #606266;
}
.u-cell__value--large.data-v-f0399ea5 {
  font-size: 15px;
}
.u-cell--required.data-v-f0399ea5 {
  overflow: visible;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-cell--required.data-v-f0399ea5:before {
  position: absolute;
  content: "*";
  left: -8px;
  margin-top: 4rpx;
  font-size: 14px;
  color: #f56c6c;
}
.u-cell--clickable.data-v-f0399ea5 {
  background-color: #f3f4f6;
}
.u-cell--disabled.data-v-f0399ea5 {
  color: #c8c9cc;
  cursor: not-allowed;
}
.u-cell--center.data-v-f0399ea5 {
  align-items: center;
}