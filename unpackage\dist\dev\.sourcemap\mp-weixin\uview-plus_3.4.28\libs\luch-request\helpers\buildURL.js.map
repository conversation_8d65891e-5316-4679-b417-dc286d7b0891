{"version": 3, "file": "buildURL.js", "sources": ["uview-plus_3.4.28/libs/luch-request/helpers/buildURL.js"], "sourcesContent": ["'use strict'\r\n\r\nimport * as utils from '../utils'\r\n\r\nfunction encode(val) {\r\n    return encodeURIComponent(val)\r\n        .replace(/%40/gi, '@')\r\n        .replace(/%3A/gi, ':')\r\n        .replace(/%24/g, '$')\r\n        .replace(/%2C/gi, ',')\r\n        .replace(/%20/g, '+')\r\n        .replace(/%5B/gi, '[')\r\n        .replace(/%5D/gi, ']')\r\n}\r\n\r\n/**\r\n * Build a URL by appending params to the end\r\n *\r\n * @param {string} url The base of the url (e.g., http://www.google.com)\r\n * @param {object} [params] The params to be appended\r\n * @returns {string} The formatted url\r\n */\r\nexport default function buildURL(url, params) {\r\n    /* eslint no-param-reassign:0 */\r\n    if (!params) {\r\n        return url\r\n    }\r\n\r\n    let serializedParams\r\n    if (utils.isURLSearchParams(params)) {\r\n        serializedParams = params.toString()\r\n    } else {\r\n        const parts = []\r\n\r\n        utils.forEach(params, (val, key) => {\r\n            if (val === null || typeof val === 'undefined') {\r\n                return\r\n            }\r\n\r\n            if (utils.isArray(val)) {\r\n                key = `${key}[]`\r\n            } else {\r\n                val = [val]\r\n            }\r\n\r\n            utils.forEach(val, (v) => {\r\n                if (utils.isDate(v)) {\r\n                    v = v.toISOString()\r\n                } else if (utils.isObject(v)) {\r\n                    v = JSON.stringify(v)\r\n                }\r\n                parts.push(`${encode(key)}=${encode(v)}`)\r\n            })\r\n        })\r\n\r\n        serializedParams = parts.join('&')\r\n    }\r\n\r\n    if (serializedParams) {\r\n        const hashmarkIndex = url.indexOf('#')\r\n        if (hashmarkIndex !== -1) {\r\n            url = url.slice(0, hashmarkIndex)\r\n        }\r\n\r\n        url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams\r\n    }\r\n\r\n    return url\r\n}\r\n"], "names": ["utils.isURLSearchParams", "utils.forEach", "utils.isArray", "utils.isDate", "utils.isObject"], "mappings": ";;AAIA,SAAS,OAAO,KAAK;AACjB,SAAO,mBAAmB,GAAG,EACxB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,GAAG,EACpB,QAAQ,QAAQ,GAAG,EACnB,QAAQ,SAAS,GAAG,EACpB,QAAQ,SAAS,GAAG;AAC7B;AASe,SAAS,SAAS,KAAK,QAAQ;AAE1C,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACV;AAED,MAAI;AACJ,MAAIA,wCAAAA,kBAAwB,MAAM,GAAG;AACjC,uBAAmB,OAAO,SAAU;AAAA,EAC5C,OAAW;AACH,UAAM,QAAQ,CAAE;AAEhBC,4CAAAA,QAAc,QAAQ,CAAC,KAAK,QAAQ;AAChC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,aAAa;AAC5C;AAAA,MACH;AAED,UAAIC,wCAAAA,QAAc,GAAG,GAAG;AACpB,cAAM,GAAG,GAAG;AAAA,MAC5B,OAAmB;AACH,cAAM,CAAC,GAAG;AAAA,MACb;AAEDD,sDAAc,KAAK,CAAC,MAAM;AACtB,YAAIE,wCAAAA,OAAa,CAAC,GAAG;AACjB,cAAI,EAAE,YAAa;AAAA,QACvC,WAA2BC,wCAAAA,SAAe,CAAC,GAAG;AAC1B,cAAI,KAAK,UAAU,CAAC;AAAA,QACvB;AACD,cAAM,KAAK,GAAG,OAAO,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE;AAAA,MACxD,CAAa;AAAA,IACb,CAAS;AAED,uBAAmB,MAAM,KAAK,GAAG;AAAA,EACpC;AAED,MAAI,kBAAkB;AAClB,UAAM,gBAAgB,IAAI,QAAQ,GAAG;AACrC,QAAI,kBAAkB,IAAI;AACtB,YAAM,IAAI,MAAM,GAAG,aAAa;AAAA,IACnC;AAED,YAAQ,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO;AAAA,EAClD;AAED,SAAO;AACX;;"}