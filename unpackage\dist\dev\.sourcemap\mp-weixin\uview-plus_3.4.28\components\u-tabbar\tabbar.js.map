{"version": 3, "file": "tabbar.js", "sources": ["uview-plus_3.4.28/components/u-tabbar/tabbar.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:40\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabbar.js\n */\nexport default {\n    // tabbar\n    tabbar: {\n        value: null,\n        safeAreaInsetBottom: true,\n        border: true,\n        zIndex: 1,\n        activeColor: '#1989fa',\n        inactiveColor: '#7d7e80',\n        fixed: true,\n        placeholder: true\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,OAAO;AAAA,IACP,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,aAAa;AAAA,EAChB;AACL;;"}