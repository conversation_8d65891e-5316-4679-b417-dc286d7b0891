{"version": 3, "file": "line.js", "sources": ["uview-plus_3.4.28/components/u-line/line.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:04:49\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/line.js\n */\nexport default {\n    // line组件\n    line: {\n        color: '#d6d7d9',\n        length: '100%',\n        direction: 'row',\n        hairline: true,\n        margin: 0,\n        dashed: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACL;;"}