{"version": 3, "file": "calc.js", "sources": ["uview-plus_3.4.28/libs/function/calc.js"], "sourcesContent": ["// 浮点数加法\nexport function add (arg1, arg2) {\n\tvar r1, r2, m\n\t\ttry {\n\t\t\tr1 = arg1.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t\tr1 = 0\n\t\t}\n\t\ttry {\n\t\t\tr2 = arg2.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t\tr2 = 0\n\t\t}\n\t\tm = Math.pow(10, Math.max(r1, r2))\n\treturn (arg1 * m + arg2 * m) / m\n}\n// 浮点数减法\nexport function sub (arg1, arg2) {\n\tvar r1, r2, m, n\n\t\ttry {\n\t\t  r1 = arg1.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t  r1 = 0\n\t\t}\n\t\ttry {\n\t\t  r2 = arg2.toString().split('.')[1].length\n\t\t} catch (e) {\n\t\t  r2 = 0\n\t\t}\n\t\tm = Math.pow(10, Math.max(r1, r2))\n\t\tn = (r1 >= r2) ? r1 : r2\n\treturn Math.abs(((arg1 * m - arg2 * m) / m).toFixed(n))\n}\n//浮点乘法\nexport function mul (a, b) {\n\tvar c = 0,\n\t\td = a.toString(),\n\t\te = b.toString();\n\ttry {\n\t\tc += d.split(\".\")[1].length;\n\t} catch (f) {}\n\ttry {\n\t\tc += e.split(\".\")[1].length;\n\t} catch (f) {}\n\treturn Number(d.replace(\".\", \"\")) * Number(e.replace(\".\", \"\")) / Math.pow(10, c);\n}\n//浮点除法\nexport function div (a, b) {\n\tvar c, d, e = 0,\n\t\tf = 0;\n\ttry {\n\t\te = a.toString().split(\".\")[1].length;\n\t} catch (g) {}\n\ttry {\n\t\tf = b.toString().split(\".\")[1].length;\n\t} catch (g) {}\n\treturn c = Number(a.toString().replace(\".\", \"\")), d = Number(b.toString().replace(\".\", \"\")), xyutil.mul(c / d, Math.pow(10, f - e));\n}\nexport default {\n\tadd,\n\tsub,\n\tmul,\n\tdiv\n}\n"], "names": [], "mappings": ";AACO,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI;AACX,MAAI;AACH,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACnC,SAAQ,GAAG;AACX,SAAK;AAAA,EACL;AACD,MAAI;AACH,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACnC,SAAQ,GAAG;AACX,SAAK;AAAA,EACL;AACD,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AAClC,UAAQ,OAAO,IAAI,OAAO,KAAK;AAChC;AAEO,SAAS,IAAK,MAAM,MAAM;AAChC,MAAI,IAAI,IAAI,GAAG;AACd,MAAI;AACF,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAQ,GAAG;AACV,SAAK;AAAA,EACN;AACD,MAAI;AACF,SAAK,KAAK,SAAU,EAAC,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACpC,SAAQ,GAAG;AACV,SAAK;AAAA,EACN;AACD,MAAI,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;AACjC,MAAK,MAAM,KAAM,KAAK;AACvB,SAAO,KAAK,MAAM,OAAO,IAAI,OAAO,KAAK,GAAG,QAAQ,CAAC,CAAC;AACvD;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,IAAI,GACP,IAAI,EAAE,SAAU,GAChB,IAAI,EAAE;AACP,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACvB,SAAU,GAAG;AAAA,EAAE;AACd,MAAI;AACH,SAAK,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACvB,SAAU,GAAG;AAAA,EAAE;AACd,SAAO,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,OAAO,EAAE,QAAQ,KAAK,EAAE,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC;AAChF;AAEO,SAAS,IAAK,GAAG,GAAG;AAC1B,MAAI,GAAG,GAAG,IAAI,GACb,IAAI;AACL,MAAI;AACH,QAAI,EAAE,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACjC,SAAU,GAAG;AAAA,EAAE;AACd,MAAI;AACH,QAAI,EAAE,WAAW,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACjC,SAAU,GAAG;AAAA,EAAE;AACd,SAAO,IAAI,OAAO,EAAE,SAAU,EAAC,QAAQ,KAAK,EAAE,CAAC,GAAG,IAAI,OAAO,EAAE,SAAU,EAAC,QAAQ,KAAK,EAAE,CAAC,GAAG,OAAO,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC;AACnI;AACA,MAAe,OAAA;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;;"}