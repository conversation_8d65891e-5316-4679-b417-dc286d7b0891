<template>
    <view class="u-page">
        <view class="u-demo-block">
            <text class="u-demo-block__title">基础使用</text>
            <view class="u-demo-block__content">
                <up-row customStyle="margin-bottom: 10px">
                    <up-col span="6">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="6">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                </up-row>
                <up-row customStyle="margin-bottom: 10px">
                    <up-col span="4">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="4">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="4">
                        <view class="demo-layout bg-purple-dark"></view>
                    </up-col>
                </up-row>
                <up-row justify="space-between">
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                </up-row>
            </view>
        </view>
        <view class="u-demo-block">
            <text class="u-demo-block__title">分栏间隔</text>
            <view class="u-demo-block__content">
                <up-row justify="space-between" gutter="10">
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                </up-row>
            </view>
        </view>
        <view class="u-demo-block">
            <text class="u-demo-block__title">混合布局</text>
            <view class="u-demo-block__content">
                <up-row justify="space-between" gutter="10">
                    <up-col span="2">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="4">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                    <up-col span="6">
                        <view class="demo-layout bg-purple-dark"></view>
                    </up-col>
                </up-row>
            </view>
        </view>
        <view class="u-demo-block">
            <text class="u-demo-block__title">分栏偏移</text>
            <view class="u-demo-block__content">
                <up-row
                    justify="space-between"
                    customStyle="margin-bottom: 10px"
                >
                    <up-col span="3" offset="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3" offset="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                </up-row>
                <up-row>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3" offset="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                </up-row>
            </view>
        </view>
        <view class="u-demo-block">
            <text class="u-demo-block__title">对齐方式</text>
            <view class="u-demo-block__content">
                <up-row
                    justify="space-between"
                    customStyle="margin-bottom: 10px"
                >
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                </up-row>
                <up-row>
                    <up-col span="3">
                        <view class="demo-layout bg-purple-light"></view>
                    </up-col>
                    <up-col span="3">
                        <view class="demo-layout bg-purple"></view>
                    </up-col>
                </up-row>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
};
</script>

<style lang="scss" scoped>
.wrap {
    padding: 12px;
}

.demo-layout {
	width: 100%;
    height: 25px;
    border-radius: 4px;
}

.bg-purple {
    background: #ced7e1;
}

.bg-purple-light {
    background: #e5e9f2;
}

.bg-purple-dark {
    background: #99a9bf;
}
</style>
