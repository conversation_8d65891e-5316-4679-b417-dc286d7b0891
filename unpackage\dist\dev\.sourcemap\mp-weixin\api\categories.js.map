{"version": 3, "file": "categories.js", "sources": ["api/categories.js"], "sourcesContent": ["import request from './request.js';\n\n/**\n * 获取分类列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getCategoryList(params = {}) {\n  return request.get('/api/categories', params);\n}\n\n/**\n * 获取分类树形列表\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getCategoryTreeList(params = {}) {\n  return request.get('/api/categories/tree', params);\n}\n\n/**\n * 获取分类详情\n * @param {String|Number} id - 分类ID\n * @returns {Promise} - 返回Promise对象\n */\nexport function getCategoryDetail(id) {\n  return request.get(`/api/categories/${id}`);\n}\n\n/**\n * 获取分类下的文章列表\n * @param {String|Number} categoryId - 分类ID\n * @param {Object} params - 查询参数\n * @returns {Promise} - 返回Promise对象\n */\nexport function getCategoryArticles(categoryId, params = {}) {\n  return request.get(`/api/categories/${categoryId}/articles`, params);\n}\n"], "names": ["request"], "mappings": ";;AAgBO,SAAS,oBAAoB,SAAS,IAAI;AAC/C,SAAOA,oBAAQ,IAAI,wBAAwB,MAAM;AACnD;AAiBO,SAAS,oBAAoB,YAAY,SAAS,IAAI;AAC3D,SAAOA,YAAAA,QAAQ,IAAI,mBAAmB,UAAU,aAAa,MAAM;AACrE;;;"}