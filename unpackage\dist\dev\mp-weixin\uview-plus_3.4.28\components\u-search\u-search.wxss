/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.u-empty.data-v-869d5ec9,
.u-empty__wrap.data-v-869d5ec9,
.u-tabs.data-v-869d5ec9,
.u-tabs__wrapper.data-v-869d5ec9,
.u-tabs__wrapper__scroll-view-wrapper.data-v-869d5ec9,
.u-tabs__wrapper__scroll-view.data-v-869d5ec9,
.u-tabs__wrapper__nav.data-v-869d5ec9,
.u-tabs__wrapper__nav__line.data-v-869d5ec9,
.up-empty.data-v-869d5ec9,
.up-empty__wrap.data-v-869d5ec9,
.up-tabs.data-v-869d5ec9,
.up-tabs__wrapper.data-v-869d5ec9,
.up-tabs__wrapper__scroll-view-wrapper.data-v-869d5ec9,
.up-tabs__wrapper__scroll-view.data-v-869d5ec9,
.up-tabs__wrapper__nav.data-v-869d5ec9,
.up-tabs__wrapper__nav__line.data-v-869d5ec9 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-search.data-v-869d5ec9 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-search__content.data-v-869d5ec9 {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 10px;
  flex: 1;
  justify-content: space-between;
  border-width: 1px;
  border-color: transparent;
  border-style: solid;
  overflow: hidden;
}
.u-search__content__icon.data-v-869d5ec9 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-search__content__label.data-v-869d5ec9 {
  color: #303133;
  font-size: 14px;
  margin: 0 4px;
}
.u-search__content__close.data-v-869d5ec9 {
  width: 20px;
  height: 20px;
  border-top-left-radius: 100px;
  border-top-right-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  background-color: #C6C7CB;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.82);
}
.u-search__content__input.data-v-869d5ec9 {
  flex: 1;
  font-size: 14px;
  line-height: 1;
  margin: 0 5px;
  color: #303133;
}
.u-search__content__input--placeholder.data-v-869d5ec9 {
  color: #909193;
}
.u-search__action.data-v-869d5ec9 {
  font-size: 14px;
  color: #303133;
  width: 0;
  overflow: hidden;
  transition-property: width;
  transition-duration: 0.3s;
  white-space: nowrap;
  text-align: center;
}
.u-search__action--active.data-v-869d5ec9 {
  width: 40px;
  margin-left: 5px;
}
.u-search__reverse .u-search__content__icon.data-v-869d5ec9 {
  order: 3;
}
.u-search__reverse .u-search__content__close.data-v-869d5ec9 {
  order: 2;
}