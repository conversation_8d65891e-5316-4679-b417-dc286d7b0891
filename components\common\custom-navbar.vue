<template>
	<view class="custom-navbar">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

		<!-- 导航栏内容 -->
		<view class="navbar-content">
			<!-- 左侧区域 -->
			<view class="left-area">
				<slot name="left">
					<view class="left-slot">
						<view v-if="showBack" class="back-icon" @click="onBackClick">
							<u-icon name="arrow-left" color="#FFFFFF" size="20"></u-icon>
						</view>
						<view v-if="showBack && showHome" class="divider-line">
							<u-line direction="column" :hairline="false" length="16" color="#FFFFFF" width="1"></u-line>
						</view>
						<view v-if="showHome" class="home-icon" @click="onHomeClick">
							<u-icon name="home" color="#FFFFFF" size="20"></u-icon>
						</view>
					</view>
				</slot>
			</view>

			<!-- 标题区域 -->
			<view class="title-area">
				<slot name="center">
					<text class="title-text">{{ title }}</text>
				</slot>
			</view>

			<!-- 右侧区域 -->
			<view class="right-area" @click="onRightClick">
				<slot name="right">
					<view class="right-slot">
						<u-icon v-if="rightIcon" :name="rightIcon" color="#FFFFFF" size="20"></u-icon>
						<text v-if="rightText" class="right-text">{{ rightText }}</text>
					</view>
				</slot>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'custom-navbar',
		props: {
			// 标题
			title: {
				type: String,
				default: ''
			},
			// 是否显示返回图标
			showBack: {
				type: Boolean,
				default: true
			},
			// 是否显示返回首页图标
			showHome: {
				type: Boolean,
				default: false
			},
			// 右侧图标
			rightIcon: {
				type: String,
				default: ''
			},
			// 右侧文字
			rightText: {
				type: String,
				default: ''
			},
			// 是否自动返回上一页
			autoBack: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				statusBarHeight: 20 // 状态栏高度，默认值
			}
		},
		created() {
			// 获取状态栏高度
			this.getStatusBarHeight();
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				try {
					// 使用uni-app提供的API获取窗口信息
					const windowInfo = uni.getWindowInfo();
					this.statusBarHeight = windowInfo.statusBarHeight || 20;
				} catch (e) {
					console.error('获取状态栏高度失败:', e);
					// 设置默认值
					this.statusBarHeight = 20;
				}
			},

			// 返回按钮点击事件
			onBackClick() {
				// 触发事件，让父组件可以监听
				this.$emit('leftClick');

				// 如果是首页，不执行返回操作
				const pages = getCurrentPages();
				if (pages.length <= 1) {
					uni.switchTab({
						url: '/pages/index/index'
					});
					return;
				}

				// 正常返回上一页
				uni.navigateBack({
					delta: 1,
					fail: () => {
						// 如果返回失败，跳转到首页
						uni.switchTab({
							url: '/pages/index/index'
						});
					}
				});
			},

			// 首页按钮点击事件
			onHomeClick() {
				// 触发事件，让父组件可以监听
				this.$emit('homeClick');

				// 直接跳转到首页
				uni.navigateTo({
					url: '/pages/index/index',
					fail: (err) => {
						console.error('跳转到首页失败:', err);
						// 如果当前已经在首页，可能会失败，此时尝试重新加载首页
						uni.reLaunch({
							url: '/pages/index/index',
							fail: (err2) => {
								console.error('reLaunch跳转到首页也失败:', err2);
								uni.showToast({
									title: '跳转到首页失败',
									icon: 'none'
								});
							}
						});
					}
				});
			},

			// 右侧点击事件
			onRightClick() {
				this.$emit('rightClick');
			}
		}
	}
</script>

<style lang="scss">
	.custom-navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background-color: #D9001B;

		.status-bar {
			width: 100%;
		}

		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 50px; /* 增加导航栏高度 */
			padding: 0 10px;

			.left-area {
				display: flex;
				align-items: center;
				justify-content: flex-start;
				background-color: #C7250D;
				border-radius: 20px;
				padding: 4px 8px;

				.left-slot {
					display: flex;
					align-items: center;
					justify-content: space-between;
					width: 100%;
				}

				.back-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30px;
				}

				.divider-line {
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0 5px;
				}

				.home-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 30px;
				}
			}

			.title-area {
				flex: 1;
				text-align: center;

				.title-text {
					color: #FFFFFF;
					font-size: 18px;
					font-weight: 500;
				}
			}

			.right-area {
				width: 80px;
				display: flex;
				align-items: center;
				justify-content: flex-end;

				.right-slot {
					display: flex;
					align-items: center;
				}

				.right-text {
					color: #FFFFFF;
					font-size: 14px;
				}
			}
		}
	}
</style>
