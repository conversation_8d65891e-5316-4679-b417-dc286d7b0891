{"version": 3, "file": "u-row-notice.js", "sources": ["uview-plus_3.4.28/components/u-row-notice/u-row-notice.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3Utcm93LW5vdGljZS91LXJvdy1ub3RpY2UudnVl"], "sourcesContent": ["<template>\n\t<view\n\t\tclass=\"u-notice\"\n\t\t@tap=\"clickHandler\"\n\t>\n\t\t<slot name=\"icon\">\n\t\t\t<view\n\t\t\t\tclass=\"u-notice__left-icon\"\n\t\t\t\tv-if=\"icon\"\n\t\t\t>\n\t\t\t\t<u-icon\n\t\t\t\t\t:name=\"icon\"\n\t\t\t\t\t:color=\"color\"\n\t\t\t\t\tsize=\"19\"\n\t\t\t\t></u-icon>\n\t\t\t</view>\n\t\t</slot>\n\t\t<view\n\t\t\tclass=\"u-notice__content\"\n\t\t\tref=\"u-notice__content\"\n\t\t>\n\t\t\t<view\n\t\t\t\tref=\"u-notice__content__text\"\n\t\t\t\tclass=\"u-notice__content__text\"\n\t\t\t\t:style=\"[animationStyle]\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tv-for=\"(item, index) in innerText\"\n\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t:style=\"[textStyle]\"\n\t\t\t\t>{{item}}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<view\n\t\t\tclass=\"u-notice__right-icon\"\n\t\t\tv-if=\"['link', 'closable'].includes(mode)\"\n\t\t>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'link'\"\n\t\t\t\tname=\"arrow-right\"\n\t\t\t\t:size=\"17\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t\t<u-icon\n\t\t\t\tv-if=\"mode === 'closable'\"\n\t\t\t\t@click=\"close\"\n\t\t\t\tname=\"close\"\n\t\t\t\t:size=\"16\"\n\t\t\t\t:color=\"color\"\n\t\t\t></u-icon>\n\t\t</view>\n\t</view>\n</template>\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, error, sleep, getPx } from '../../libs/function/index';\n\timport test from '../../libs/function/test';\n\t// #ifdef APP-NVUE\n\tconst animation = uni.requireNativePlugin('animation')\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * RowNotice 滚动通知中的水平滚动模式\n\t * @description 水平滚动\n\t * @tutorial https://ijry.github.io/uview-plus/components/noticeBar.html\n\t * @property {String | Number}\ttext\t\t\t显示的内容，字符串\n\t * @property {String}\t\t\ticon\t\t\t是否显示左侧的音量图标 (默认 'volume' )\n\t * @property {String}\t\t\tmode\t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色，各图标也会使用文字颜色 (默认 '#f9ae3d' )\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 (默认 ''#fdf6ec' )\n\t * @property {String | Number}\tfontSize\t\t字体大小，单位px (默认 14 )\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度  (默认 80 )\n\t * \n\t * @event {Function} click 点击通告文字触发\n\t * @event {Function} close 点击右侧关闭图标触发\n\t * @example \n\t */\n\texport default {\n\t\tname: 'u-row-notice',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tanimationDuration: '0', // 动画执行时间\n\t\t\t\tanimationPlayState: 'paused', // 动画的开始和结束执行\n\t\t\t\t// nvue下，内容发生变化，导致滚动宽度也变化，需要标志为是否需要重新计算宽度\n\t\t\t\t// 不能在内容变化时直接重新计算，因为nvue的animation模块上一次的滚动不是刚好结束，会有影响\n\t\t\t\tnvueInit: true,\n\t\t\t\tshow: true\n\t\t\t};\n\t\t},\n\t\twatch: {\n\t\t\ttext: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler(newValue, oldValue) {\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tthis.nvueInit = true\n\t\t\t\t\t// #endif\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tthis.vue()\n\t\t\t\t\t// #endif\n\t\t\t\t\t\n\t\t\t\t\tif(!test.string(newValue)) {\n\t\t\t\t\t\terror('noticebar组件direction为row时，要求text参数为字符串形式')\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfontSize() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = true\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tspeed() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = true\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 文字内容的样式\n\t\t\ttextStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.whiteSpace = 'nowrap !important'\n\t\t\t\tstyle.color = this.color\n\t\t\t\tstyle.fontSize = addUnit(this.fontSize)\n\t\t\t\treturn style\n\t\t\t},\n\t\t\tanimationStyle() {\n\t\t\t\tlet style = {}\n\t\t\t\tstyle.animationDuration = this.animationDuration\n\t\t\t\tstyle.animationPlayState = this.animationPlayState\n\t\t\t\treturn style\n\t\t\t},\n\t\t\t// 内部对用户传入的数据进一步分割，放到多个text标签循环，否则如果用户传入的字符串很长（100个字符以上）\n\t\t\t// 放在一个text标签中进行滚动，在低端安卓机上，动画可能会出现抖动现象，需要分割到多个text中可解决此问题\n\t\t\tinnerText() {\n\t\t\t\tlet result = [],\n\t\t\t\t\t// 每组text标签的字符长度\n\t\t\t\t\tlen = 20\n\t\t\t\tconst textArr = this.text.split('')\n\t\t\t\tfor (let i = 0; i < textArr.length; i += len) {\n\t\t\t\t\t// 对拆分的后的text进行slice分割，得到的为数组再进行join拼接为字符串\n\t\t\t\t\tresult.push(textArr.slice(i, i + len).join(''))\n\t\t\t\t}\n\t\t\t\treturn result\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\t// #ifdef APP-PLUS\n\t\t\t// 在APP上(含nvue)，监听当前webview是否处于隐藏状态(进入下一页时即为hide状态)\n\t\t\t// 如果webivew隐藏了，为了节省性能的损耗，应停止动画的执行，同时也是为了保持进入下一页返回后，滚动位置保持不变\n\t\t\tvar pages = getCurrentPages()\n\t\t\tvar page = pages[pages.length - 1]\n\t\t\tvar currentWebview = page.$getAppWebview()\n\t\t\tcurrentWebview.addEventListener('hide', () => {\n\t\t\t\tthis.webviewHide = true\n\t\t\t})\n\t\t\tcurrentWebview.addEventListener('show', () => {\n\t\t\t\tthis.webviewHide = false\n\t\t\t})\n\t\t\t// #endif\n\n\t\t\tthis.init()\n\t\t},\n\t\temits: [\"click\", \"close\"],\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvue()\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.vue()\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\tif(!test.string(this.text)) {\n\t\t\t\t\terror('noticebar组件direction为row时，要求text参数为字符串形式')\n\t\t\t\t}\n\t\t\t},\n\t\t\t// vue版处理\n\t\t\tasync vue() {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tlet boxWidth = 0,\n\t\t\t\t\ttextWidth = 0\n\t\t\t\t// 进行一定的延时\n\t\t\t\tawait sleep()\n\t\t\t\t// 查询盒子和文字的宽度\n\t\t\t\ttextWidth = (await this.$uGetRect('.u-notice__content__text')).width\n\t\t\t\tboxWidth = (await this.$uGetRect('.u-notice__content')).width\n\t\t\t\t// 根据t=s/v(时间=路程/速度)，这里为何不需要加上#u-notice-box的宽度，因为中设置了.u-notice-content样式中设置了padding-left: 100%\n\t\t\t\t// 恰巧计算出来的结果中已经包含了#u-notice-box的宽度\n\t\t\t\tthis.animationDuration = `${textWidth / getPx(this.speed)}s`\n\t\t\t\t// 这里必须这样开始动画，否则在APP上动画速度不会改变\n\t\t\t\tthis.animationPlayState = 'paused'\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.animationPlayState = 'running'\n\t\t\t\t}, 10)\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// nvue版处理\n\t\t\tasync nvue() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis.nvueInit = false\n\t\t\t\tlet boxWidth = 0,\n\t\t\t\t\ttextWidth = 0\n\t\t\t\t// 进行一定的延时\n\t\t\t\tawait sleep()\n\t\t\t\t// 查询盒子和文字的宽度\n\t\t\t\ttextWidth = (await this.getNvueRect('u-notice__content__text')).width\n\t\t\t\tboxWidth = (await this.getNvueRect('u-notice__content')).width\n\t\t\t\t// 将文字移动到盒子的右边沿，之所以需要这么做，是因为nvue不支持100%单位，否则可以通过css设置\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\ttransform: `translateX(${boxWidth}px)`\n\t\t\t\t\t},\n\t\t\t\t}, () => {\n\t\t\t\t\t// 如果非禁止动画，则开始滚动\n\t\t\t\t\t!this.stopAnimation && this.loopAnimation(textWidth, boxWidth)\n\t\t\t\t});\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tloopAnimation(textWidth, boxWidth) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\tstyles: {\n\t\t\t\t\t\t// 目标移动终点为-textWidth，也即当文字的最右边贴到盒子的左边框的位置\n\t\t\t\t\t\ttransform: `translateX(-${textWidth}px)`\n\t\t\t\t\t},\n\t\t\t\t\t// 滚动时间的计算为，时间 = 路程(boxWidth + textWidth) / 速度，最后转为毫秒\n\t\t\t\t\tduration: (boxWidth + textWidth) / getPx(this.speed) * 1000,\n\t\t\t\t\tdelay: 10\n\t\t\t\t}, () => {\n\t\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\n\t\t\t\t\t\tstyles: {\n\t\t\t\t\t\t\t// 重新将文字移动到盒子的右边沿\n\t\t\t\t\t\t\ttransform: `translateX(${this.stopAnimation ? 0 : boxWidth}px)`\n\t\t\t\t\t\t},\n\t\t\t\t\t}, () => {\n\t\t\t\t\t\t// 如果非禁止动画，则继续下一轮滚动\n\t\t\t\t\t\tif (!this.stopAnimation) {\n\t\t\t\t\t\t\t// 判断是否需要初始化计算尺寸\n\t\t\t\t\t\t\tif (this.nvueInit) {\n\t\t\t\t\t\t\t\tthis.nvue()\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.loopAnimation(textWidth, boxWidth)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgetNvueRect(el) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 返回一个promise\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(this.$refs[el], (res) => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 点击通告栏\n\t\t\tclickHandler(index) {\n\t\t\t\tthis.$emit('click')\n\t\t\t},\n\t\t\t// 点击右侧按钮，需要判断点击的是关闭图标还是箭头图标\n\t\t\tclose() {\n\t\t\t\tthis.$emit('close')\n\t\t\t}\n\t\t},\n\t\tbeforeUnmount() {\n\t\t\tthis.stopAnimation = true\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-notice {\n\t\t@include flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\n\t\t&__left-icon {\n\t\t\talign-items: center;\n\t\t\tmargin-right: 5px;\n\t\t}\n\n\t\t&__right-icon {\n\t\t\tmargin-left: 5px;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t&__content {\n\t\t\ttext-align: right;\n\t\t\tflex: 1;\n\t\t\t@include flex;\n\t\t\tflex-wrap: nowrap;\n\t\t\toverflow: hidden;\n\n\t\t\t&__text {\n\t\t\t\tfont-size: 14px;\n\t\t\t\tcolor: $u-warning;\n\t\t\t\t/* #ifndef APP-NVUE */\n\t\t\t\t// 这一句很重要，为了能让滚动左右连接起来\n\t\t\t\tpadding-left: 100%;\n\t\t\t\tword-break: keep-all;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\tanimation: u-loop-animation 10s linear infinite both;\n\t\t\t\t/* #endif */\n\t\t\t\t@include flex(row);\n\t\t\t\tline-height: 100%;\n\t\t\t}\n\t\t}\n\n\t}\n\n\t/* #ifndef APP-NVUE */\n\t@keyframes u-loop-animation {\n\t\t0% {\n\t\t\ttransform: translate3d(0, 0, 0);\n\t\t}\n\t\n\t\t100% {\n\t\t\ttransform: translate3d(-100%, 0, 0);\n\t\t}\n\t}\n\t/* #endif */\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-row-notice/u-row-notice.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "test", "error", "addUnit", "sleep", "getPx"], "mappings": ";;;;;;;AA+EC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,kDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,mBAAmB;AAAA;AAAA,MACnB,oBAAoB;AAAA;AAAA;AAAA;AAAA,MAGpB,UAAU;AAAA,MACV,MAAM;AAAA;EAEP;AAAA,EACD,OAAO;AAAA,IACN,MAAM;AAAA,MACL,WAAW;AAAA,MACX,QAAQ,UAAU,UAAU;AAK3B,aAAK,IAAI;AAGT,YAAG,CAACC,oCAAI,KAAC,OAAO,QAAQ,GAAG;AAC1BC,+CAAAA,MAAM,0CAA0C;AAAA,QACjD;AAAA,MACD;AAAA,IACA;AAAA,IACD,WAAW;AAKV,WAAK,IAAI;AAAA,IAET;AAAA,IACD,QAAQ;AAKP,WAAK,IAAI;AAAA,IAEV;AAAA,EACA;AAAA,EACD,UAAU;AAAA;AAAA,IAET,YAAY;AACX,UAAI,QAAQ,CAAC;AACb,YAAM,aAAa;AACnB,YAAM,QAAQ,KAAK;AACnB,YAAM,WAAWC,6CAAQ,KAAK,QAAQ;AACtC,aAAO;AAAA,IACP;AAAA,IACD,iBAAiB;AAChB,UAAI,QAAQ,CAAC;AACb,YAAM,oBAAoB,KAAK;AAC/B,YAAM,qBAAqB,KAAK;AAChC,aAAO;AAAA,IACP;AAAA;AAAA;AAAA,IAGD,YAAY;AACX,UAAI,SAAS,CAAE,GAEd,MAAM;AACP,YAAM,UAAU,KAAK,KAAK,MAAM,EAAE;AAClC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,KAAK;AAE7C,eAAO,KAAK,QAAQ,MAAM,GAAG,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AAAA,MAC/C;AACA,aAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,UAAU;AAeT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,OAAO,CAAC,SAAS,OAAO;AAAA,EACxB,SAAS;AAAA,IACR,OAAO;AAMN,WAAK,IAAI;AAGT,UAAG,CAACF,oCAAAA,KAAK,OAAO,KAAK,IAAI,GAAG;AAC3BC,6CAAAA,MAAM,0CAA0C;AAAA,MACjD;AAAA,IACA;AAAA;AAAA,IAED,MAAM,MAAM;AAEP,UACH,YAAY;AAEb,YAAME,2CAAM;AAEZ,mBAAa,MAAM,KAAK,UAAU,0BAA0B,GAAG;AACpD,OAAC,MAAM,KAAK,UAAU,oBAAoB,GAAG;AAGxD,WAAK,oBAAoB,GAAG,YAAYC,qCAAK,MAAC,KAAK,KAAK,CAAC;AAEzD,WAAK,qBAAqB;AAC1B,iBAAW,MAAM;AAChB,aAAK,qBAAqB;AAAA,MAC1B,GAAE,EAAE;AAAA,IAEL;AAAA;AAAA,IAED,MAAM,OAAO;AAAA,IAoBZ;AAAA,IACD,cAAc,WAAW,UAAU;AAAA,IA6BlC;AAAA,IACD,YAAY,IAAI;AAAA,IASf;AAAA;AAAA,IAED,aAAa,OAAO;AACnB,WAAK,MAAM,OAAO;AAAA,IAClB;AAAA;AAAA,IAED,QAAQ;AACP,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACA;AAAA,EACD,gBAAgB;AACf,SAAK,gBAAgB;AAAA,EACtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtRF,GAAG,gBAAgB,SAAS;"}