{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-column-notice/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 显示的内容，字符串\n        text: {\n            type: [Array],\n            default: () => defProps.columnNotice.text\n        },\n        // 是否显示左侧的音量图标\n        icon: {\n            type: String,\n            default: () => defProps.columnNotice.icon\n        },\n        // 通告模式，link-显示右箭头，closable-显示右侧关闭图标\n        mode: {\n            type: String,\n            default: () => defProps.columnNotice.mode\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: () => defProps.columnNotice.color\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: () => defProps.columnNotice.bgColor\n        },\n        // 字体大小，单位px\n        fontSize: {\n            type: [String, Number],\n            default: () => defProps.columnNotice.fontSize\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少px(px)，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: [String, Number],\n            default: () => defProps.columnNotice.speed\n        },\n        // direction = row时，是否使用步进形式滚动\n        step: {\n            type: Boolean,\n            default: () => defProps.columnNotice.step\n        },\n        // 滚动一个周期的时间长，单位ms\n        duration: {\n            type: [String, Number],\n            default: () => defProps.columnNotice.duration\n        },\n        // 是否禁止用手滑动切换\n        // 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序\n        disableTouch: {\n            type: Boolean,\n            default: () => defProps.columnNotice.disableTouch\n        },\n\t\tjustifyContent: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.columnNotice.justifyContent\n\t\t}\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM,CAAC,KAAK;AAAA,MACZ,SAAS,MAAMC,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA;AAAA;AAAA,IAGD,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACxC;AAAA,IACP,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,aAAa;AAAA,IACrC;AAAA,EACE;AACL,CAAC;;"}