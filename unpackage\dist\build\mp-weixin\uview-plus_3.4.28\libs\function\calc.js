"use strict";const t={add:function(t,r){var n,a,c;try{n=t.toString().split(".")[1].length}catch(e){n=0}try{a=r.toString().split(".")[1].length}catch(e){a=0}return(t*(c=Math.pow(10,Math.max(n,a)))+r*c)/c},sub:function(t,r){var n,a,c,e;try{n=t.toString().split(".")[1].length}catch(i){n=0}try{a=r.toString().split(".")[1].length}catch(i){a=0}return c=Math.pow(10,Math.max(n,a)),e=n>=a?n:a,Math.abs(((t*c-r*c)/c).toFixed(e))},mul:function(t,r){var n=0,a=t.toString(),c=r.toString();try{n+=a.split(".")[1].length}catch(e){}try{n+=c.split(".")[1].length}catch(e){}return Number(a.replace(".",""))*Number(c.replace(".",""))/Math.pow(10,n)},div:function(t,r){var n,a,c=0,e=0;try{c=t.toString().split(".")[1].length}catch(i){}try{e=r.toString().split(".")[1].length}catch(i){}return n=Number(t.toString().replace(".","")),a=Number(r.toString().replace(".","")),xyutil.mul(n/a,Math.pow(10,e-c))}};exports.calc=t;
