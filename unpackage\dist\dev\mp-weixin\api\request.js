"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("../utils/config.js");
const request = (options) => {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: utils_config.config.apiBaseUrl + options.url,
      data: options.data || options.params,
      method: options.method || "GET",
      header: {
        "content-type": "application/json;charset=utf-8",
        ...options.header
      },
      timeout: 1e4,
      // 请求超时时间，单位ms
      dataType: "json",
      success: (res) => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          common_vendor.index.__f__("error", "at api/request.js:26", "请求异常:", res);
          reject(res);
        }
      },
      fail: (err) => {
        common_vendor.index.__f__("error", "at api/request.js:32", "请求失败:", err);
        reject(err);
      }
    };
    common_vendor.index.request(requestOptions);
  });
};
const get = (url, params = {}, header = {}) => {
  return request({
    url,
    params,
    method: "GET",
    header
  });
};
const post = (url, data = {}, header = {}) => {
  return request({
    url,
    data,
    method: "POST",
    header
  });
};
const request$1 = {
  request,
  get,
  post
};
exports.request = request$1;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/request.js.map
