<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础功能</text>
			<up-swiper
				:list="list1"
				@change="change"
				@click="click"
			></up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带标题</text>
			<up-swiper
				:list="list2"
				keyName="image"
				showTitle
				:autoplay="false"
				circular
			></up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带指示器</text>
			<up-swiper
				:list="list3"
				indicator
				indicatorMode="line"
				circular
			></up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">加载中</text>
			<up-swiper
				:list="list3"
				loading
			></up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">嵌入视频</text>
			<up-swiper
				:list="list4"
				keyName="url"
				:autoplay="false"
			></up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义内容</text>
			<up-swiper
				:list="list2"
				keyName="image"
				showTitle
				:autoplay="false"
				circular
			>
				<template #default="scope">
					<image :src="scope.item.image"></image>
				</template>
			</up-swiper>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义指示器</text>
			<up-swiper
				:list="list5"
				@change="e => current = e.current"
				:autoplay="false"
			>
				<template #indicator>
					<view
						class="indicator"
					>
						<view
							class="indicator__dot"
							v-for="(item, index) in list5"
							:key="index"
							:class="[index === current && 'indicator__dot--active']"
						>
						</view>
					</view>
				</template>
			</up-swiper>
			<up-gap
				bgColor="transparent"
				height="15"
			></up-gap>
			<up-swiper
				:list="list6"
				@change="e => currentNum = e.current"
				:autoplay="false"
				indicatorStyle="right: 20px"
			>
				<template #indicator>
					<view
						class="indicator-num"
					>
						<text class="indicator-num__text">{{ currentNum + 1 }}/{{ list6.length }}</text>
					</view>
				</template>
			</up-swiper>
		</view>
		<!-- #ifndef APP-NVUE || MP-TOUTIAO -->
		<view class="u-demo-block">
			<text class="u-demo-block__title">卡片式</text>
			<up-swiper
				:list="list3"
				previousMargin="30"
				nextMargin="30"
				circular
				:autoplay="false"
				radius="5"
				bgColor="#ffffff"
			></up-swiper>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current: 0,
				currentNum: 0,
				list: [{
						// image: 'https://uview-plus.jiangruyi.com/uview/resources/video.mp4',
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						poster: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png'
					},
					{
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list1: [
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
				],
				list2: [{
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						type: 'image',
					},
					{
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list3: [
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
				],
				list4: [{
						url: 'https://uview-plus.jiangruyi.com/uview/resources/video.mp4',
						title: '昨夜星辰昨夜风，画楼西畔桂堂东',
						poster: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
					},
					{
						url: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
						title: '身无彩凤双飞翼，心有灵犀一点通'
					},
					{
						url: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
						title: '谁念西风独自凉，萧萧黄叶闭疏窗，沉思往事立残阳'
					}
				],
				list5: [
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
				],
				list6: [
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png',
					'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png',
				]
			}
		},
		methods: {
			change(e) {
				// console.log('change', e);
			},
			click(e) {
				console.log('click', e);
			}
		},
	}
</script>

<style lang="scss">
	.indicator {
		@include flex(row);
		justify-content: center;

		&__dot {
			height: 6px;
			width: 6px;
			border-radius: 100px;
			background-color: rgba(255, 255, 255, 0.35);
			margin: 0 5px;
			transition: background-color 0.3s;

			&--active {
				background-color: #ffffff;
			}
		}
	}

	.indicator-num {
		padding: 2px 0;
		background-color: rgba(0, 0, 0, 0.35);
		border-radius: 100px;
		width: 35px;
		@include flex;
		justify-content: center;

		&__text {
			color: #FFFFFF;
			font-size: 12px;
		}
	}
</style>
