{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-list/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 控制是否出现滚动条，仅nvue有效\n        showScrollbar: {\n            type: Boolean,\n            default: () => defProps.list.showScrollbar\n        },\n        // 距底部多少时触发scrolltolower事件\n        lowerThreshold: {\n            type: [String, Number],\n            default: () => defProps.list.lowerThreshold\n        },\n        // 距顶部多少时触发scrolltoupper事件，非nvue有效\n        upperThreshold: {\n            type: [String, Number],\n            default: () => defProps.list.upperThreshold\n        },\n        // 设置竖向滚动条位置\n        scrollTop: {\n            type: [String, Number],\n            default: () => defProps.list.scrollTop\n        },\n        // 控制 onscroll 事件触发的频率，仅nvue有效\n        offsetAccuracy: {\n            type: [String, Number],\n            default: () => defProps.list.offsetAccuracy\n        },\n        // 启用 flexbox 布局。开启后，当前节点声明了display: flex就会成为flex container，并作用于其孩子节点，仅微信小程序有效\n        enableFlex: {\n            type: Boolean,\n            default: () => defProps.list.enableFlex\n        },\n        // 是否按分页模式显示List，默认值false\n        pagingEnabled: {\n            type: Boolean,\n            default: () => defProps.list.pagingEnabled\n        },\n        // 是否允许List滚动\n        scrollable: {\n            type: Boolean,\n            default: () => defProps.list.scrollable\n        },\n        // 值应为某子元素id（id不能以数字开头）\n        scrollIntoView: {\n            type: String,\n            default: () => defProps.list.scrollIntoView\n        },\n        // 在设置滚动条位置时使用动画过渡\n        scrollWithAnimation: {\n            type: Boolean,\n            default: () => defProps.list.scrollWithAnimation\n        },\n        // iOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只对微信小程序有效\n        enableBackToTop: {\n            type: Boolean,\n            default: () => defProps.list.enableBackToTop\n        },\n        // 列表的高度\n        height: {\n            type: [String, Number],\n            default: () => defProps.list.height\n        },\n        // 列表宽度\n        width: {\n            type: [String, Number],\n            default: () => defProps.list.width\n        },\n        // 列表前后预渲染的屏数，1代表一个屏幕的高度，1.5代表1个半屏幕高度\n        preLoadScreen: {\n            type: [String, Number],\n            default: () => defProps.list.preLoadScreen\n        },\n        // 开启自定义下拉刷新\n        refresherEnabled: {\n            type: Boolean,\n            default: () => false\n        },\n        // 设置自定义下拉刷新阈值\t\n        refresherThreshold: {\n            type: Number,\n            default: () => 45\n        },\n        // 设置自定义下拉刷新默认样式，支持设置 black，white，none，none 表示不使用默认样式\n        refresherDefaultStyle: {\n            type: String,\n            default: () => 'black'\n        },\n        // 设置自定义下拉刷新区域背景颜色\n        refresherBackground: {\n            type: String,\n            default: () => '#FFF'\n        },\n        // 设置当前下拉刷新状态，true 表示下拉刷新已经被触发，false 表示下拉刷新未被触发\n        refresherTriggered: {\n            type: Boolean,\n            default: () => false\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAED,oBAAoB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAED,uBAAuB;AAAA,MACnB,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAED,oBAAoB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA,EACJ;AACL,CAAC;;"}