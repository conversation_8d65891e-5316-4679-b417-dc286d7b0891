{"version": 3, "file": "brand-article.js", "sources": ["pages/party/category-articles/brand-article.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFydHkvY2F0ZWdvcnktYXJ0aWNsZXMvYnJhbmQtYXJ0aWNsZS52dWU"], "sourcesContent": ["<template>\n\t<view class=\"common-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar title=\"品牌文章详情\" :showBack=\"true\" :showHome=\"true\" @leftClick=\"goBack\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 文章标题 -->\n\t\t<view class=\"article-header\">\n\t\t\t<view class=\"article-title\">\n\t\t\t\t<text>{{ article.title || '加载中...' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"article-meta\">\n\t\t\t\t<view v-if=\"article.created_at\">\n\t\t\t\t\t<text>发布时间：{{ formatDate(article.created_at) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view v-if=\"article.category_id\">\n\t\t\t\t\t<text>分类ID：{{ article.category_id }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 文章封面图 -->\n\t\t<view class=\"article-cover\" v-if=\"coverImage\">\n\t\t\t<image :src=\"getFullImageUrl(coverImage.url)\" :alt=\"coverImage.altText || article.title\" mode=\"widthFix\"></image>\n\t\t</view>\n\n\t\t<!-- 文章内容 -->\n\t\t<view class=\"article-content\">\n\t\t\t<rich-text :nodes=\"formatContent(article.content) || '加载中...'\"></rich-text>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"article-footer\">\n\t\t\t<u-button type=\"primary\" text=\"返回列表\" @click=\"goBack\" :customStyle=\"{backgroundColor: '#D9001B'}\"></u-button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { brandArticleApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\timport { getFullImageUrl as getFullImageUrlUtil, fetchImageById } from '@/utils/image.js';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tarticleId: null,\n\t\t\t\tarticle: {\n\t\t\t\t\tid: null,\n\t\t\t\t\ttitle: '',\n\t\t\t\t\tcontent: '',\n\t\t\t\t\tcreated_at: '',\n\t\t\t\t\tcategory_id: null,\n\t\t\t\t\tcoverImageId: null\n\t\t\t\t},\n\t\t\t\tcoverImage: null,\n\t\t\t\tloading: true\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取传递过来的文章ID\n\t\t\tif (options && options.id) {\n\t\t\t\tthis.articleId = options.id;\n\t\t\t\tthis.fetchBrandArticleDetail();\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '文章ID不存在',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.goBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取品牌文章详情\n\t\t\tasync fetchBrandArticleDetail() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\tconst result = await brandArticleApi.getBrandArticleDetail(this.articleId);\n\t\t\t\t\tconsole.log('获取品牌文章详情成功:', result);\n\n\t\t\t\t\tif (result && result.success && result.data) {\n\t\t\t\t\t\tthis.article = result.data;\n\t\t\t\t\t\t// 如果没有内容，设置默认内容\n\t\t\t\t\t\tif (!this.article.content) {\n\t\t\t\t\t\t\tthis.article.content = '这是一篇关于\"' + this.article.title + '\"的文章内容。\\n\\n由于API暂未提供完整内容，这里展示的是默认内容。';\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 获取封面图片\n\t\t\t\t\t\tif (this.article.coverImageId) {\n\t\t\t\t\t\t\tthis.fetchCoverImage(this.article.coverImageId);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '获取文章详情失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取品牌文章详情失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取文章详情失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 格式化日期\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '';\n\t\t\t\tconst date = new Date(dateString);\n\t\t\t\treturn `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n\t\t\t},\n\t\t\t// 格式化内容为HTML\n\t\t\tformatContent(content) {\n\t\t\t\tif (!content) return '';\n\n\t\t\t\t// 简单的文本转HTML处理\n\t\t\t\tlet html = content\n\t\t\t\t\t// 换行转为<br>\n\t\t\t\t\t.replace(/\\n/g, '<br>')\n\t\t\t\t\t// 段落\n\t\t\t\t\t.replace(/<br><br>/g, '</p><p>');\n\n\t\t\t\t// 确保内容被包裹在段落标签中\n\t\t\t\tif (!html.startsWith('<')) {\n\t\t\t\t\thtml = '<p>' + html;\n\t\t\t\t}\n\t\t\t\tif (!html.endsWith('>')) {\n\t\t\t\t\thtml = html + '</p>';\n\t\t\t\t}\n\n\t\t\t\treturn html;\n\t\t\t},\n\t\t\t// 获取封面图片\n\t\t\tasync fetchCoverImage(imageId) {\n\t\t\t\ttry {\n\t\t\t\t\tconst imageData = await fetchImageById(imageId);\n\t\t\t\t\tconsole.log('获取封面图片成功:', imageData);\n\n\t\t\t\t\tif (imageData) {\n\t\t\t\t\t\tthis.coverImage = imageData;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取封面图片失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取完整的图片URL\n\t\t\tgetFullImageUrl(url) {\n\t\t\t\treturn getFullImageUrlUtil(url);\n\t\t\t},\n\n\t\t\t// 返回上一页\n\t\t\tgoBack() {\n\t\t\t\tuni.navigateBack({\n\t\t\t\t\tdelta: 1\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t/* 文章封面图样式 */\n\t.article-cover {\n\t\tmargin: 20rpx 0;\n\t\twidth: 100%;\n\n\t\timage {\n\t\t\twidth: 100%;\n\t\t\tborder-radius: 8rpx;\n\t\t}\n\t}\n\n\t/* 使用全局样式文件中的.article-footer */\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/party/category-articles/brand-article.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "brandArticleApi.getBrandArticleDetail", "fetchImageById", "getFullImageUrlUtil"], "mappings": ";;;;AA6CC,MAAK,eAAgB,MAAW;AAGhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,SAAS;AAAA,QACR,IAAI;AAAA,QACJ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,cAAc;AAAA,MACd;AAAA,MACD,YAAY;AAAA,MACZ,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,WAAW,QAAQ,IAAI;AAC1B,WAAK,YAAY,QAAQ;AACzB,WAAK,wBAAuB;AAAA,WACtB;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChB,aAAK,OAAM;AAAA,MACX,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,0BAA0B;AAC/B,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMC,kBAAAA,sBAAsC,KAAK,SAAS;AACzED,sBAAA,MAAA,MAAA,OAAA,yDAAY,eAAe,MAAM;AAEjC,YAAI,UAAU,OAAO,WAAW,OAAO,MAAM;AAC5C,eAAK,UAAU,OAAO;AAEtB,cAAI,CAAC,KAAK,QAAQ,SAAS;AAC1B,iBAAK,QAAQ,UAAU,YAAY,KAAK,QAAQ,QAAQ;AAAA,UACzD;AAGA,cAAI,KAAK,QAAQ,cAAc;AAC9B,iBAAK,gBAAgB,KAAK,QAAQ,YAAY;AAAA,UAC/C;AAAA,eACM;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,0DAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AACf,aAAK,UAAU;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAED,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AACxB,YAAM,OAAO,IAAI,KAAK,UAAU;AAChC,aAAO,GAAG,KAAK,YAAW,CAAE,IAAI,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,KAAK,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,IACvH;AAAA;AAAA,IAED,cAAc,SAAS;AACtB,UAAI,CAAC;AAAS,eAAO;AAGrB,UAAI,OAAO,QAET,QAAQ,OAAO,MAAM,EAErB,QAAQ,aAAa,SAAS;AAGhC,UAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AAC1B,eAAO,QAAQ;AAAA,MAChB;AACA,UAAI,CAAC,KAAK,SAAS,GAAG,GAAG;AACxB,eAAO,OAAO;AAAA,MACf;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,MAAM,gBAAgB,SAAS;AAC9B,UAAI;AACH,cAAM,YAAY,MAAME,2BAAe,OAAO;AAC9CF,sBAAY,MAAA,MAAA,OAAA,0DAAA,aAAa,SAAS;AAElC,YAAI,WAAW;AACd,eAAK,aAAa;AAAA,QACnB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,0DAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAGD,gBAAgB,KAAK;AACpB,aAAOG,YAAAA,gBAAoB,GAAG;AAAA,IAC9B;AAAA;AAAA,IAGD,SAAS;AACRH,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7KD,GAAG,WAAW,eAAe;"}