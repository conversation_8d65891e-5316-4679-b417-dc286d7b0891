<template>
	<view class="u-page">
		<view class="u-page__top-box">
			<text class="u-demo-block__title">演示效果</text>
		</view>
		<up-empty
			:mode="mode"
			:icon="imgList[mode]"
		>
			<up-button
				size="small"
				type="primary"
				:style="{marginTop:10+'px'}"
				v-if="mode=='car'"
				text="查看更多商品"
			>
			</up-button>
		</up-empty>
		<div class="empty-select">
			<up-cell
				:titleStyle="{fontWeight: 500}"
				@click="openImg(item.imgName)"
				:title="item.title"
				v-for="(item, index) in list"
				:key="index"
				isLink
			>
				<template #icon>
					<image
						class="u-cell-icon"
						:src="item.iconUrl"
						mode="widthFix"
					></image>
				</template>
			</up-cell>
		</div>
	</view>
</template>

<script>
	export default {
		data() {
			// 如果使用这些图片，请勿直接引入cdn.uviewui.com的资源，因为此资源路径随时会有变动
			// 变动后，您将会访问图片失败，如有需要，您可以将这些图片上传到自己的oss或者服务器再使用
			const baseUrl = 'http://cdn.uviewui.com/uview/empty/'
			return {
				mode: 'car',
				imgList: {
					car: baseUrl + 'car.png',
					address: baseUrl + 'address.png',
					comment: baseUrl + 'comment.png',
					coupon: baseUrl + 'coupon.png',
					data: baseUrl + 'data.png',
					history: baseUrl + 'history.png',
					list: baseUrl + 'list.png',
					message: baseUrl + 'message.png',
					news: baseUrl + 'news.png',
					order: baseUrl + 'order.png',
					page: baseUrl + 'page.png',
					permission: baseUrl + 'permission.png',
					search: baseUrl + 'search.png',
					wifi: baseUrl + 'wifi.png',
				},
				list: [{
						imgName: 'car',
						title: '购物车为空(同时传入slot)',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/car.png'
					},
					{
						imgName: 'data',
						title: '数据为空',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/data.png'
					}, {
						imgName: 'comment',
						title: '评论为空',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/comment.png'
					}, {
						imgName: 'coupon',
						title: '没有优惠券',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/coupon.png'
					}, {
						imgName: 'history',
						title: '无历史记录',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/history.png'
					}, {
						imgName: 'list',
						title: '列表为空',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/list.png'
					}, {
						imgName: 'message',
						title: '消息列表为空',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/message.png'
					}, {
						imgName: 'news',
						title: '无新闻列表',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/news.png'
					}, {
						imgName: 'order',
						title: '订单为空',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/order.png'
					}, {
						imgName: 'page',
						title: '页面不存在',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/page.png'
					}, {
						imgName: 'permission',
						title: '无权限',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/permission.png'
					}, {
						imgName: 'search',
						title: '没有搜索结果',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/search.png'
					}, {
						imgName: 'wifi',
						title: '没有WiFi',
						iconUrl: 'https://cdn.uviewui.com/uview/demo/empty/wifi.png'
					},
				]

			}
		},
		methods: {
			//点击改变图片
			openImg(imgName) {
				// this.mode = this.imgList[imgName]
				this.mode = imgName;
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		padding: 40rpx 0px;

		&__top-box {
			padding-left: 40rpx;
		}
	}

	.empty-select {
		margin-top: 10px;
	}
</style>
