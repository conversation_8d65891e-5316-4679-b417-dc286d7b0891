{"version": 3, "file": "noticeBar.js", "sources": ["uview-plus_3.4.28/components/u-notice-bar/noticeBar.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:17:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/noticeBar.js\n */\nexport default {\n    // noticeBar\n    noticeBar: {\n        text: [],\n        direction: 'row',\n        step: false,\n        icon: 'volume',\n        mode: '',\n        color: '#f9ae3d',\n        bgColor: '#fdf6ec',\n        speed: 80,\n        fontSize: 14,\n        duration: 2000,\n        disableTouch: true,\n        url: '',\n        linkType: 'navigateTo',\n\t\tjustifyContent: 'flex-start'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM,CAAE;AAAA,IACR,WAAW;AAAA,IACX,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,KAAK;AAAA,IACL,UAAU;AAAA,IAChB,gBAAgB;AAAA,EACb;AACL;;"}