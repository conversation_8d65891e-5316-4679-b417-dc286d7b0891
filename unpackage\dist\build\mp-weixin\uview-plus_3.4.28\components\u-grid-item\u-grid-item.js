"use strict";const t=require("../../../common/vendor.js"),e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),s=require("../../libs/mixin/mixin.js"),a=require("../../libs/function/index.js"),r={name:"u-grid-item",mixins:[i.mpMixin,s.mixin,e.props],data:()=>({parentData:{col:0,border:!0},classes:[]}),mounted(){this.init()},emits:["click"],options:{virtualHost:!0},computed:{itemStyle(){const t={background:this.bgColor,width:"100%"};return a.deepMerge(t,a.addStyle(this.customStyle))}},methods:{init(){t.index.$on("$uGridItem",(()=>{this.gridItemClasses()})),this.updateParentData(),t.index.$emit("$uGridItem"),this.gridItemClasses()},updateParentData(){this.getParentData("u-grid")},clickHandler(){var t;let e=this.name;const i=null==(t=this.parent)?void 0:t.children;i&&null===this.name&&(e=i.findIndex((t=>t===this))),this.parent&&this.parent.childClick(e),this.$emit("click",e)},async getItemWidth(){let t=0;if(this.parent){t=await this.getParentWidth()/Number(this.parentData.col)+"px"}this.width=t},getParentWidth(){},gridItemClasses(){if(this.parentData.border){let t=[];this.parent.children.map(((e,i)=>{if(this===e){const e=this.parent.children.length;(i+1)%this.parentData.col!=0&&i+1!==e&&t.push("u-border-right");i<e-(e%this.parentData.col==0?this.parentData.col:e%this.parentData.col)&&t.push("u-border-bottom")}})),this.classes=t}}},beforeUnmount(){t.index.$off("$uGridItem")}};const n=t._export_sfc(r,[["render",function(e,i,s,a,r,n){return t.e({a:r.parentData.col>0},r.parentData.col>0?{b:t.o(((...t)=>n.clickHandler&&n.clickHandler(...t))),c:t.n(r.classes),d:t.s(n.itemStyle)}:{})}],["__scopeId","data-v-25911724"]]);wx.createComponent(n);
