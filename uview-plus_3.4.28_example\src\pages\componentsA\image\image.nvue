<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基本案例</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					 <up-image
					     :showLoading="true"
					     :src="src"
					     width="80px"
					     height="80px"
						 @click="click"
					 ></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义形状</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    shape="circle"
					    :src="src"
					    width="80px"
					    height="80px"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义圆角</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    radius="12"
					    :src="src"
					    width="80px"
					    height="80px"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">宽度100%</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    radius="12"
					    :src="src"
					    width="100%"
					    height="80px"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">图片模式(widthFix)</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src"
					    width="80px"
					    height="80px"
						mode="widthFix"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">图片模式(heightFix)</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src"
					    width="80px"
					    height="80px"
						mode="heightFix"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">图片模式(scaleToFill)</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src"
					    width="80px"
					    height="80px"
						mode="scaleToFill"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">图片模式(aspectFit)</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src"
					    width="80px"
					    height="80px"
						mode="aspectFit"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">图片模式(aspectFill)</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src"
					    width="80px"
					    height="80px"
						mode="aspectFill"
					></up-image>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义图片加载插槽</text>
			<view class="u-demo-block__content">
				<view class="u-page__image-item">
					<up-image
					    :src="src1"
					    width="80px"
					    height="80px"
						mode="widthFix"
					>
						<template v-slot:loading>
							<up-loading-icon color="red"></up-loading-icon>
						</template>
					</up-image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				src: 'https://cdn.uviewui.com/uview/album/1.jpg',
				src1:''
			}
		},
		onLoad() {
			setTimeout(()=>{
				this.src1 = this.src
			},3000)
		},
		methods: {
			click() {
				console.log('click');
			}
		}
	}
</script>

<style lang="scss">
	 
</style>
