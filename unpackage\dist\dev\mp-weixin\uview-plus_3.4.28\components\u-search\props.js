"use strict";
const uviewPlus_3_4_28_libs_vue = require("../../libs/vue.js");
const uviewPlus_3_4_28_libs_config_props = require("../../libs/config/props.js");
const props = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    // 搜索框形状，round-圆形，square-方形
    shape: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.shape
    },
    // 搜索框背景色，默认值#f2f2f2
    bgColor: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.bgColor
    },
    // 占位提示文字
    placeholder: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.placeholder
    },
    // 是否启用清除控件
    clearabled: {
      type: Boolean,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.clearabled
    },
    // 是否自动聚焦
    focus: {
      type: Boolean,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.focus
    },
    // 是否在搜索框右侧显示取消按钮
    showAction: {
      type: Boolean,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.showAction
    },
    // 右边控件的样式
    actionStyle: {
      type: Object,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.actionStyle
    },
    // 取消按钮文字
    actionText: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.actionText
    },
    // 输入框内容对齐方式，可选值为 left|center|right
    inputAlign: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.inputAlign
    },
    // input输入框的样式，可以定义文字颜色，大小等，对象形式
    inputStyle: {
      type: Object,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.inputStyle
    },
    // 是否启用输入框
    disabled: {
      type: Boolean,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.disabled
    },
    // 边框颜色
    borderColor: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.borderColor
    },
    // 搜索图标的颜色，默认同输入框字体颜色
    searchIconColor: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.searchIconColor
    },
    // 输入框字体颜色
    color: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.color
    },
    // placeholder的颜色
    placeholderColor: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.placeholderColor
    },
    // 左边输入框的图标，可以为uView图标名称或图片路径
    searchIcon: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.searchIcon
    },
    searchIconSize: {
      type: [Number, String],
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.searchIconSize
    },
    // 组件与其他上下左右元素之间的距离，带单位的字符串形式，如"30px"、"30px 20px"等写法
    margin: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.margin
    },
    // 开启showAction时，是否在input获取焦点时才显示
    animation: {
      type: Boolean,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.animation
    },
    // 输入框的初始化内容
    modelValue: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.value
    },
    value: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.value
    },
    // 输入框最大能输入的长度，-1为不限制长度(来自uniapp文档)
    maxlength: {
      type: [String, Number],
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.maxlength
    },
    // 搜索框高度，单位px
    height: {
      type: [String, Number],
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.height
    },
    // 搜索框左侧文本
    label: {
      type: [String, Number, null],
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.label
    },
    // 键盘弹起时，是否自动上推页面	
    adjustPosition: {
      type: Boolean,
      default: () => true
    },
    // 键盘收起时，是否自动失去焦点	
    autoBlur: {
      type: Boolean,
      default: () => false
    },
    iconPosition: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.search.iconPosition
    }
  }
});
exports.props = props;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-search/props.js.map
