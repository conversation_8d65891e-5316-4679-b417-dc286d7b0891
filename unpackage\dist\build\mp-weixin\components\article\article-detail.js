"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),r={name:"ArticleDetail",props:{article:{type:Object,default:()=>({id:null,title:"",content:"",content_markdown:"",created_at:"",createdAt:"",category:"",author:"",coverImageId:null})},articleType:{type:String,default:"normal",validator:e=>["normal","menu"].includes(e)},showFooter:{type:Boolean,default:!0}},data:()=>({coverImage:null,loading:!0}),computed:{articleContent(){return"normal"===this.articleType?this.formatContent(this.article.content_markdown):this.article.content}},watch:{article:{handler(e){e&&e.coverImageId&&this.fetchCoverImage(e.coverImageId)},immediate:!0,deep:!0}},methods:{formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},formatContent(e){if(!e)return"";let t=e.replace(/\n\n/g,'</p><p class="article-paragraph">').replace(/\n/g,"<br>").replace(/#{6}\s(.*?)\s*$/gm,"<h6>$1</h6>").replace(/#{5}\s(.*?)\s*$/gm,"<h5>$1</h5>").replace(/#{4}\s(.*?)\s*$/gm,"<h4>$1</h4>").replace(/#{3}\s(.*?)\s*$/gm,"<h3>$1</h3>").replace(/#{2}\s(.*?)\s*$/gm,"<h2>$1</h2>").replace(/#{1}\s(.*?)\s*$/gm,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/>\s(.*?)\s*$/gm,"<blockquote>$1</blockquote>");return t.startsWith("<")||(t='<p class="article-paragraph">'+t),t.endsWith(">")||(t+="</p>"),t},async fetchCoverImage(e){try{const r=await t.fetchImageById(e);console.log("获取封面图片成功:",r),r&&(this.coverImage=r)}catch(r){console.error("获取封面图片失败:",r)}},getFullImageUrl:e=>t.getFullImageUrl(e),previewCoverImage(){if(this.coverImage&&this.coverImage.url){const t=this.getFullImageUrl(this.coverImage.url);console.log("预览封面图片:",t),e.index.previewImage({urls:[t],current:t,success:()=>{console.log("图片预览成功")},fail:t=>{console.error("图片预览失败:",t),e.index.showToast({title:"图片预览失败",icon:"none"})}})}},goBack(){e.index.navigateBack()}}};if(!Array){e.resolveComponent("u-button")()}Math;const a=e._export_sfc(r,[["render",function(t,r,a,c,o,l){return e.e({a:e.t(a.article.title||"加载中..."),b:a.article.created_at||a.article.createdAt},a.article.created_at||a.article.createdAt?{c:e.t(l.formatDate(a.article.created_at||a.article.createdAt))}:{},{d:a.article.category},a.article.category?{e:e.t(a.article.category)}:{},{f:a.article.author},a.article.author?{g:e.t(a.article.author)}:{},{h:o.coverImage},o.coverImage?{i:l.getFullImageUrl(o.coverImage.url),j:o.coverImage.altText||a.article.title,k:e.o(((...e)=>l.previewCoverImage&&l.previewCoverImage(...e)))}:{},{l:l.articleContent||"加载中...",m:e.o(l.goBack),n:e.p({type:"primary",text:"返回列表",customStyle:{backgroundColor:"#D9001B"}})})}]]);wx.createComponent(a);
