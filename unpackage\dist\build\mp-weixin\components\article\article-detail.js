"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),a={name:"ArticleDetail",props:{article:{type:Object,default:()=>({id:null,title:"",content:"",content_markdown:"",created_at:"",createdAt:"",category:"",author:"",coverImageId:null})},articleType:{type:String,default:"normal",validator:e=>["normal","menu"].includes(e)},showFooter:{type:Boolean,default:!0}},data:()=>({coverImage:null,loading:!0}),computed:{articleContent(){return"normal"===this.articleType?this.formatContent(this.article.content_markdown):this.article.content}},watch:{article:{handler(e){e&&e.coverImageId&&this.fetchCoverImage(e.coverImageId)},immediate:!0,deep:!0}},methods:{formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},formatContent(e){if(!e)return"";let t=e.replace(/\n\n/g,'</p><p class="article-paragraph">').replace(/\n/g,"<br>").replace(/#{6}\s(.*?)\s*$/gm,"<h6>$1</h6>").replace(/#{5}\s(.*?)\s*$/gm,"<h5>$1</h5>").replace(/#{4}\s(.*?)\s*$/gm,"<h4>$1</h4>").replace(/#{3}\s(.*?)\s*$/gm,"<h3>$1</h3>").replace(/#{2}\s(.*?)\s*$/gm,"<h2>$1</h2>").replace(/#{1}\s(.*?)\s*$/gm,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/>\s(.*?)\s*$/gm,"<blockquote>$1</blockquote>");return t.startsWith("<")||(t='<p class="article-paragraph">'+t),t.endsWith(">")||(t+="</p>"),t},async fetchCoverImage(e){try{const a=await t.fetchImageById(e);console.log("获取封面图片成功:",a),a&&(this.coverImage=a)}catch(a){console.error("获取封面图片失败:",a)}},getFullImageUrl:e=>t.getFullImageUrl(e),previewCoverImage(){if(this.coverImage&&this.coverImage.url){const t=this.getFullImageUrl(this.coverImage.url);console.log("预览封面图片:",t),e.index.previewImage({urls:[t],current:t,success:()=>{console.log("图片预览成功")},fail:t=>{console.error("图片预览失败:",t),e.index.showToast({title:"图片预览失败",icon:"none"})}})}},navigateToMiniProgram(){this.coverImage&&this.coverImage.miniAppId&&this.coverImage.miniAppPagePath?(console.log("准备跳转到小程序:",{appId:this.coverImage.miniAppId,path:this.coverImage.miniAppPagePath,title:this.coverImage.title}),e.index.navigateToMiniProgram({appId:this.coverImage.miniAppId,path:this.coverImage.miniAppPagePath,extraData:{source:"article_cover_image",articleId:this.article.id,imageId:this.coverImage.id},envVersion:"release",success:e=>{console.log("跳转小程序成功:",e)},fail:e=>{console.error("跳转小程序失败:",e),this.showMiniProgramFallback()}})):e.index.showToast({title:"小程序信息不完整",icon:"none"})},showMiniProgramFallback(){e.index.showModal({title:"跳转失败",content:"无法直接跳转到小程序，请尝试其他方式",showCancel:!0,cancelText:"取消",confirmText:"复制信息",success:t=>{if(t.confirm){const t=`小程序ID: ${this.coverImage.miniAppId}\n页面路径: ${this.coverImage.miniAppPagePath}`;e.index.setClipboardData({data:t,success:()=>{e.index.showToast({title:"已复制到剪贴板",icon:"success"})}})}}})},goBack(){e.index.navigateBack()}}};if(!Array){e.resolveComponent("u-button")()}Math;const r=e._export_sfc(a,[["render",function(t,a,r,o,i,c){return e.e({a:e.t(r.article.title||"加载中..."),b:r.article.created_at||r.article.createdAt},r.article.created_at||r.article.createdAt?{c:e.t(c.formatDate(r.article.created_at||r.article.createdAt))}:{},{d:r.article.category},r.article.category?{e:e.t(r.article.category)}:{},{f:r.article.author},r.article.author?{g:e.t(r.article.author)}:{},{h:i.coverImage},i.coverImage?e.e({i:c.getFullImageUrl(i.coverImage.url),j:i.coverImage.altText||r.article.title,k:e.o(((...e)=>c.previewCoverImage&&c.previewCoverImage(...e))),l:i.coverImage.miniAppId&&i.coverImage.miniAppPagePath},i.coverImage.miniAppId&&i.coverImage.miniAppPagePath?{m:e.t(i.coverImage.title||"相关小程序"),n:e.o(((...e)=>c.navigateToMiniProgram&&c.navigateToMiniProgram(...e)))}:{}):{},{o:c.articleContent||"加载中...",p:e.o(c.goBack),q:e.p({type:"primary",text:"返回列表",customStyle:{backgroundColor:"#D9001B"}})})}]]);wx.createComponent(r);
