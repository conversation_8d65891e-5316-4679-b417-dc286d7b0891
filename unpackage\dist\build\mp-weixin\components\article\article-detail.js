"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),a={name:"ArticleDetail",props:{article:{type:Object,default:()=>({id:null,title:"",content:"",content_markdown:"",created_at:"",createdAt:"",category:"",author:"",coverImageId:null})},articleType:{type:String,default:"normal",validator:e=>["normal","menu"].includes(e)},showFooter:{type:Boolean,default:!0}},data:()=>({coverImage:null,loading:!0}),computed:{articleContent(){return"normal"===this.articleType?this.formatContent(this.article.content_markdown):this.article.content}},watch:{article:{handler(e){e&&e.coverImageId&&this.fetchCoverImage(e.coverImageId)},immediate:!0,deep:!0}},methods:{formatDate(e){if(!e)return"";const t=new Date(e);return`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}-${String(t.getDate()).padStart(2,"0")}`},formatContent(e){if(!e)return"";let t=e.replace(/\n\n/g,'</p><p class="article-paragraph">').replace(/\n/g,"<br>").replace(/#{6}\s(.*?)\s*$/gm,"<h6>$1</h6>").replace(/#{5}\s(.*?)\s*$/gm,"<h5>$1</h5>").replace(/#{4}\s(.*?)\s*$/gm,"<h4>$1</h4>").replace(/#{3}\s(.*?)\s*$/gm,"<h3>$1</h3>").replace(/#{2}\s(.*?)\s*$/gm,"<h2>$1</h2>").replace(/#{1}\s(.*?)\s*$/gm,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/>\s(.*?)\s*$/gm,"<blockquote>$1</blockquote>");return t.startsWith("<")||(t='<p class="article-paragraph">'+t),t.endsWith(">")||(t+="</p>"),t},async fetchCoverImage(e){try{const a=await t.fetchImageById(e);console.log("获取封面图片成功:",a),a&&(this.coverImage=a)}catch(a){console.error("获取封面图片失败:",a)}},getFullImageUrl:e=>t.getFullImageUrl(e),goBack(){e.index.navigateBack()}}};if(!Array){e.resolveComponent("u-button")()}Math;const r=e._export_sfc(a,[["render",function(t,a,r,c,l,o){return e.e({a:e.t(r.article.title||"加载中..."),b:r.article.created_at||r.article.createdAt},r.article.created_at||r.article.createdAt?{c:e.t(o.formatDate(r.article.created_at||r.article.createdAt))}:{},{d:r.article.category},r.article.category?{e:e.t(r.article.category)}:{},{f:r.article.author},r.article.author?{g:e.t(r.article.author)}:{},{h:l.coverImage},l.coverImage?{i:o.getFullImageUrl(l.coverImage.url),j:l.coverImage.altText||r.article.title}:{},{k:o.articleContent||"加载中...",l:e.o(o.goBack),m:e.p({type:"primary",text:"返回列表",customStyle:{backgroundColor:"#D9001B"}})})}]]);wx.createComponent(r);
