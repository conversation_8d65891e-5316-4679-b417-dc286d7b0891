"use strict";
/**
* @vue/shared v3.4.21
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**/
function e(e,t){const n=new Set(e.split(","));return t?e=>n.has(e.toLowerCase()):e=>n.has(e)}const t={},n=[],o=()=>{},r=()=>!1,s=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),i=e=>e.startsWith("onUpdate:"),c=Object.assign,u=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},a=Object.prototype.hasOwnProperty,l=(e,t)=>a.call(e,t),f=Array.isArray,p=e=>"[object Map]"===x(e),d=e=>"[object Set]"===x(e),h=e=>"function"==typeof e,g=e=>"string"==typeof e,m=e=>"symbol"==typeof e,v=e=>null!==e&&"object"==typeof e,_=e=>(v(e)||h(e))&&h(e.then)&&h(e.catch),y=Object.prototype.toString,x=e=>y.call(e),b=e=>"[object Object]"===x(e),w=e=>g(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,$=e(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),S=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},k=/-(\w)/g,O=S((e=>e.replace(k,((e,t)=>t?t.toUpperCase():"")))),P=/\B([A-Z])/g,C=S((e=>e.replace(P,"-$1").toLowerCase())),E=S((e=>e.charAt(0).toUpperCase()+e.slice(1))),I=S((e=>e?`on${E(e)}`:"")),A=(e,t)=>!Object.is(e,t),j=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},R=e=>{const t=parseFloat(e);return isNaN(t)?e:t};function L(e){if(f(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=g(o)?D(o):L(o);if(r)for(const e in r)t[e]=r[e]}return t}if(g(e)||v(e))return e}const T=/;(?![^(]*\))/g,M=/:([^]+)/,V=/\/\*[^]*?\*\//g;function D(e){const t={};return e.replace(V,"").split(T).forEach((e=>{if(e){const n=e.split(M);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function H(e){let t="";if(g(e))t=e;else if(f(e))for(let n=0;n<e.length;n++){const o=H(e[n]);o&&(t+=o+" ")}else if(v(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const N=(e,t)=>t&&t.__v_isRef?N(e,t.value):p(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[B(t,o)+" =>"]=n,e)),{})}:d(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>B(e)))}:m(t)?B(t):!v(t)||f(t)||b(t)?t:String(t),B=(e,t="")=>{var n;return m(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};function U(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function W(e,t){if(!g(t))return;const n=(t=t.replace(/\[(\d+)\]/g,".$1")).split(".");let o=n[0];return e||(e={}),1===n.length?e[o]:W(e[o],n.slice(1).join("."))}function z(e){let t={};return b(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}const F=/:/g;const K=encodeURIComponent;function q(e,t=K){const n=e?Object.keys(e).map((n=>{let o=e[n];return void 0===typeof o||null===o?o="":b(o)&&(o=JSON.stringify(o)),t(n)+"="+t(o)})).filter((e=>e.length>0)).join("&"):null;return n?`?${n}`:""}const G=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onShareChat","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const J=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onExit","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onShareChat","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],Z=(()=>({onPageScroll:1,onShareAppMessage:2,onShareTimeline:4}))();function Q(e,t,n=!0){return!(n&&!h(t))&&(J.indexOf(e)>-1||0===e.indexOf("on"))}let X;const Y=[];const ee=U(((e,t)=>t(e))),te=function(){};te.prototype={_id:1,on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n,_id:this._id}),this._id++},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t){for(var s=o.length-1;s>=0;s--)if(o[s].fn===t||o[s].fn._===t||o[s]._id===t){o.splice(s,1);break}r=o}return r.length?n[e]=r:delete n[e],this}};var ne=te;function oe(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}function re(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let se=1;const ie={};function ce(e,t,n){if("number"==typeof e){const o=ie[e];if(o)return o.keepAlive||delete ie[e],o.callback(t,n)}return t}const ue="success",ae="fail",le="complete";function fe(e,t={},{beforeAll:n,beforeSuccess:o}={}){b(t)||(t={});const{success:r,fail:s,complete:i}=function(e){const t={};for(const n in e){const o=e[n];h(o)&&(t[n]=re(o),delete e[n])}return t}(t),c=h(r),u=h(s),a=h(i),l=se++;return function(e,t,n,o=!1){ie[e]={name:t,keepAlive:o,callback:n}}(l,e,(l=>{(l=l||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(l.errMsg,e),h(n)&&n(l),l.errMsg===e+":ok"?(h(o)&&o(l,t),c&&r(l)):u&&s(l),a&&i(l)})),l}const pe="success",de="fail",he="complete",ge={},me={};function ve(e,t){return function(n){return e(n,t)||n}}function _e(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const s=e[r];if(o)o=Promise.resolve(ve(s,n));else{const e=s(t,n);if(_(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function ye(e,t={}){return[pe,de,he].forEach((n=>{const o=e[n];if(!f(o))return;const r=t[n];t[n]=function(e){_e(o,e,t).then((e=>h(r)&&r(e)||e))}})),t}function xe(e,t){const n=[];f(ge.returnValue)&&n.push(...ge.returnValue);const o=me[e];return o&&f(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function be(e){const t=Object.create(null);Object.keys(ge).forEach((e=>{"returnValue"!==e&&(t[e]=ge[e].slice())}));const n=me[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function we(e,t,n,o){const r=be(e);if(r&&Object.keys(r).length){if(f(r.invoke)){return _e(r.invoke,n).then((n=>t(ye(be(e),n),...o)))}return t(ye(r,n),...o)}return t(n,...o)}function $e(e,t){return(n={},...o)=>function(e){return!(!b(e)||![ue,ae,le].find((t=>h(e[t]))))}(n)?xe(e,we(e,t,n,o)):xe(e,new Promise(((r,s)=>{we(e,t,c(n,{success:r,fail:s}),o)})))}function Se(e,t,n,o={}){const r=t+":fail";let s="";return s=n?0===n.indexOf(r)?n:r+" "+n:r,delete o.errCode,ce(e,c({errMsg:s},o))}function ke(e,t,n,o){const r=function(e,t){e[0]}(t);if(r)return r}function Oe(e,t,n,o){return n=>{const r=fe(e,n,o),s=ke(0,[n]);return s?Se(r,e,s):t(n,{resolve:t=>function(e,t,n){return ce(e,c(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>Se(r,e,function(e){return!e||g(e)?e:e.stack?("undefined"!=typeof globalThis&&globalThis.harmonyChannel||console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function Pe(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=ke(0,e);if(n)throw new Error(n);return t.apply(null,e)}}(0,t)}let Ce=!1,Ee=0,Ie=0;const Ae=Pe(0,((e,t)=>{if(0===Ee&&function(){var e,t;let n,o,r;{const s=(null===(e=wx.getWindowInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync(),i=(null===(t=wx.getDeviceInfo)||void 0===t?void 0:t.call(wx))||wx.getSystemInfoSync();n=s.windowWidth,o=s.pixelRatio,r=i.platform}Ee=n,Ie=o,Ce="ios"===r}(),0===(e=Number(e)))return 0;let n=e/750*(t||Ee);return n<0&&(n=-n),n=Math.floor(n+1e-4),0===n&&(n=1!==Ie&&Ce?.5:1),e<0?-n:n}));function je(e,t){Object.keys(t).forEach((n=>{h(t[n])&&(e[n]=function(e,t){const n=t?e?e.concat(t):f(t)?t:[t]:e;return n?function(e){const t=[];for(let n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}(n):n}(e[n],t[n]))}))}function Re(e,t){e&&t&&Object.keys(t).forEach((n=>{const o=e[n],r=t[n];f(o)&&h(r)&&u(o,r)}))}const Le=Pe(0,((e,t)=>{g(e)&&b(t)?je(me[e]||(me[e]={}),t):b(e)&&je(ge,e)})),Te=Pe(0,((e,t)=>{g(e)?b(t)?Re(me[e],t):delete me[e]:b(e)&&Re(ge,e)}));const Me=new class{constructor(){this.$emitter=new ne}on(e,t){return this.$emitter.on(e,t)}once(e,t){return this.$emitter.once(e,t)}off(e,t){e?this.$emitter.off(e,t):this.$emitter.e={}}emit(e,...t){this.$emitter.emit(e,...t)}},Ve=Pe(0,((e,t)=>(Me.on(e,t),()=>Me.off(e,t)))),De=Pe(0,((e,t)=>(Me.once(e,t),()=>Me.off(e,t)))),He=Pe(0,((e,t)=>{f(e)||(e=e?[e]:[]),e.forEach((e=>{Me.off(e,t)}))})),Ne=Pe(0,((e,...t)=>{Me.emit(e,...t)}));let Be,Ue,We;function ze(e){try{return JSON.parse(e)}catch(t){}return e}const Fe=[];function Ke(e,t){Fe.forEach((n=>{n(e,t)})),Fe.length=0}const qe=$e(Ge="getPushClientId",function(e,t,n,o){return Oe(e,t,0,o)}(Ge,((e,{resolve:t,reject:n})=>{Promise.resolve().then((()=>{void 0===We&&(We=!1,Be="",Ue="uniPush is not enabled"),Fe.push(((e,o)=>{e?t({cid:e}):n(o)})),void 0!==Be&&Ke(Be,Ue)}))}),0,Je));var Ge,Je;const Ze=[],Qe=/^\$|__f__|getLocale|setLocale|sendNativeEvent|restoreGlobal|requireGlobal|getCurrentSubNVue|getMenuButtonBoundingClientRect|^report|interceptors|Interceptor$|getSubNVueById|requireNativePlugin|upx2px|rpx2px|hideKeyboard|canIUse|^create|Sync$|Manager$|base64ToArrayBuffer|arrayBufferToBase64|getDeviceInfo|getAppBaseInfo|getWindowInfo|getSystemSetting|getAppAuthorizeSetting/,Xe=/^create|Manager$/,Ye=["createBLEConnection"],et=["request","downloadFile","uploadFile","connectSocket"],tt=["createBLEConnection"],nt=/^on|^off/;function ot(e){return Xe.test(e)&&-1===Ye.indexOf(e)}function rt(e){return Qe.test(e)&&-1===tt.indexOf(e)}function st(e){return-1!==et.indexOf(e)}function it(e){return!(ot(e)||rt(e)||function(e){return nt.test(e)&&"onPush"!==e}(e))}function ct(e,t){return it(e)&&h(t)?function(n={},...o){return h(n.success)||h(n.fail)||h(n.complete)?xe(e,we(e,t,n,o)):xe(e,new Promise(((r,s)=>{we(e,t,c({},n,{success:r,fail:s}),o)})))}:t}Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e&&e()).then((()=>n))),(n=>t.resolve(e&&e()).then((()=>{throw n}))))});const ut=["success","fail","cancel","complete"];const at=()=>{const e=h(getApp)&&getApp({allowDefault:!0});return e&&e.$vm?e.$vm.$locale:function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=oe(n&&n.language?n.language:"en")||"en"}return t}()},lt=[];"undefined"!=typeof global&&(global.getLocale=at);let ft;function pt(e=wx){return function(t,n){ft=ft||e.getStorageSync("__DC_STAT_UUID"),ft||(ft=Date.now()+""+Math.floor(1e7*Math.random()),wx.setStorage({key:"__DC_STAT_UUID",data:ft})),n.deviceId=ft}}function dt(e,t){if(e.safeArea){const n=e.safeArea;t.safeAreaInsets={top:n.top,left:n.left,right:e.windowWidth-n.right,bottom:e.screenHeight-n.bottom}}}function ht(e,t){let n="",o="";switch(n=e.split(" ")[0]||t,o=e.split(" ")[1]||"",n=n.toLocaleLowerCase(),n){case"harmony":case"ohos":case"openharmony":n="harmonyos";break;case"iphone os":n="ios";break;case"mac":case"darwin":n="macos";break;case"windows_nt":n="windows"}return{osName:n,osVersion:o}}function gt(e,t){let n=e.deviceType||"phone";{const e={ipad:"pad",windows:"pc",mac:"pc"},o=Object.keys(e),r=t.toLocaleLowerCase();for(let t=0;t<o.length;t++){const s=o[t];if(-1!==r.indexOf(s)){n=e[s];break}}}return n}function mt(e){let t=e;return t&&(t=t.toLocaleLowerCase()),t}function vt(e){return at?at():e}function _t(e){let t=e.hostName||"WeChat";return e.environment?t=e.environment:e.host&&e.host.env&&(t=e.host.env),t}const yt={returnValue:(e,t)=>{dt(e,t),pt()(e,t),function(e,t){const{brand:n="",model:o="",system:r="",language:s="",theme:i,version:u,platform:a,fontSizeSetting:l,SDKVersion:f,pixelRatio:p,deviceOrientation:d}=e,{osName:h,osVersion:g}=ht(r,a);let m=u,v=gt(e,o),_=mt(n),y=_t(e),x=d,b=p,w=f;const $=(s||"").replace(/_/g,"-"),S={appId:"__UNI__C402FDD",appName:"RedProtectio",appVersion:"1.0.0",appVersionCode:"100",appLanguage:vt($),uniCompileVersion:"4.64",uniCompilerVersion:"4.64",uniRuntimeVersion:"4.64",uniPlatform:"mp-weixin",deviceBrand:_,deviceModel:o,deviceType:v,devicePixelRatio:b,deviceOrientation:x,osName:h,osVersion:g,hostTheme:i,hostVersion:m,hostLanguage:$,hostName:y,hostSDKVersion:w,hostFontSizeSetting:l,windowTop:0,windowBottom:0,osLanguage:void 0,osTheme:void 0,ua:void 0,hostPackageName:void 0,browserName:void 0,browserVersion:void 0,isUniAppX:!1};c(t,S)}(e,t)}},xt=yt,bt={args(e,t){let n=parseInt(e.current);if(isNaN(n))return;const o=e.urls;if(!f(o))return;const r=o.length;return r?(n<0?n=0:n>=r&&(n=r-1),n>0?(t.current=o[n],t.urls=o.filter(((e,t)=>!(t<n)||e!==o[n]))):t.current=o[0],{indicator:!1,loop:!1}):void 0}},wt={args(e,t){t.alertText=e.title}},$t={returnValue:(e,t)=>{const{brand:n,model:o,system:r="",platform:s=""}=e;let i=gt(e,o),u=mt(n);pt()(e,t);const{osName:a,osVersion:l}=ht(r,s);t=z(c(t,{deviceType:i,deviceBrand:u,deviceModel:o,osName:a,osVersion:l}))}},St={returnValue:(e,t)=>{const{version:n,language:o,SDKVersion:r,theme:s}=e;let i=_t(e),u=(o||"").replace(/_/g,"-");const a={hostVersion:n,hostLanguage:u,hostName:i,hostSDKVersion:r,hostTheme:s,appId:"__UNI__C402FDD",appName:"RedProtectio",appVersion:"1.0.0",appVersionCode:"100",appLanguage:vt(u),isUniAppX:!1,uniPlatform:"mp-weixin",uniCompileVersion:"4.64",uniCompilerVersion:"4.64",uniRuntimeVersion:"4.64"};c(t,a)}},kt={returnValue:(e,t)=>{dt(e,t),t=z(c(t,{windowTop:0,windowBottom:0}))}},Ot={args(e){const t=getApp({allowDefault:!0})||{};t.$vm?ir("onError",e,t.$vm.$):(wx.$onErrorHandlers||(wx.$onErrorHandlers=[]),wx.$onErrorHandlers.push(e))}},Pt={args(e){const t=getApp({allowDefault:!0})||{};if(t.$vm){if(e.__weh){const n=t.$vm.$.onError;if(n){const t=n.indexOf(e.__weh);t>-1&&n.splice(t,1)}}}else{if(!wx.$onErrorHandlers)return;const t=wx.$onErrorHandlers.findIndex((t=>t===e));-1!==t&&wx.$onErrorHandlers.splice(t,1)}}},Ct={args(){if(wx.__uni_console__){if(wx.__uni_console_warned__)return;wx.__uni_console_warned__=!0,console.warn("开发模式下小程序日志回显会使用 socket 连接，为了避免冲突，建议使用 SocketTask 的方式去管理 WebSocket 或手动关闭日志回显功能。[详情](https://uniapp.dcloud.net.cn/tutorial/run/mp-log.html)")}}},Et=Ct,It={$on:Ve,$off:He,$once:De,$emit:Ne,upx2px:Ae,rpx2px:Ae,interceptors:{},addInterceptor:Le,removeInterceptor:Te,onCreateVueApp:function(e){if(X)return e(X);Y.push(e)},invokeCreateVueAppHook:function(e){X=e,Y.forEach((t=>t(e)))},getLocale:at,setLocale:e=>{const t=h(getApp)&&getApp();if(!t)return!1;return t.$vm.$locale!==e&&(t.$vm.$locale=e,lt.forEach((t=>t({locale:e}))),!0)},onLocaleChange:e=>{-1===lt.indexOf(e)&&lt.push(e)},getPushClientId:qe,onPushMessage:e=>{-1===Ze.indexOf(e)&&Ze.push(e)},offPushMessage:e=>{if(e){const t=Ze.indexOf(e);t>-1&&Ze.splice(t,1)}else Ze.length=0},invokePushCallback:function(e){if("enabled"===e.type)We=!0;else if("clientId"===e.type)Be=e.cid,Ue=e.errMsg,Ke(Be,e.errMsg);else if("pushMsg"===e.type){const t={type:"receive",data:ze(e.message)};for(let e=0;e<Ze.length;e++){if((0,Ze[e])(t),t.stopped)break}}else"click"===e.type&&Ze.forEach((t=>{t({type:"click",data:ze(e.message)})}))},__f__:function(e,t,...n){t&&n.push(t),console[e].apply(console,n)}};const At=["qy","env","error","version","lanDebug","cloud","serviceMarket","router","worklet","__webpack_require_UNI_MP_PLUGIN__"],jt=["lanDebug","router","worklet"],Rt=wx.getLaunchOptionsSync?wx.getLaunchOptionsSync():null;function Lt(e){return(!Rt||1154!==Rt.scene||!jt.includes(e))&&(At.indexOf(e)>-1||"function"==typeof wx[e])}function Tt(){const e={};for(const t in wx)Lt(t)&&(e[t]=wx[t]);return"undefined"!=typeof globalThis&&"undefined"==typeof requireMiniProgram&&(globalThis.wx=e),e}const Mt=["__route__","__wxExparserNodeId__","__wxWebviewId__"],Vt=(Dt={oauth:["weixin"],share:["weixin"],payment:["wxpay"],push:["weixin"]},function({service:e,success:t,fail:n,complete:o}){let r;Dt[e]?(r={errMsg:"getProvider:ok",service:e,provider:Dt[e]},h(t)&&t(r)):(r={errMsg:"getProvider:fail:服务["+e+"]不存在"},h(n)&&n(r)),h(o)&&o(r)});var Dt;const Ht=Tt();Ht.canIUse("getAppBaseInfo")||(Ht.getAppBaseInfo=Ht.getSystemInfoSync),Ht.canIUse("getWindowInfo")||(Ht.getWindowInfo=Ht.getSystemInfoSync),Ht.canIUse("getDeviceInfo")||(Ht.getDeviceInfo=Ht.getSystemInfoSync);let Nt=Ht.getAppBaseInfo&&Ht.getAppBaseInfo();Nt||(Nt=Ht.getSystemInfoSync());const Bt=Nt?Nt.host:null,Ut=Bt&&"SAAASDK"===Bt.env?Ht.miniapp.shareVideoMessage:Ht.shareVideoMessage;var Wt=Object.freeze({__proto__:null,createSelectorQuery:function(){const e=Ht.createSelectorQuery(),t=e.in;return e.in=function(e){return e.$scope?t.call(this,e.$scope):t.call(this,function(e){const t=Object.create(null);return Mt.forEach((n=>{t[n]=e[n]})),t}(e))},e},getProvider:Vt,shareVideoMessage:Ut});const zt={args(e,t){e.compressedHeight&&!t.compressHeight&&(t.compressHeight=e.compressedHeight),e.compressedWidth&&!t.compressWidth&&(t.compressWidth=e.compressedWidth)}};var Ft=function(e,t,n=wx){const o=function(e){function t(e,t,n){return function(r){return t(o(e,r,n))}}function n(e,n,o={},r={},s=!1){if(b(n)){const i=!0===s?n:{};h(o)&&(o=o(n,i)||{});for(const c in n)if(l(o,c)){let t=o[c];h(t)&&(t=t(n[c],n,i)),t?g(t)?i[t]=n[c]:b(t)&&(i[t.name?t.name:c]=t.value):console.warn(`微信小程序 ${e} 暂不支持 ${c}`)}else if(-1!==ut.indexOf(c)){const o=n[c];h(o)&&(i[c]=t(e,o,r))}else s||l(i,c)||(i[c]=n[c]);return i}return h(n)&&(h(o)&&o(n,{}),n=t(e,n,r)),n}function o(t,o,r,s=!1){return h(e.returnValue)&&(o=e.returnValue(t,o)),n(t,o,r,{},s||!1)}return function(t,r){const s=l(e,t);if(!s&&"function"!=typeof wx[t])return r;const i=s||h(e.returnValue)||ot(t)||st(t),c=s||h(r);if(!s&&!r)return function(){console.error(`微信小程序 暂不支持${t}`)};if(!i||!c)return r;const u=e[t];return function(e,r){let s=u||{};h(u)&&(s=u(e));const i=[e=n(t,e,s.args,s.returnValue)];void 0!==r&&i.push(r);const c=wx[s.name||t].apply(wx,i);return(ot(t)||st(t))&&c&&!c.__v_skip&&(c.__v_skip=!0),rt(t)?o(t,c,s.returnValue,ot(t)):c}}}(t);return new Proxy({},{get:(t,r)=>l(t,r)?t[r]:l(e,r)?ct(r,e[r]):l(It,r)?ct(r,It[r]):ct(r,o(r,n[r]))})}(Wt,Object.freeze({__proto__:null,compressImage:zt,getAppAuthorizeSetting:{returnValue:function(e,t){const{locationReducedAccuracy:n}=e;t.locationAccuracy="unsupported",!0===n?t.locationAccuracy="reduced":!1===n&&(t.locationAccuracy="full")}},getAppBaseInfo:St,getDeviceInfo:$t,getSystemInfo:yt,getSystemInfoSync:xt,getWindowInfo:kt,offError:Pt,onError:Ot,onSocketMessage:Et,onSocketOpen:Ct,previewImage:bt,redirectTo:{},showActionSheet:wt}),Tt());let Kt,qt;class Gt{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Kt,!e&&Kt&&(this.index=(Kt.scopes||(Kt.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Kt;try{return Kt=this,e()}finally{Kt=t}}}on(){Kt=this}off(){Kt=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}class Jt{constructor(e,t,n,o){this.fn=e,this.trigger=t,this.scheduler=n,this.active=!0,this.deps=[],this._dirtyLevel=4,this._trackId=0,this._runnings=0,this._shouldSchedule=!1,this._depsLength=0,function(e,t=Kt){t&&t.active&&t.effects.push(e)}(this,o)}get dirty(){if(2===this._dirtyLevel||3===this._dirtyLevel){this._dirtyLevel=1,nn();for(let e=0;e<this._depsLength;e++){const t=this.deps[e];if(t.computed&&(t.computed.value,this._dirtyLevel>=4))break}1===this._dirtyLevel&&(this._dirtyLevel=0),on()}return this._dirtyLevel>=4}set dirty(e){this._dirtyLevel=e?4:0}run(){if(this._dirtyLevel=0,!this.active)return this.fn();let e=Yt,t=qt;try{return Yt=!0,qt=this,this._runnings++,Zt(this),this.fn()}finally{Qt(this),this._runnings--,qt=t,Yt=e}}stop(){var e;this.active&&(Zt(this),Qt(this),null==(e=this.onStop)||e.call(this),this.active=!1)}}function Zt(e){e._trackId++,e._depsLength=0}function Qt(e){if(e.deps.length>e._depsLength){for(let t=e._depsLength;t<e.deps.length;t++)Xt(e.deps[t],e);e.deps.length=e._depsLength}}function Xt(e,t){const n=e.get(t);void 0!==n&&t._trackId!==n&&(e.delete(t),0===e.size&&e.cleanup())}let Yt=!0,en=0;const tn=[];function nn(){tn.push(Yt),Yt=!1}function on(){const e=tn.pop();Yt=void 0===e||e}function rn(){en++}function sn(){for(en--;!en&&un.length;)un.shift()()}function cn(e,t,n){if(t.get(e)!==e._trackId){t.set(e,e._trackId);const n=e.deps[e._depsLength];n!==t?(n&&Xt(n,e),e.deps[e._depsLength++]=t):e._depsLength++}}const un=[];function an(e,t,n){rn();for(const o of e.keys()){let n;o._dirtyLevel<t&&(null!=n?n:n=e.get(o)===o._trackId)&&(o._shouldSchedule||(o._shouldSchedule=0===o._dirtyLevel),o._dirtyLevel=t),o._shouldSchedule&&(null!=n?n:n=e.get(o)===o._trackId)&&(o.trigger(),o._runnings&&!o.allowRecurse||2===o._dirtyLevel||(o._shouldSchedule=!1,o.scheduler&&un.push(o.scheduler)))}sn()}const ln=(e,t)=>{const n=new Map;return n.cleanup=e,n.computed=t,n},fn=new WeakMap,pn=Symbol(""),dn=Symbol("");function hn(e,t,n){if(Yt&&qt){let t=fn.get(e);t||fn.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=ln((()=>t.delete(n)))),cn(qt,o)}}function gn(e,t,n,o,r,s){const i=fn.get(e);if(!i)return;let c=[];if("clear"===t)c=[...i.values()];else if("length"===n&&f(e)){const e=Number(o);i.forEach(((t,n)=>{("length"===n||!m(n)&&n>=e)&&c.push(t)}))}else switch(void 0!==n&&c.push(i.get(n)),t){case"add":f(e)?w(n)&&c.push(i.get("length")):(c.push(i.get(pn)),p(e)&&c.push(i.get(dn)));break;case"delete":f(e)||(c.push(i.get(pn)),p(e)&&c.push(i.get(dn)));break;case"set":p(e)&&c.push(i.get(pn))}rn();for(const u of c)u&&an(u,4);sn()}const mn=e("__proto__,__v_isRef,__isVue"),vn=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(m)),_n=yn();function yn(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=so(this);for(let t=0,r=this.length;t<r;t++)hn(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(so)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){nn(),rn();const n=so(this)[t].apply(this,e);return sn(),on(),n}})),e}function xn(e){const t=so(this);return hn(t,0,e),t.hasOwnProperty(e)}class bn{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){const o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?Qn:Zn:r?Jn:Gn).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;const s=f(e);if(!o){if(s&&l(_n,t))return Reflect.get(_n,t,n);if("hasOwnProperty"===t)return xn}const i=Reflect.get(e,t,n);return(m(t)?vn.has(t):mn(t))?i:(o||hn(e,0,t),r?i:po(i)?s&&w(t)?i:i.value:v(i)?o?eo(i):Yn(i):i)}}class wn extends bn{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){const t=oo(r);if(ro(n)||oo(n)||(r=so(r),n=so(n)),!f(e)&&po(r)&&!po(n))return!t&&(r.value=n,!0)}const s=f(e)&&w(t)?Number(t)<e.length:l(e,t),i=Reflect.set(e,t,n,o);return e===so(o)&&(s?A(n,r)&&gn(e,"set",t,n):gn(e,"add",t,n)),i}deleteProperty(e,t){const n=l(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&gn(e,"delete",t,void 0),o}has(e,t){const n=Reflect.has(e,t);return m(t)&&vn.has(t)||hn(e,0,t),n}ownKeys(e){return hn(e,0,f(e)?"length":pn),Reflect.ownKeys(e)}}class $n extends bn{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}const Sn=new wn,kn=new $n,On=new wn(!0),Pn=e=>e,Cn=e=>Reflect.getPrototypeOf(e);function En(e,t,n=!1,o=!1){const r=so(e=e.__v_raw),s=so(t);n||(A(t,s)&&hn(r,0,t),hn(r,0,s));const{has:i}=Cn(r),c=o?Pn:n?uo:co;return i.call(r,t)?c(e.get(t)):i.call(r,s)?c(e.get(s)):void(e!==r&&e.get(t))}function In(e,t=!1){const n=this.__v_raw,o=so(n),r=so(e);return t||(A(e,r)&&hn(o,0,e),hn(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function An(e,t=!1){return e=e.__v_raw,!t&&hn(so(e),0,pn),Reflect.get(e,"size",e)}function jn(e){e=so(e);const t=so(this);return Cn(t).has.call(t,e)||(t.add(e),gn(t,"add",e,e)),this}function Rn(e,t){t=so(t);const n=so(this),{has:o,get:r}=Cn(n);let s=o.call(n,e);s||(e=so(e),s=o.call(n,e));const i=r.call(n,e);return n.set(e,t),s?A(t,i)&&gn(n,"set",e,t):gn(n,"add",e,t),this}function Ln(e){const t=so(this),{has:n,get:o}=Cn(t);let r=n.call(t,e);r||(e=so(e),r=n.call(t,e)),o&&o.call(t,e);const s=t.delete(e);return r&&gn(t,"delete",e,void 0),s}function Tn(){const e=so(this),t=0!==e.size,n=e.clear();return t&&gn(e,"clear",void 0,void 0),n}function Mn(e,t){return function(n,o){const r=this,s=r.__v_raw,i=so(s),c=t?Pn:e?uo:co;return!e&&hn(i,0,pn),s.forEach(((e,t)=>n.call(o,c(e),c(t),r)))}}function Vn(e,t,n){return function(...o){const r=this.__v_raw,s=so(r),i=p(s),c="entries"===e||e===Symbol.iterator&&i,u="keys"===e&&i,a=r[e](...o),l=n?Pn:t?uo:co;return!t&&hn(s,0,u?dn:pn),{next(){const{value:e,done:t}=a.next();return t?{value:e,done:t}:{value:c?[l(e[0]),l(e[1])]:l(e),done:t}},[Symbol.iterator](){return this}}}}function Dn(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}function Hn(){const e={get(e){return En(this,e)},get size(){return An(this)},has:In,add:jn,set:Rn,delete:Ln,clear:Tn,forEach:Mn(!1,!1)},t={get(e){return En(this,e,!1,!0)},get size(){return An(this)},has:In,add:jn,set:Rn,delete:Ln,clear:Tn,forEach:Mn(!1,!0)},n={get(e){return En(this,e,!0)},get size(){return An(this,!0)},has(e){return In.call(this,e,!0)},add:Dn("add"),set:Dn("set"),delete:Dn("delete"),clear:Dn("clear"),forEach:Mn(!0,!1)},o={get(e){return En(this,e,!0,!0)},get size(){return An(this,!0)},has(e){return In.call(this,e,!0)},add:Dn("add"),set:Dn("set"),delete:Dn("delete"),clear:Dn("clear"),forEach:Mn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=Vn(r,!1,!1),n[r]=Vn(r,!0,!1),t[r]=Vn(r,!1,!0),o[r]=Vn(r,!0,!0)})),[e,n,t,o]}const[Nn,Bn,Un,Wn]=Hn();function zn(e,t){const n=t?e?Wn:Un:e?Bn:Nn;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(l(n,o)&&o in t?n:t,o,r)}const Fn={get:zn(!1,!1)},Kn={get:zn(!1,!0)},qn={get:zn(!0,!1)},Gn=new WeakMap,Jn=new WeakMap,Zn=new WeakMap,Qn=new WeakMap;function Xn(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>x(e).slice(8,-1))(e))}function Yn(e){return oo(e)?e:to(e,!1,Sn,Fn,Gn)}function eo(e){return to(e,!0,kn,qn,Zn)}function to(e,t,n,o,r){if(!v(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=r.get(e);if(s)return s;const i=Xn(e);if(0===i)return e;const c=new Proxy(e,2===i?o:n);return r.set(e,c),c}function no(e){return oo(e)?no(e.__v_raw):!(!e||!e.__v_isReactive)}function oo(e){return!(!e||!e.__v_isReadonly)}function ro(e){return!(!e||!e.__v_isShallow)}function so(e){const t=e&&e.__v_raw;return t?so(t):e}function io(e){return Object.isExtensible(e)&&((e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})})(e,"__v_skip",!0),e}const co=e=>v(e)?Yn(e):e,uo=e=>v(e)?eo(e):e;class ao{constructor(e,t,n,o){this.getter=e,this._setter=t,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this.effect=new Jt((()=>e(this._value)),(()=>fo(this,2===this.effect._dirtyLevel?2:3))),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=so(this);return e._cacheable&&!e.effect.dirty||!A(e._value,e._value=e.effect.run())||fo(e,4),lo(e),e.effect._dirtyLevel>=2&&fo(e,2),e._value}set value(e){this._setter(e)}get _dirty(){return this.effect.dirty}set _dirty(e){this.effect.dirty=e}}function lo(e){var t;Yt&&qt&&(e=so(e),cn(qt,null!=(t=e.dep)?t:e.dep=ln((()=>e.dep=void 0),e instanceof ao?e:void 0)))}function fo(e,t=4,n){const o=(e=so(e)).dep;o&&an(o,t)}function po(e){return!(!e||!0!==e.__v_isRef)}function ho(e){return function(e,t){if(po(e))return e;return new go(e,t)}(e,!1)}class go{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:so(e),this._value=t?e:co(e)}get value(){return lo(this),this._value}set value(e){const t=this.__v_isShallow||ro(e)||oo(e);e=t?e:so(e),A(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:co(e),fo(this,4))}}function mo(e){return po(e)?e.value:e}const vo={get:(e,t,n)=>mo(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return po(r)&&!po(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function _o(e){return no(e)?e:new Proxy(e,vo)}function yo(e,t,n,o){try{return o?e(...o):e()}catch(r){bo(r,t,n)}}function xo(e,t,n,o){if(h(e)){const r=yo(e,t,n,o);return r&&_(r)&&r.catch((e=>{bo(e,t,n)})),r}const r=[];for(let s=0;s<e.length;s++)r.push(xo(e[s],t,n,o));return r}function bo(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,s=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,s))return;o=o.parent}const i=t.appContext.config.errorHandler;if(i)return void yo(i,null,10,[e,r,s])}wo(e,n,r,o)}function wo(e,t,n,o=!0){console.error(e)}let $o=!1,So=!1;const ko=[];let Oo=0;const Po=[];let Co=null,Eo=0;const Io=Promise.resolve();let Ao=null;function jo(e){const t=Ao||Io;return e?t.then(this?e.bind(this):e):t}function Ro(e){ko.length&&ko.includes(e,$o&&e.allowRecurse?Oo+1:Oo)||(null==e.id?ko.push(e):ko.splice(function(e){let t=Oo+1,n=ko.length;for(;t<n;){const o=t+n>>>1,r=ko[o],s=Vo(r);s<e||s===e&&r.pre?t=o+1:n=o}return t}(e.id),0,e),Lo())}function Lo(){$o||So||(So=!0,Ao=Io.then(Ho))}function To(e){f(e)?Po.push(...e):Co&&Co.includes(e,e.allowRecurse?Eo+1:Eo)||Po.push(e),Lo()}function Mo(e,t,n=($o?Oo+1:0)){for(;n<ko.length;n++){const e=ko[n];e&&e.pre&&(ko.splice(n,1),n--,e())}}const Vo=e=>null==e.id?1/0:e.id,Do=(e,t)=>{const n=Vo(e)-Vo(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Ho(e){So=!1,$o=!0,ko.sort(Do);try{for(Oo=0;Oo<ko.length;Oo++){const e=ko[Oo];e&&!1!==e.active&&yo(e,null,14)}}finally{Oo=0,ko.length=0,function(e){if(Po.length){const e=[...new Set(Po)].sort(((e,t)=>Vo(e)-Vo(t)));if(Po.length=0,Co)return void Co.push(...e);for(Co=e,Eo=0;Eo<Co.length;Eo++)Co[Eo]();Co=null,Eo=0}}(),$o=!1,Ao=null,(ko.length||Po.length)&&Ho()}}function No(e,n,...o){if(e.isUnmounted)return;const r=e.vnode.props||t;let s=o;const i=n.startsWith("update:"),c=i&&n.slice(7);if(c&&c in r){const e=`${"modelValue"===c?"model":c}Modifiers`,{number:n,trim:i}=r[e]||t;i&&(s=o.map((e=>g(e)?e.trim():e))),n&&(s=o.map(R))}let u,a=r[u=I(n)]||r[u=I(O(n))];!a&&i&&(a=r[u=I(C(n))]),a&&xo(a,e,6,s);const l=r[u+"Once"];if(l){if(e.emitted){if(e.emitted[u])return}else e.emitted={};e.emitted[u]=!0,xo(l,e,6,s)}}function Bo(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const s=e.emits;let i={},u=!1;if(!h(e)){const o=e=>{const n=Bo(e,t,!0);n&&(u=!0,c(i,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return s||u?(f(s)?s.forEach((e=>i[e]=null)):c(i,s),v(e)&&o.set(e,i),i):(v(e)&&o.set(e,null),null)}function Uo(e,t){return!(!e||!s(t))&&(t=t.slice(2).replace(/Once$/,""),l(e,t[0].toLowerCase()+t.slice(1))||l(e,C(t))||l(e,t))}let Wo=null;function zo(e){const t=Wo;return Wo=e,e&&e.type.__scopeId,t}function Fo(e,t){return e&&(e[t]||e[O(t)]||e[E(O(t))])}const Ko={};function qo(e,t,n){return Go(e,t,n)}function Go(e,n,{immediate:r,deep:s,flush:i,once:c,onTrack:a,onTrigger:l}=t){if(n&&c){const e=n;n=(...t)=>{e(...t),k()}}const p=Gr,d=e=>!0===s?e:Qo(e,!1===s?1:void 0);let g,m,v=!1,_=!1;if(po(e)?(g=()=>e.value,v=ro(e)):no(e)?(g=()=>d(e),v=!0):f(e)?(_=!0,v=e.some((e=>no(e)||ro(e))),g=()=>e.map((e=>po(e)?e.value:no(e)?d(e):h(e)?yo(e,p,2):void 0))):g=h(e)?n?()=>yo(e,p,2):()=>(m&&m(),xo(e,p,3,[y])):o,n&&s){const e=g;g=()=>Qo(e())}let y=e=>{m=$.onStop=()=>{yo(e,p,4),m=$.onStop=void 0}},x=_?new Array(e.length).fill(Ko):Ko;const b=()=>{if($.active&&$.dirty)if(n){const e=$.run();(s||v||(_?e.some(((e,t)=>A(e,x[t]))):A(e,x)))&&(m&&m(),xo(n,p,3,[e,x===Ko?void 0:_&&x[0]===Ko?[]:x,y]),x=e)}else $.run()};let w;b.allowRecurse=!!n,"sync"===i?w=b:"post"===i?w=()=>Wr(b,p&&p.suspense):(b.pre=!0,p&&(b.id=p.uid),w=()=>Ro(b));const $=new Jt(g,o,w),S=Kt,k=()=>{$.stop(),S&&u(S.effects,$)};return n?r?b():x=$.run():"post"===i?Wr($.run.bind($),p&&p.suspense):$.run(),k}function Jo(e,t,n){const o=this.proxy,r=g(e)?e.includes(".")?Zo(o,e):()=>o[e]:e.bind(o,o);let s;h(t)?s=t:(s=t.handler,n=t);const i=Xr(this),c=Go(r,s.bind(o),n);return i(),c}function Zo(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Qo(e,t,n=0,o){if(!v(e)||e.__v_skip)return e;if(t&&t>0){if(n>=t)return e;n++}if((o=o||new Set).has(e))return e;if(o.add(e),po(e))Qo(e.value,t,n,o);else if(f(e))for(let r=0;r<e.length;r++)Qo(e[r],t,n,o);else if(d(e)||p(e))e.forEach((e=>{Qo(e,t,n,o)}));else if(b(e))for(const r in e)Qo(e[r],t,n,o);return e}function Xo(){return{app:null,config:{isNativeTag:r,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Yo=0;let er=null;function tr(e,t,n=!1){const o=Gr||Wo;if(o||er){const r=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:er._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&h(t)?t.call(o&&o.proxy):t}}function nr(e,t){rr(e,"a",t)}function or(e,t){rr(e,"da",t)}function rr(e,t,n=Gr){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(ir(t,o,n),n){let e=n.parent;for(;e&&e.parent;)e.parent.vnode.type.__isKeepAlive&&sr(o,t,n,e),e=e.parent}}function sr(e,t,n,o){const r=ir(t,e,o,!0);dr((()=>{u(o[t],r)}),n)}function ir(e,t,n=Gr,o=!1){if(n){(function(e){return G.indexOf(e)>-1})(e)&&(n=n.root);const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;nn();const r=Xr(n),s=xo(t,n,e,o);return r(),on(),s});return o?r.unshift(s):r.push(s),s}}const cr=e=>(t,n=Gr)=>(!ts||"sp"===e)&&ir(e,((...e)=>t(...e)),n),ur=cr("bm"),ar=cr("m"),lr=cr("bu"),fr=cr("u"),pr=cr("bum"),dr=cr("um"),hr=cr("sp"),gr=cr("rtg"),mr=cr("rtc");function vr(e,t=Gr){ir("ec",e,t)}const _r=e=>e?es(e)?rs(e)||e.proxy:_r(e.parent):null,yr=c(Object.create(null),{$:e=>e,$el:e=>e.__$el||(e.__$el={}),$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>_r(e.parent),$root:e=>_r(e.root),$emit:e=>e.emit,$options:e=>Pr(e),$forceUpdate:e=>e.f||(e.f=()=>{e.effect.dirty=!0,Ro(e.update)}),$watch:e=>Jo.bind(e)}),xr=(e,n)=>e!==t&&!e.__isScriptSetup&&l(e,n),br={get({_:e},n){const{ctx:o,setupState:r,data:s,props:i,accessCache:c,type:u,appContext:a}=e;let f;if("$"!==n[0]){const u=c[n];if(void 0!==u)switch(u){case 1:return r[n];case 2:return s[n];case 4:return o[n];case 3:return i[n]}else{if(xr(r,n))return c[n]=1,r[n];if(s!==t&&l(s,n))return c[n]=2,s[n];if((f=e.propsOptions[0])&&l(f,n))return c[n]=3,i[n];if(o!==t&&l(o,n))return c[n]=4,o[n];$r&&(c[n]=0)}}const p=yr[n];let d,h;return p?("$attrs"===n&&hn(e,0,n),p(e)):(d=u.__cssModules)&&(d=d[n])?d:o!==t&&l(o,n)?(c[n]=4,o[n]):(h=a.config.globalProperties,l(h,n)?h[n]:void 0)},set({_:e},n,o){const{data:r,setupState:s,ctx:i}=e;return xr(s,n)?(s[n]=o,!0):r!==t&&l(r,n)?(r[n]=o,!0):!l(e.props,n)&&(("$"!==n[0]||!(n.slice(1)in e))&&(i[n]=o,!0))},has({_:{data:e,setupState:n,accessCache:o,ctx:r,appContext:s,propsOptions:i}},c){let u;return!!o[c]||e!==t&&l(e,c)||xr(n,c)||(u=i[0])&&l(u,c)||l(r,c)||l(yr,c)||l(s.config.globalProperties,c)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:l(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function wr(e){return f(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let $r=!0;function Sr(e){const t=Pr(e),n=e.proxy,r=e.ctx;$r=!1,t.beforeCreate&&kr(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:c,watch:u,provide:a,inject:l,created:p,beforeMount:d,mounted:g,beforeUpdate:m,updated:_,activated:y,deactivated:x,beforeDestroy:b,beforeUnmount:w,destroyed:$,unmounted:S,render:k,renderTracked:O,renderTriggered:P,errorCaptured:C,serverPrefetch:E,expose:I,inheritAttrs:A,components:j,directives:R,filters:L}=t;if(l&&function(e,t,n=o){f(e)&&(e=Ar(e));for(const o in e){const n=e[o];let r;r=v(n)?"default"in n?tr(n.from||o,n.default,!0):tr(n.from||o):tr(n),po(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:e=>r.value=e}):t[o]=r}}(l,r,null),c)for(const o in c){const e=c[o];h(e)&&(r[o]=e.bind(n))}if(s){const t=s.call(n,n);v(t)&&(e.data=Yn(t))}if($r=!0,i)for(const f in i){const e=i[f],t=h(e)?e.bind(n,n):h(e.get)?e.get.bind(n,n):o,s=!h(e)&&h(e.set)?e.set.bind(n):o,c=ss({get:t,set:s});Object.defineProperty(r,f,{enumerable:!0,configurable:!0,get:()=>c.value,set:e=>c.value=e})}if(u)for(const o in u)Or(u[o],r,n,o);function T(e,t){f(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(function(){if(a){const e=h(a)?a.call(n):a;Reflect.ownKeys(e).forEach((t=>{!function(e,t){if(Gr){let n=Gr.provides;const o=Gr.parent&&Gr.parent.provides;o===n&&(n=Gr.provides=Object.create(o)),n[e]=t,"app"===Gr.type.mpType&&Gr.appContext.app.provide(e,t)}}(t,e[t])}))}}(),p&&kr(p,e,"c"),T(ur,d),T(ar,g),T(lr,m),T(fr,_),T(nr,y),T(or,x),T(vr,C),T(mr,O),T(gr,P),T(pr,w),T(dr,S),T(hr,E),f(I))if(I.length){const t=e.exposed||(e.exposed={});I.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===o&&(e.render=k),null!=A&&(e.inheritAttrs=A),j&&(e.components=j),R&&(e.directives=R),e.ctx.$onApplyOptions&&e.ctx.$onApplyOptions(t,e,n)}function kr(e,t,n){xo(f(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Or(e,t,n,o){const r=o.includes(".")?Zo(n,o):()=>n[o];if(g(e)){const n=t[e];h(n)&&qo(r,n)}else if(h(e))qo(r,e.bind(n));else if(v(e))if(f(e))e.forEach((e=>Or(e,t,n,o)));else{const o=h(e.handler)?e.handler.bind(n):t[e.handler];h(o)&&qo(r,o,e)}}function Pr(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:i}}=e.appContext,c=s.get(t);let u;return c?u=c:r.length||n||o?(u={},r.length&&r.forEach((e=>Cr(u,e,i,!0))),Cr(u,t,i)):u=t,v(t)&&s.set(t,u),u}function Cr(e,t,n,o=!1){const{mixins:r,extends:s}=t;s&&Cr(e,s,n,!0),r&&r.forEach((t=>Cr(e,t,n,!0)));for(const i in t)if(o&&"expose"===i);else{const o=Er[i]||n&&n[i];e[i]=o?o(e[i],t[i]):t[i]}return e}const Er={data:Ir,props:Lr,emits:Lr,methods:Rr,computed:Rr,beforeCreate:jr,created:jr,beforeMount:jr,mounted:jr,beforeUpdate:jr,updated:jr,beforeDestroy:jr,beforeUnmount:jr,destroyed:jr,unmounted:jr,activated:jr,deactivated:jr,errorCaptured:jr,serverPrefetch:jr,components:Rr,directives:Rr,watch:function(e,t){if(!e)return t;if(!t)return e;const n=c(Object.create(null),e);for(const o in t)n[o]=jr(e[o],t[o]);return n},provide:Ir,inject:function(e,t){return Rr(Ar(e),Ar(t))}};function Ir(e,t){return t?e?function(){return c(h(e)?e.call(this,this):e,h(t)?t.call(this,this):t)}:t:e}function Ar(e){if(f(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function jr(e,t){return e?[...new Set([].concat(e,t))]:t}function Rr(e,t){return e?c(Object.create(null),e,t):t}function Lr(e,t){return e?f(e)&&f(t)?[...new Set([...e,...t])]:c(Object.create(null),wr(e),wr(null!=t?t:{})):t}function Tr(e,t,n,o=!1){const r={},s={};e.propsDefaults=Object.create(null),Mr(e,t,r,s);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=o?r:to(r,!1,On,Kn,Jn):e.type.props?e.props=r:e.props=s,e.attrs=s}function Mr(e,n,o,r){const[s,i]=e.propsOptions;let c,u=!1;if(n)for(let t in n){if($(t))continue;const a=n[t];let f;s&&l(s,f=O(t))?i&&i.includes(f)?(c||(c={}))[f]=a:o[f]=a:Uo(e.emitsOptions,t)||t in r&&a===r[t]||(r[t]=a,u=!0)}if(i){const n=so(o),r=c||t;for(let t=0;t<i.length;t++){const c=i[t];o[c]=Vr(s,n,c,r[c],e,!l(r,c))}}return u}function Vr(e,t,n,o,r,s){const i=e[n];if(null!=i){const e=l(i,"default");if(e&&void 0===o){const e=i.default;if(i.type!==Function&&!i.skipFactory&&h(e)){const{propsDefaults:s}=r;if(n in s)o=s[n];else{const i=Xr(r);o=s[n]=e.call(null,t),i()}}else o=e}i[0]&&(s&&!e?o=!1:!i[1]||""!==o&&o!==C(n)||(o=!0))}return o}function Dr(e,o,r=!1){const s=o.propsCache,i=s.get(e);if(i)return i;const u=e.props,a={},p=[];let d=!1;if(!h(e)){const t=e=>{d=!0;const[t,n]=Dr(e,o,!0);c(a,t),n&&p.push(...n)};!r&&o.mixins.length&&o.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!u&&!d)return v(e)&&s.set(e,n),n;if(f(u))for(let n=0;n<u.length;n++){const e=O(u[n]);Hr(e)&&(a[e]=t)}else if(u)for(const t in u){const e=O(t);if(Hr(e)){const n=u[t],o=a[e]=f(n)||h(n)?{type:n}:c({},n);if(o){const t=Ur(Boolean,o.type),n=Ur(String,o.type);o[0]=t>-1,o[1]=n<0||t<n,(t>-1||l(o,"default"))&&p.push(e)}}}const g=[a,p];return v(e)&&s.set(e,g),g}function Hr(e){return"$"!==e[0]&&!$(e)}function Nr(e){if(null===e)return"null";if("function"==typeof e)return e.name||"";if("object"==typeof e){return e.constructor&&e.constructor.name||""}return""}function Br(e,t){return Nr(e)===Nr(t)}function Ur(e,t){return f(t)?t.findIndex((t=>Br(t,e))):h(t)&&Br(t,e)?0:-1}const Wr=To;function zr(e){return e?no(t=e)||oo(t)||"__vInternal"in e?c({},e):e:null;var t}const Fr=Xo();let Kr=0;function qr(e,n,o){const r=e.type,s=(n?n.appContext:e.appContext)||Fr,i={uid:Kr++,vnode:e,type:r,parent:n,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new Gt(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Dr(r,s),emitsOptions:Bo(r,s),emit:null,emitted:null,propsDefaults:t,inheritAttrs:r.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:o,suspenseId:o?o.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null,$uniElements:new Map,$templateUniElementRefs:[],$templateUniElementStyles:{},$eS:{},$eA:{}};return i.ctx={_:i},i.root=n?n.root:i,i.emit=No.bind(null,i),e.ce&&e.ce(i),i}let Gr=null;const Jr=()=>Gr||Wo;let Zr,Qr;Zr=e=>{Gr=e},Qr=e=>{ts=e};const Xr=e=>{const t=Gr;return Zr(e),e.scope.on(),()=>{e.scope.off(),Zr(t)}},Yr=()=>{Gr&&Gr.scope.off(),Zr(null)};function es(e){return 4&e.vnode.shapeFlag}let ts=!1;function ns(e,t=!1){t&&Qr(t);const{props:n}=e.vnode,o=es(e);Tr(e,n,o,t);const r=o?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=io(new Proxy(e.ctx,br));const{setup:o}=n;if(o){const t=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return function(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get:(t,n)=>(hn(e,0,"$attrs"),t[n])}))}(e)},slots:e.slots,emit:e.emit,expose:t}}(e):null,n=Xr(e);nn();const r=yo(o,e,0,[e.props,t]);on(),n(),_(r)?r.then(Yr,Yr):function(e,t,n){h(t)?e.render=t:v(t)&&(e.setupState=_o(t));os(e)}(e,r)}else os(e)}(e):void 0;return t&&Qr(!1),r}function os(e,t,n){const r=e.type;e.render||(e.render=r.render||o);{const t=Xr(e);nn();try{Sr(e)}finally{on(),t()}}}function rs(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(_o(io(e.exposed)),{get:(t,n)=>n in t?t[n]:e.proxy[n],has:(e,t)=>t in e||t in yr}))}const ss=(e,t)=>{const n=function(e,t,n=!1){let r,s;const i=h(e);return i?(r=e,s=o):(r=e.get,s=e.set),new ao(r,s,i||!s,n)}(e,0,ts);return n},is="3.4.21";function cs(e){return mo(e)}const us="[object Array]",as="[object Object]";function ls(e,t){const n={};return fs(e,t),ps(e,t,"",n),n}function fs(e,t){if((e=cs(e))===t)return;const n=x(e),o=x(t);if(n==as&&o==as)for(let r in t){const n=e[r];void 0===n?e[r]=null:fs(n,t[r])}else n==us&&o==us&&e.length>=t.length&&t.forEach(((t,n)=>{fs(e[n],t)}))}function ps(e,t,n,o){if((e=cs(e))===t)return;const r=x(e),s=x(t);if(r==as)if(s!=as||Object.keys(e).length<Object.keys(t).length)ds(o,n,e);else for(let i in e){const r=cs(e[i]),s=t[i],c=x(r),u=x(s);if(c!=us&&c!=as)r!=s&&ds(o,(""==n?"":n+".")+i,r);else if(c==us)u!=us||r.length<s.length?ds(o,(""==n?"":n+".")+i,r):r.forEach(((e,t)=>{ps(e,s[t],(""==n?"":n+".")+i+"["+t+"]",o)}));else if(c==as)if(u!=as||Object.keys(r).length<Object.keys(s).length)ds(o,(""==n?"":n+".")+i,r);else for(let e in r)ps(r[e],s[e],(""==n?"":n+".")+i+"."+e,o)}else r==us?s!=us||e.length<t.length?ds(o,n,e):e.forEach(((e,r)=>{ps(e,t[r],n+"["+r+"]",o)})):ds(o,n,e)}function ds(e,t,n){e[t]=n}function hs(e){const t=e.ctx.__next_tick_callbacks;if(t&&t.length){const e=t.slice(0);t.length=0;for(let t=0;t<e.length;t++)e[t]()}}function gs(e,t){const n=e.ctx;if(!n.__next_tick_pending&&!function(e){return ko.includes(e.update)}(e))return jo(t&&t.bind(e.proxy));let o;return n.__next_tick_callbacks||(n.__next_tick_callbacks=[]),n.__next_tick_callbacks.push((()=>{t?yo(t.bind(e.proxy),e,14):o&&o(e.proxy)})),new Promise((e=>{o=e}))}function ms(e,t){const n=typeof(e=cs(e));if("object"===n&&null!==e){let n=t.get(e);if(void 0!==n)return n;if(f(e)){const o=e.length;n=new Array(o),t.set(e,n);for(let r=0;r<o;r++)n[r]=ms(e[r],t)}else{n={},t.set(e,n);for(const o in e)l(e,o)&&(n[o]=ms(e[o],t))}return n}if("symbol"!==n)return e}function vs(e){return ms(e,"undefined"!=typeof WeakMap?new WeakMap:new Map)}function _s(e,t,n){if(!t)return;(t=vs(t)).$eS=e.$eS||{},t.$eA=e.$eA||{};const o=e.ctx,r=o.mpType;if("page"===r||"component"===r){t.r0=1;const r=o.$scope,s=Object.keys(t),i=ls(t,n||function(e,t){const n=e.data,o=Object.create(null);return t.forEach((e=>{o[e]=n[e]})),o}(r,s));Object.keys(i).length?(o.__next_tick_pending=!0,r.setData(i,(()=>{o.__next_tick_pending=!1,hs(e)})),Mo()):hs(e)}}function ys(e,t,n){t.appContext.config.globalProperties.$applyOptions(e,t,n);const o=e.computed;if(o){const e=Object.keys(o);if(e.length){const n=t.ctx;n.$computedKeys||(n.$computedKeys=[]),n.$computedKeys.push(...e)}}delete t.ctx.$onApplyOptions}function xs(e,t=!1){const{setupState:n,$templateRefs:o,$templateUniElementRefs:r,ctx:{$scope:s,$mpPlatform:i}}=e;if("mp-alipay"===i)return;if(!s||!o&&!r)return;if(t)return o&&o.forEach((e=>bs(e,null,n))),void(r&&r.forEach((e=>bs(e,null,n))));const c="mp-baidu"===i||"mp-toutiao"===i,u=e=>{if(0===e.length)return[];const t=(s.selectAllComponents(".r")||[]).concat(s.selectAllComponents(".r-i-f")||[]);return e.filter((e=>{const o=function(e,t){const n=e.find((e=>e&&(e.properties||e.props).uI===t));if(n){const e=n.$vm;return e?rs(e.$)||e:function(e){v(e)&&io(e);return e}(n)}return null}(t,e.i);return!(!c||null!==o)||(bs(e,o,n),!1)}))},a=()=>{if(o){const t=u(o);t.length&&e.proxy&&e.proxy.$scope&&e.proxy.$scope.setData({r1:1},(()=>{u(t)}))}};r&&r.length&&gs(e,(()=>{r.forEach((e=>{f(e.v)?e.v.forEach((t=>{bs(e,t,n)})):bs(e,e.v,n)}))})),s._$setRef?s._$setRef(a):gs(e,a)}function bs({r:e,f:t},n,o){if(h(e))e(n,{});else{const r=g(e),s=po(e);if(r||s)if(t){if(!s)return;f(e.value)||(e.value=[]);const t=e.value;if(-1===t.indexOf(n)){if(t.push(n),!n)return;n.$&&pr((()=>u(t,n)),n.$)}}else r?l(o,e)&&(o[e]=n):po(e)&&(e.value=n)}}const ws=To;function $s(e,t){const n=e.component=qr(e,t.parentComponent,null);return n.ctx.$onApplyOptions=ys,n.ctx.$children=[],"app"===t.mpType&&(n.render=o),t.onBeforeSetup&&t.onBeforeSetup(n,t),ns(n),t.parentComponent&&n.proxy&&t.parentComponent.ctx.$children.push(rs(n)||n.proxy),function(e){const t=Os.bind(e);e.$updateScopedSlots=()=>jo((()=>Ro(t)));const n=()=>{if(e.isMounted){const{next:t,bu:n,u:o}=e;Ps(e,!1),nn(),Mo(),on(),n&&j(n),Ps(e,!0),_s(e,Ss(e)),o&&ws(o)}else pr((()=>{xs(e,!0)}),e),_s(e,Ss(e))},r=e.effect=new Jt(n,o,(()=>Ro(s)),e.scope),s=e.update=()=>{r.dirty&&r.run()};s.id=e.uid,Ps(e,!0),s()}(n),n.proxy}function Ss(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[c],slots:u,attrs:a,emit:l,render:f,renderCache:p,data:d,setupState:h,ctx:g,uid:m,appContext:{app:{config:{globalProperties:{pruneComponentPropsCache:v}}}},inheritAttrs:_}=e;let y;e.$uniElementIds=new Map,e.$templateRefs=[],e.$templateUniElementRefs=[],e.$templateUniElementStyles={},e.$ei=0,v(m),e.__counter=0===e.__counter?1:0;const x=zo(e);try{if(4&n.shapeFlag){ks(_,i,c,a);const e=r||o;y=f.call(e,e,p,i,h,d,g)}else{ks(_,i,c,t.props?a:(e=>{let t;for(const n in e)("class"===n||"style"===n||s(n))&&((t||(t={}))[n]=e[n]);return t})(a));const e=t;y=e.length>1?e(i,{attrs:a,slots:u,emit:l}):e(i,null)}}catch(b){bo(b,e,1),y=!1}return xs(e),zo(x),y}function ks(e,t,n,o){if(t&&o&&!1!==e){const e=Object.keys(o).filter((e=>"class"!==e&&"style"!==e));if(!e.length)return;n&&e.some(i)?e.forEach((e=>{i(e)&&e.slice(9)in n||(t[e]=o[e])})):e.forEach((e=>t[e]=o[e]))}}function Os(){const e=this.$scopedSlotsData;if(!e||0===e.length)return;const t=this.ctx.$scope,n=t.data,o=Object.create(null);e.forEach((({path:e,index:t,data:r})=>{const s=W(n,e),i=g(t)?`${e}.${t}`:`${e}[${t}]`;if(void 0===s||void 0===s[t])o[i]=r;else{const e=ls(r,s[t]);Object.keys(e).forEach((t=>{o[i+"."+t]=e[t]}))}})),e.length=0,Object.keys(o).length&&t.setData(o)}function Ps({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}const Cs=function(e,t=null){h(e)||(e=c({},e)),null==t||v(t)||(t=null);const n=Xo(),o=new WeakSet,r=n.app={_uid:Yo++,_component:e,_props:t,_container:null,_context:n,_instance:null,version:is,get config(){return n.config},set config(e){},use:(e,...t)=>(o.has(e)||(e&&h(e.install)?(o.add(e),e.install(r,...t)):h(e)&&(o.add(e),e(r,...t))),r),mixin:e=>(n.mixins.includes(e)||n.mixins.push(e),r),component:(e,t)=>t?(n.components[e]=t,r):n.components[e],directive:(e,t)=>t?(n.directives[e]=t,r):n.directives[e],mount(){},unmount(){},provide:(e,t)=>(n.provides[e]=t,r),runWithContext(e){const t=er;er=r;try{return e()}finally{er=t}}};return r};function Es(e,t=null){("undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:"undefined"!=typeof my?my:void 0).__VUE__=!0;const n=Cs(e,t),r=n._context;r.config.globalProperties.$nextTick=function(e){return gs(this.$,e)};const s=e=>(e.appContext=r,e.shapeFlag=6,e),i=function(e,t){return $s(s(e),t)},c=function(e){return e&&function(e){const{bum:t,scope:n,update:o,um:r}=e;t&&j(t);{const t=e.parent;if(t){const n=t.ctx.$children,o=rs(e)||e.proxy,r=n.indexOf(o);r>-1&&n.splice(r,1)}}n.stop(),o&&(o.active=!1),r&&ws(r),ws((()=>{e.isUnmounted=!0}))}(e.$)};return n.mount=function(){e.render=o;const t=$s(s({type:e}),{mpType:"app",mpInstance:null,parentComponent:null,slots:[],props:null});return n._instance=t.$,t.$app=n,t.$createComponent=i,t.$destroyComponent=c,r.$appInstance=t,t},n.unmount=function(){},n}function Is(e,t,n,o){h(t)&&ir(e,t.bind(n),o)}function As(e,t,n){!function(e,t,n){const o=e.mpType||n.$mpType;o&&"component"!==o&&Object.keys(e).forEach((o=>{if(Q(o,e[o],!1)){const r=e[o];f(r)?r.forEach((e=>Is(o,e,n,t))):Is(o,r,n,t)}}))}(e,t,n)}function js(e,t,n){return e[t]=n}function Rs(e,...t){const n=this[e];return n?n(...t):(console.error(`method ${e} not found`),null)}function Ls(e){const t=e.config.errorHandler;return function(n,o,r){t&&t(n,o,r);const s=e._instance;if(!s||!s.proxy)throw n;s.onError?s.proxy.$callHook("onError",n):wo(n,0,o&&o.$.vnode,!1)}}function Ts(e,t){return e?[...new Set([].concat(e,t))]:t}let Ms;const Vs="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",Ds=/^(?:[A-Za-z\d+/]{4})*?(?:[A-Za-z\d+/]{2}(?:==)?|[A-Za-z\d+/]{3}=?)?$/;function Hs(){const e=Ft.getStorageSync("uni_id_token")||"",t=e.split(".");if(!e||3!==t.length)return{uid:null,role:[],permission:[],tokenExpired:0};let n;try{n=JSON.parse((o=t[1],decodeURIComponent(Ms(o).split("").map((function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)})).join(""))))}catch(r){throw new Error("获取当前用户信息出错，详细错误信息为："+r.message)}var o;return n.tokenExpired=1e3*n.exp,delete n.exp,delete n.iat,n}function Ns(e){const t=e.config;var n;t.errorHandler=ee(e,Ls),n=t.optionMergeStrategies,J.forEach((e=>{n[e]=Ts}));const o=t.globalProperties;!function(e){e.uniIDHasRole=function(e){const{role:t}=Hs();return t.indexOf(e)>-1},e.uniIDHasPermission=function(e){const{permission:t}=Hs();return this.uniIDHasRole("admin")||t.indexOf(e)>-1},e.uniIDTokenValid=function(){const{tokenExpired:e}=Hs();return e>Date.now()}}(o),o.$set=js,o.$applyOptions=As,o.$callMethod=Rs,Ft.invokeCreateVueAppHook(e)}Ms="function"!=typeof atob?function(e){if(e=String(e).replace(/[\t\n\f\r ]+/g,""),!Ds.test(e))throw new Error("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.");var t;e+="==".slice(2-(3&e.length));for(var n,o,r="",s=0;s<e.length;)t=Vs.indexOf(e.charAt(s++))<<18|Vs.indexOf(e.charAt(s++))<<12|(n=Vs.indexOf(e.charAt(s++)))<<6|(o=Vs.indexOf(e.charAt(s++))),r+=64===n?String.fromCharCode(t>>16&255):64===o?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return r}:atob;const Bs=Object.create(null);function Us(e){delete Bs[e]}function Ws(e){if(!e)return;const[t,n]=e.split(",");return Bs[t]?Bs[t][parseInt(n)]:void 0}var zs={install(e){Ns(e),e.config.globalProperties.pruneComponentPropsCache=Us;const t=e.mount;e.mount=function(n){const o=t.call(e,n),r=function(){const e="createApp";if("undefined"!=typeof global&&void 0!==global[e])return global[e];if("undefined"!=typeof my)return my[e]}();return r?r(o):"undefined"!=typeof createMiniProgramApp&&createMiniProgramApp(o),o}}};function Fs(e){return g(e)?e:function(e){let t="";if(!e||g(e))return t;for(const n in e)t+=`${n.startsWith("--")?n:C(n)}:${e[n]};`;return t}(L(e))}function Ks(e,t){const n=Jr(),r=n.ctx,s=void 0===t||"mp-weixin"!==r.$mpPlatform&&"mp-qq"!==r.$mpPlatform&&"mp-xhs"!==r.$mpPlatform||!g(t)&&"number"!=typeof t?"":"_"+t,i="e"+n.$ei+++s,u=r.$scope;if(!e)return delete u[i],i;const a=u[i];return a?a.value=e:u[i]=function(e,t){const n=e=>{var r;(r=e).type&&r.target&&(r.preventDefault=o,r.stopPropagation=o,r.stopImmediatePropagation=o,l(r,"detail")||(r.detail={}),l(r,"markerId")&&(r.detail="object"==typeof r.detail?r.detail:{},r.detail.markerId=r.markerId),b(r.detail)&&l(r.detail,"checked")&&!l(r.detail,"value")&&(r.detail.value=r.detail.checked),b(r.detail)&&(r.target=c({},r.target,r.detail)));let s=[e];t&&t.ctx.$getTriggerEventDetail&&"number"==typeof e.detail&&(e.detail=t.ctx.$getTriggerEventDetail(e.detail)),e.detail&&e.detail.__args__&&(s=e.detail.__args__);const i=n.value,u=()=>xo(function(e,t){if(f(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n&&n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e(t)))}return t}(e,i),t,5,s),a=e.target,p=!!a&&(!!a.dataset&&"true"===String(a.dataset.eventsync));if(!qs.includes(e.type)||p){const t=u();if("input"===e.type&&(f(t)||_(t)))return;return t}setTimeout(u)};return n.value=e,n}(e,n),i}const qs=["tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend","touchforcechange"];function Gs(e,t={},n){const o=Jr(),{parent:r,isMounted:s,ctx:{$scope:i}}=o,c=(i.properties||i.props).uI;if(!c)return;if(!r&&!s)return void ar((()=>{Gs(e,t,n)}),o);const u=function(e,t){let n=t.parent;for(;n;){const t=n.$ssi;if(t&&t[e])return t[e];n=n.parent}}(c,o);u&&u(e,t,n)}const Js=function(e,t=null){return e&&(e.mpType="app"),Es(e,t).use(zs)};const Zs=["externalClasses"];const Qs=/_(.*)_worklet_factory_/;function Xs(e,t){const n=e.$children;for(let r=n.length-1;r>=0;r--){const e=n[r];if(e.$scope._$vueId===t)return e}let o;for(let r=n.length-1;r>=0;r--)if(o=Xs(n[r],t),o)return o}const Ys=["createSelectorQuery","createIntersectionObserver","selectAllComponents","selectComponent"];function ei(e,t){const n=e.ctx;n.mpType=t.mpType,n.$mpType=t.mpType,n.$mpPlatform="mp-weixin",n.$scope=t.mpInstance,Object.defineProperties(n,{virtualHostId:{get(){const e=this.$scope.data.virtualHostId;return void 0===e?"":e}}}),n.$mp={},n._self={},e.slots={},f(t.slots)&&t.slots.length&&(t.slots.forEach((t=>{e.slots[t]=!0})),e.slots.d&&(e.slots.default=!0)),n.getOpenerEventChannel=function(){return t.mpInstance.getOpenerEventChannel()},n.$hasHook=ti,n.$callHook=ni,e.emit=function(e,t){return function(n,...o){const r=t.$scope;if(r&&n){const e={__args__:o};r.triggerEvent(n,e)}return e.apply(this,[n,...o])}}(e.emit,n)}function ti(e){const t=this.$[e];return!(!t||!t.length)}function ni(e,t){"mounted"===e&&(ni.call(this,"bm"),this.$.isMounted=!0,e="m");const n=this.$[e];return n&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(n,t)}const oi=["onLoad","onShow","onHide","onUnload","onResize","onTabItemTap","onReachBottom","onPullDownRefresh","onAddToFavorites"];function ri(e,t=new Set){if(e){Object.keys(e).forEach((n=>{Q(n,e[n])&&t.add(n)}));{const{extends:n,mixins:o}=e;o&&o.forEach((e=>ri(e,t))),n&&ri(n,t)}}return t}function si(e,t,n){-1!==n.indexOf(t)||l(e,t)||(e[t]=function(e){return this.$vm&&this.$vm.$callHook(t,e)})}const ii=["onReady"];function ci(e,t,n=ii){t.forEach((t=>si(e,t,n)))}function ui(e,t,n=ii){ri(t).forEach((t=>si(e,t,n)))}const ai=U((()=>{const e=[],t=h(getApp)&&getApp({allowDefault:!0});if(t&&t.$vm&&t.$vm.$){const n=t.$vm.$.appContext.mixins;if(f(n)){const t=Object.keys(Z);n.forEach((n=>{t.forEach((t=>{l(n,t)&&!e.includes(t)&&e.push(t)}))}))}}return e}));const li=["onShow","onHide","onError","onThemeChange","onPageNotFound","onUnhandledRejection"];function fi(e,t){const n=e.$,o={globalData:e.$options&&e.$options.globalData||{},$vm:e,onLaunch(t){this.$vm=e;const o=n.ctx;this.$vm&&o.$scope&&o.$callHook||(ei(n,{mpType:"app",mpInstance:this,slots:[]}),o.globalData=this.globalData,e.$callHook("onLaunch",t))}},r=wx.$onErrorHandlers;r&&(r.forEach((e=>{ir("onError",e,n)})),r.length=0),function(e){const t=ho(function(){var e;let t="";{const n=(null===(e=wx.getAppBaseInfo)||void 0===e?void 0:e.call(wx))||wx.getSystemInfoSync();t=oe(n&&n.language?n.language:"en")||"en"}return t}());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(e);const s=e.$.type;ci(o,li),ui(o,s);{const e=s.methods;e&&c(o,e)}return o}function pi(e,t){if(h(e.onLaunch)){const t=wx.getLaunchOptionsSync&&wx.getLaunchOptionsSync();e.onLaunch(t)}h(e.onShow)&&wx.onAppShow&&wx.onAppShow((e=>{t.$callHook("onShow",e)})),h(e.onHide)&&wx.onAppHide&&wx.onAppHide((e=>{t.$callHook("onHide",e)}))}const di=["eO","uR","uRIF","uI","uT","uP","uS"];function hi(e){e.properties||(e.properties={}),c(e.properties,function(e,t=!1){const n={};if(!t){let e=function(e){const t=Object.create(null);e&&e.forEach((e=>{t[e]=!0})),this.setData({$slots:t})};di.forEach((e=>{n[e]={type:null,value:""}})),n.uS={type:null,value:[]},n.uS.observer=e}return e.behaviors&&e.behaviors.includes("wx://form-field")&&(e.properties&&e.properties.name||(n.name={type:null,value:""}),e.properties&&e.properties.value||(n.value={type:null,value:""})),n}(e),function(e){const t={};return e&&e.virtualHost&&(t.virtualHostStyle={type:null,value:""},t.virtualHostClass={type:null,value:""},t.virtualHostHidden={type:null,value:""},t.virtualHostId={type:null,value:""}),t}(e.options))}const gi=[String,Number,Boolean,Object,Array,null];function mi(e,t){const n=function(e,t){return f(e)&&1===e.length?e[0]:e}(e);return-1!==gi.indexOf(n)?n:null}function vi(e,t){return(t?function(e){const t={};b(e)&&Object.keys(e).forEach((n=>{-1===di.indexOf(n)&&(t[n]=e[n])}));return t}(e):Ws(e.uP))||{}}function _i(e){const t=function(){const e=this.properties.uP;e&&(this.$vm?function(e,t){const n=so(t.props),o=Ws(e)||{};yi(n,o)&&(!function(e,t,n,o){const{props:r,attrs:s,vnode:{patchFlag:i}}=e,c=so(r),[u]=e.propsOptions;let a=!1;if(!(o||i>0)||16&i){let o;Mr(e,t,r,s)&&(a=!0);for(const s in c)t&&(l(t,s)||(o=C(s))!==s&&l(t,o))||(u?!n||void 0===n[s]&&void 0===n[o]||(r[s]=Vr(u,c,s,void 0,e,!0)):delete r[s]);if(s!==c)for(const e in s)t&&l(t,e)||(delete s[e],a=!0)}else if(8&i){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let i=n[o];if(Uo(e.emitsOptions,i))continue;const f=t[i];if(u)if(l(s,i))f!==s[i]&&(s[i]=f,a=!0);else{const t=O(i);r[t]=Vr(u,c,t,f,e,!1)}else f!==s[i]&&(s[i]=f,a=!0)}}a&&gn(e,"set","$attrs")}(t,o,n,!1),r=t.update,ko.indexOf(r)>-1&&function(e){const t=ko.indexOf(e);t>Oo&&ko.splice(t,1)}(t.update),t.update());var r}(e,this.$vm.$):"m"===this.properties.uT&&function(e,t){const n=t.properties,o=Ws(e)||{};yi(n,o,!1)&&t.setData(o)}(e,this))};e.observers||(e.observers={}),e.observers.uP=t}function yi(e,t,n=!0){const o=Object.keys(t);if(n&&o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const n=o[r];if(t[n]!==e[n])return!0}return!1}function xi(e,t){e.data={},e.behaviors=function(e){const t=e.behaviors;let n=e.props;n||(e.props=n=[]);const o=[];return f(t)&&t.forEach((e=>{o.push(e.replace("uni://","wx://")),"uni://form-field"===e&&(f(n)?(n.push("name"),n.push("modelValue")):(n.name={type:String,default:""},n.modelValue={type:[String,Number,Boolean,Array,Object,Date],default:""}))})),o}(t)}function bi(e,{parse:t,mocks:n,isPage:o,isPageInProject:r,initRelation:s,handleLink:i,initLifetimes:u}){e=e.default||e;const a={multipleSlots:!0,addGlobalClass:!0,pureDataPattern:/^uP$/};f(e.mixins)&&e.mixins.forEach((e=>{v(e.options)&&c(a,e.options)})),e.options&&c(a,e.options);const p={options:a,lifetimes:u({mocks:n,isPage:o,initRelation:s,vueOptions:e}),pageLifetimes:{show(){this.$vm&&this.$vm.$callHook("onPageShow")},hide(){this.$vm&&this.$vm.$callHook("onPageHide")},resize(e){this.$vm&&this.$vm.$callHook("onPageResize",e)}},methods:{__l:i}};var d,h,g,m;return xi(p,e),hi(p),_i(p),function(e,t){Zs.forEach((n=>{l(t,n)&&(e[n]=t[n])}))}(p,e),d=p.methods,h=e.wxsCallMethods,f(h)&&h.forEach((e=>{d[e]=function(t){return this.$vm[e](t)}})),g=p.methods,(m=e.methods)&&Object.keys(m).forEach((e=>{const t=e.match(Qs);if(t){const n=t[1];g[e]=m[e],g[n]=m[n]}})),t&&t(p,{handleLink:i}),p}let wi,$i;function Si(){return getApp().$vm}function ki(e,t){const{parse:n,mocks:o,isPage:r,initRelation:s,handleLink:i,initLifetimes:c}=t,u=bi(e,{mocks:o,isPage:r,isPageInProject:!0,initRelation:s,handleLink:i,initLifetimes:c});!function({properties:e},t){f(t)?t.forEach((t=>{e[t]={type:String,value:""}})):b(t)&&Object.keys(t).forEach((n=>{const o=t[n];if(b(o)){let t=o.default;h(t)&&(t=t());const r=o.type;o.type=mi(r),e[n]={type:o.type,value:t}}else e[n]={type:mi(o)}}))}(u,(e.default||e).props);const a=u.methods;return a.onLoad=function(e){var t;return this.options=e,this.$page={fullPath:(t=this.route+q(e),function(e){return 0===e.indexOf("/")}(t)?t:"/"+t)},this.$vm&&this.$vm.$callHook("onLoad",e)},ci(a,oi),ui(a,e),function(e,t){if(!t)return;Object.keys(Z).forEach((n=>{t&Z[n]&&si(e,n,[])}))}(a,e.__runtimeHooks),ci(a,ai()),n&&n(u,{handleLink:i}),u}const Oi=Page,Pi=Component;function Ci(e){const t=e.triggerEvent,n=function(n,...o){return t.apply(e,[(r=n,O(r.replace(F,"-"))),...o]);var r};try{e.triggerEvent=n}catch(o){e._triggerEvent=n}}function Ei(e,t,n){const o=t[e];t[e]=o?function(...e){return Ci(this),o.apply(this,e)}:function(){Ci(this)}}Page=function(e){return Ei("onLoad",e),Oi(e)},Component=function(e){Ei("created",e);return e.properties&&e.properties.uP||(hi(e),_i(e)),Pi(e)};var Ii=Object.freeze({__proto__:null,handleLink:function(e){const t=e.detail||e.value,n=t.vuePid;let o;n&&(o=Xs(this.$vm,n)),o||(o=this.$vm),t.parent=o},initLifetimes:function({mocks:e,isPage:t,initRelation:n,vueOptions:o}){return{attached(){let r=this.properties;!function(e,t){if(!e)return;const n=e.split(","),o=n.length;1===o?t._$vueId=n[0]:2===o&&(t._$vueId=n[0],t._$vuePid=n[1])}(r.uI,this);const s={vuePid:this._$vuePid};n(this,s);const i=this,c=t(i);let u=r;this.$vm=function(e,t){wi||(wi=Si().$createComponent);const n=wi(e,t);return rs(n.$)||n}({type:o,props:vi(u,c)},{mpType:c?"page":"component",mpInstance:i,slots:r.uS||{},parentComponent:s.parent&&s.parent.$,onBeforeSetup(t,n){!function(e,t){Object.defineProperty(e,"refs",{get(){const e={};return function(e,t,n){e.selectAllComponents(t).forEach((e=>{const t=e.properties.uR;n[t]=e.$vm||e}))}(t,".r",e),t.selectAllComponents(".r-i-f").forEach((t=>{const n=t.properties.uR;n&&(e[n]||(e[n]=[]),e[n].push(t.$vm||t))})),e}})}(t,i),function(e,t,n){const o=e.ctx;n.forEach((n=>{l(t,n)&&(e[n]=o[n]=t[n])}))}(t,i,e),function(e,t){ei(e,t);const n=e.ctx;Ys.forEach((e=>{n[e]=function(...t){const o=n.$scope;if(o&&o[e])return o[e].apply(o,t)}}))}(t,n)}}),c||function(e){const t=e.$options;f(t.behaviors)&&t.behaviors.includes("uni://form-field")&&e.$watch("modelValue",(()=>{e.$scope&&e.$scope.setData({name:e.name,value:e.modelValue})}),{immediate:!0})}(this.$vm)},ready(){this.$vm&&(this.$vm.$callHook("mounted"),this.$vm.$callHook("onReady"))},detached(){var e;this.$vm&&(Us(this.$vm.$.uid),e=this.$vm,$i||($i=Si().$destroyComponent),$i(e))}}},initRelation:function(e,t){e.triggerEvent("__l",t)},isPage:function(e){return!!e.route},mocks:["__route__","__wxExparserNodeId__","__wxWebviewId__"]});const Ai=function(e){return App(fi(e))},ji=(Ri=Ii,function(e){return Component(ki(e,Ri))});var Ri;const Li=function(e){return function(t){return Component(bi(t,e))}}(Ii),Ti=function(e){pi(fi(e),e)},Mi=function(e){const t=fi(e),n=h(getApp)&&getApp({allowDefault:!0});if(!n)return;e.$.ctx.$scope=n;const o=n.globalData;o&&Object.keys(t.globalData).forEach((e=>{l(o,e)||(o[e]=t.globalData[e])})),Object.keys(t).forEach((e=>{l(n,e)||(n[e]=t[e])})),pi(t,e)};wx.createApp=global.createApp=Ai,wx.createPage=ji,wx.createComponent=Li,wx.createPluginApp=global.createPluginApp=Ti,wx.createSubpackageApp=global.createSubpackageApp=Mi,exports._export_sfc=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n},exports.createSSRApp=Js,exports.e=(e,...t)=>c(e,...t),exports.f=(e,t)=>function(e,t){let n;if(f(e)||g(e)){n=new Array(e.length);for(let o=0,r=e.length;o<r;o++)n[o]=t(e[o],o,o)}else if("number"==typeof e){n=new Array(e);for(let o=0;o<e;o++)n[o]=t(o+1,o,o)}else if(v(e))if(e[Symbol.iterator])n=Array.from(e,((e,n)=>t(e,n,n)));else{const o=Object.keys(e);n=new Array(o.length);for(let r=0,s=o.length;r<s;r++){const s=o[r];n[r]=t(e[s],s,r)}}else n=[];return n}(e,t),exports.index=Ft,exports.n=e=>H(e),exports.nextTick$1=jo,exports.o=(e,t)=>Ks(e,t),exports.p=e=>function(e){const{uid:t,__counter:n}=Jr();return t+","+((Bs[t]||(Bs[t]=[])).push(zr(e))-1)+","+n}(e),exports.r=(e,t,n)=>Gs(e,t,n),exports.resolveComponent=function(e,t){return function(e,t,n=!0,o=!1){const r=Wo||Gr;if(r){const n=r.type;if("components"===e){const e=function(e,t=!0){return h(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===O(t)||e===E(O(t))))return n}const s=Fo(r[e]||n[e],t)||Fo(r.appContext[e],t);return!s&&o?n:s}}("components",e,!0,t)||e},exports.s=e=>Fs(e),exports.t=e=>(e=>g(e)?e:null==e?"":f(e)||v(e)&&(e.toString===y||!h(e.toString))?JSON.stringify(e,N,2):String(e))(e),exports.useCssVars=function(e){const t=Jr();t&&function(e,t){e.ctx.__cssVars=()=>{const n=t(e.proxy),o={};for(const e in n)o[`--${e}`]=n[e];return o}}(t,e)};
