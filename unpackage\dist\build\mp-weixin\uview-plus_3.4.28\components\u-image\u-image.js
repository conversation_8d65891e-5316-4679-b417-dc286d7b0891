"use strict";const i=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),r=require("../../libs/mixin/mixin.js"),o=require("../../libs/function/index.js"),e=require("../../../common/vendor.js"),s={name:"u-image",mixins:[t.mpMixin,r.mixin,i.props],data(){return{isError:!1,loading:!0,opacity:1,durationTime:this.duration,backgroundStyle:{},show:!1}},watch:{src:{immediate:!0,handler(i){i?(this.isError=!1,this.loading=!0):this.isError=!0}}},computed:{transStyle(){let i={};return this.loading||this.isError||"100%"==this.width||"heightFix"!=this.mode?i.width=o.addUnit(this.width):i.width="fit-content",this.loading||this.isError||"100%"==this.height||"widthFix"!=this.mode?i.height=o.addUnit(this.height):i.height="fit-content",i},wrapStyle(){let i={};return this.loading||this.isError||"100%"==this.width||"heightFix"!=this.mode?i.width=o.addUnit(this.width):i.width="fit-content",this.loading||this.isError||"100%"==this.height||"widthFix"!=this.mode?i.height=o.addUnit(this.height):i.height="fit-content",i.borderRadius="circle"==this.shape?"10000px":o.addUnit(this.radius),i.overflow=this.radius>0?"hidden":"visible",o.deepMerge(i,o.addStyle(this.customStyle))}},mounted(){this.show=!0},emits:["click","error","load"],methods:{addUnit:o.addUnit,onClick(i){this.$emit("click",i)},onErrorHandler(i){this.loading=!1,this.isError=!0,this.$emit("error",i)},onLoadHandler(i){this.loading=!1,this.isError=!1,this.$emit("load",i),this.removeBgColor()},removeBgColor(){this.backgroundStyle={backgroundColor:this.bgColor||"#ffffff"}}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("u-transition"))()}Math||((()=>"../../../components/u-icon/u-icon.js")+(()=>"../u-transition/u-transition.js"))();const n=e._export_sfc(s,[["render",function(i,t,r,o,s,n){return e.e({a:!s.isError},s.isError?{}:{b:i.src,c:i.mode,d:e.o(((...i)=>n.onErrorHandler&&n.onErrorHandler(...i))),e:e.o(((...i)=>n.onLoadHandler&&n.onLoadHandler(...i))),f:i.showMenuByLongpress,g:i.lazyLoad,h:n.addUnit(i.width),i:n.addUnit(i.height),j:"circle"==i.shape?"10000px":n.addUnit(i.radius)},{k:i.showLoading&&s.loading},i.showLoading&&s.loading?{l:e.p({name:i.loadingIcon}),m:"circle"==i.shape?"50%":n.addUnit(i.radius),n:this.bgColor,o:n.addUnit(i.width),p:n.addUnit(i.height)}:{},{q:i.showError&&s.isError&&!s.loading},i.showError&&s.isError&&!s.loading?{r:e.p({name:i.errorIcon}),s:"circle"==i.shape?"50%":n.addUnit(i.radius)}:{},{t:e.o(((...i)=>n.onClick&&n.onClick(...i))),v:e.s(n.wrapStyle),w:e.s(s.backgroundStyle),x:e.s(n.transStyle),y:e.p({mode:"fade",show:s.show,duration:i.fade?1e3:0})})}],["__scopeId","data-v-f76eeac7"]]);wx.createComponent(n);
