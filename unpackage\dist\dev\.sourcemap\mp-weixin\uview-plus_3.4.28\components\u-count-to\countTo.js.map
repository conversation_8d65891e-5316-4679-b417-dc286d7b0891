{"version": 3, "file": "countTo.js", "sources": ["uview-plus_3.4.28/components/u-count-to/countTo.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:32\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/countTo.js\n */\nexport default {\n    // countTo 组件\n    countTo: {\n        startVal: 0,\n        endVal: 0,\n        duration: 2000,\n        autoplay: true,\n        decimals: 0,\n        useEasing: true,\n        decimal: '.',\n        color: '#606266',\n        fontSize: 22,\n        bold: false,\n        separator: ''\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,UAAA;AAAA;AAAA,EAEX,SAAS;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,WAAW;AAAA,EACd;AACL;;"}