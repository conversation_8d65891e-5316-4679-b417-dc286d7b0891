{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-badge/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否显示圆点\n        isDot: {\n            type: Boolean,\n            default: () => defProps.badge.isDot\n        },\n        // 显示的内容\n        value: {\n            type: [Number, String],\n            default: () => defProps.badge.value\n        },\n        // 显示的内容\n        modelValue: {\n            type: [Number, String],\n            default: () => defProps.badge.modelValue\n        },\n        // 是否显示\n        show: {\n            type: Boolean,\n            default: () => defProps.badge.show\n        },\n        // 最大值，超过最大值会显示 '{max}+'\n        max: {\n            type: [Number, String],\n            default: () => defProps.badge.max\n        },\n        // 主题类型，error|warning|success|primary\n        type: {\n            type: String,\n            default: () => defProps.badge.type\n        },\n        // 当数值为 0 时，是否展示 Badge\n        showZero: {\n            type: Boolean,\n            default: () => defProps.badge.showZero\n        },\n        // 背景颜色，优先级比type高，如设置，type参数会失效\n        bgColor: {\n            type: [String, null],\n            default: () => defProps.badge.bgColor\n        },\n        // 字体颜色\n        color: {\n            type: [String, null],\n            default: () => defProps.badge.color\n        },\n        // 徽标形状，circle-四角均为圆角，horn-左下角为直角\n        shape: {\n            type: String,\n            default: () => defProps.badge.shape\n        },\n        // 设置数字的显示方式，overflow|ellipsis|limit\n        // overflow会根据max字段判断，超出显示`${max}+`\n        // ellipsis会根据max判断，超出显示`${max}...`\n        // limit会依据1000作为判断条件，超出1000，显示`${value/1000}K`，比如2.2k、3.34w，最多保留2位小数\n        numberType: {\n            type: String,\n            default: () => defProps.badge.numberType\n        },\n        // 设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，absolute为true时有效\n        offset: {\n            type: Array,\n            default: () => defProps.badge.offset\n        },\n        // 是否反转背景和字体颜色\n        inverted: {\n            type: Boolean,\n            default: () => defProps.badge.inverted\n        },\n        // 是否绝对定位\n        absolute: {\n            type: Boolean,\n            default: () => defProps.badge.absolute\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA;AAAA;AAAA;AAAA,IAKD,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}