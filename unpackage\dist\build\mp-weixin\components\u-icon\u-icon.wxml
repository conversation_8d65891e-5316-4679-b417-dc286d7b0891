<view bindtap="{{s}}" class="{{['u-icon', 'data-v-d0cbf1c8', t]}}"><image wx:if="{{a}}" class="u-icon__img data-v-d0cbf1c8" src="{{b}}" mode="{{c}}" style="{{d + ';' + e}}"></image><text wx:else class="{{['u-icon__icon', 'data-v-d0cbf1c8', g]}}" style="{{h + ';' + i}}" hover-class="{{j}}">{{f}}</text><text wx:if="{{k}}" class="u-icon__label data-v-d0cbf1c8" style="{{'color:' + m + ';' + ('font-size:' + n) + ';' + ('margin-left:' + o) + ';' + ('margin-top:' + p) + ';' + ('margin-right:' + q) + ';' + ('margin-bottom:' + r)}}">{{l}}</text></view>