/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
/* 使用全局样式文件中的.common-container */
.search-box {
  margin-bottom: 30rpx;
}
.section-title {
  margin-bottom: 20rpx;
  padding: 16rpx 24rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-content {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.ad-section {
  margin-bottom: 30rpx;
  padding: 0 10rpx;
}
.swiper-section {
  margin-bottom: 30rpx;
}

/* 自定义轮播图样式 */
 .u-swiper__wrapper {
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
 .u-swiper-indicator__dot {
  width: 12rpx;
  height: 12rpx;
}
 .u-swiper__title {
  background-color: rgba(0, 0, 0, 0.4);
  padding: 16rpx;
  font-size: 28rpx;
}
.notice-section {
  margin-bottom: 30rpx;
}

/* 自定义NoticeBar样式 */
 .u-notice-bar {
  padding: 10rpx 0;
}
 .u-notice-bar__content__text {
  font-size: 28rpx;
  padding: 12rpx 24rpx;
  text-align: left;
}
.quick-entry {
  margin-bottom: 30rpx;
}
.entry-grid {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  margin: 4rpx;
  height: 110rpx;
}
.icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #ffeeee;
  margin-bottom: 6rpx;
}
.news-section {
  margin-bottom: 30rpx;
}

/* 由于使用了u-cell组件，不再需要news-item样式 */
.bottom-nav {
  background-color: #ffffff;
  border-radius: 0;
  padding: 6rpx 0;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 999;
  border-top: 1rpx solid #f5f5f5;
  height: 100rpx;
}
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 6rpx 0;
}