"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),o=require("../../libs/mixin/mixin.js"),t=require("../../libs/function/index.js"),n=require("../../libs/function/test.js"),c=require("../../../common/vendor.js"),r={mixins:[i.mpMixin,o.mixin,e.props],watch:{text:{immediate:!0,handler(e,i){n.test.array(e)}}},computed:{textStyle(){let e={};return e.color=this.color,e.fontSize=t.addUnit(this.fontSize),e},vertical(){return"horizontal"!=this.mode}},data:()=>({index:0}),emits:["click","close"],methods:{noticeChange(e){this.index=e.detail.current},clickH<PERSON><PERSON>(){this.$emit("click",this.index)},close(){this.$emit("close")}}};if(!Array){c.resolveComponent("u-icon")()}Math;const l=c._export_sfc(r,[["render",function(e,i,o,t,n,r){return c.e({a:e.icon},e.icon?{b:c.p({name:e.icon,color:e.color,size:"19"})}:{},{c:c.f(e.text,((e,i,o)=>({a:c.t(e),b:i}))),d:c.s(r.textStyle),e:e.justifyContent,f:e.disableTouch,g:!e.step,h:e.duration,i:c.o(((...e)=>r.noticeChange&&r.noticeChange(...e))),j:["link","closable"].includes(e.mode)},["link","closable"].includes(e.mode)?c.e({k:"link"===e.mode},"link"===e.mode?{l:c.p({name:"arrow-right",size:17,color:e.color})}:{},{m:"closable"===e.mode},"closable"===e.mode?{n:c.o(r.close),o:c.p({name:"close",size:16,color:e.color})}:{}):{},{p:c.o(((...e)=>r.clickHandler&&r.clickHandler(...e)))})}],["__scopeId","data-v-c431354b"]]);wx.createComponent(l);
