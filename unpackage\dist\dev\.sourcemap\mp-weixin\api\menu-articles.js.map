{"version": 3, "file": "menu-articles.js", "sources": ["api/menu-articles.js"], "sourcesContent": ["/**\n * 菜单文章相关API\n */\nimport requestApi from './request.js';\n\n/**\n * 获取指定菜单ID的文章列表\n * @param {number} menuId - 菜单ID\n * @returns {Promise<Object>} - 返回文章列表数据\n */\nexport const getMenuArticles = async (menuId) => {\n  try {\n    const result = await requestApi.get(`/api/menu-articles/menu/${menuId}`);\n    return result;\n  } catch (error) {\n    console.error('获取菜单文章列表失败:', error);\n    throw error;\n  }\n};\n\n/**\n * 获取菜单文章详情\n * @param {number} id - 文章ID\n * @returns {Promise<Object>} - 返回文章详情数据\n */\nexport const getArticleDetail = async (id) => {\n  try {\n    const result = await requestApi.get(`/api/menu-articles/${id}`);\n    return result;\n  } catch (error) {\n    console.error('获取菜单文章详情失败:', error);\n    throw error;\n  }\n};\n"], "names": ["requestApi", "uni"], "mappings": ";;;AAUY,MAAC,kBAAkB,OAAO,WAAW;AAC/C,MAAI;AACF,UAAM,SAAS,MAAMA,oBAAW,IAAI,2BAA2B,MAAM,EAAE;AACvE,WAAO;AAAA,EACR,SAAQ,OAAO;AACdC,kBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,KAAK;AAClC,UAAM;AAAA,EACP;AACH;AAOY,MAAC,mBAAmB,OAAO,OAAO;AAC5C,MAAI;AACF,UAAM,SAAS,MAAMD,oBAAW,IAAI,sBAAsB,EAAE,EAAE;AAC9D,WAAO;AAAA,EACR,SAAQ,OAAO;AACdC,kBAAc,MAAA,MAAA,SAAA,8BAAA,eAAe,KAAK;AAClC,UAAM;AAAA,EACP;AACH;;;"}