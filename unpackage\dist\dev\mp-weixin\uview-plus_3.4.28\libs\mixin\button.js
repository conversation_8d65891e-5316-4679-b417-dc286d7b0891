"use strict";
const uviewPlus_3_4_28_libs_vue = require("../vue.js");
const buttonMixin = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    lang: String,
    sessionFrom: String,
    sendMessageTitle: String,
    sendMessagePath: String,
    sendMessageImg: String,
    showMessageCard: Boolean,
    appParameter: String,
    formType: String,
    openType: String
  }
});
exports.buttonMixin = buttonMixin;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/libs/mixin/button.js.map
