{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-swiper/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 列表数组，元素可为字符串，如为对象可通过keyName指定目标属性名\n        list: {\n            type: Array,\n            default: () => defProps.swiper.list\n        },\n        // 是否显示面板指示器\n        indicator: {\n            type: Boolean,\n            default: () => defProps.swiper.indicator\n        },\n        // 指示器非激活颜色\n        indicatorActiveColor: {\n            type: String,\n            default: () => defProps.swiper.indicatorActiveColor\n        },\n        // 指示器的激活颜色\n        indicatorInactiveColor: {\n            type: String,\n            default: () => defProps.swiper.indicatorInactiveColor\n        },\n        // 指示器样式，可通过bottom，left，right进行定位\n        indicatorStyle: {\n            type: [String, Object],\n            default: () => defProps.swiper.indicatorStyle\n        },\n        // 指示器模式，line-线型，dot-点型\n        indicatorMode: {\n            type: String,\n            default: () => defProps.swiper.indicatorMode\n        },\n        // 是否自动切换\n        autoplay: {\n            type: Boolean,\n            default: () => defProps.swiper.autoplay\n        },\n        // 当前所在滑块的 index\n        current: {\n            type: [String, Number],\n            default: () => defProps.swiper.current\n        },\n        // 当前所在滑块的 item-id ，不能与 current 被同时指定\n        currentItemId: {\n            type: String,\n            default: () => defProps.swiper.currentItemId\n        },\n        // 滑块自动切换时间间隔\n        interval: {\n            type: [String, Number],\n            default: () => defProps.swiper.interval\n        },\n        // 滑块切换过程所需时间\n        duration: {\n            type: [String, Number],\n            default: () => defProps.swiper.duration\n        },\n        // 播放到末尾后是否重新回到开头\n        circular: {\n            type: Boolean,\n            default: () => defProps.swiper.circular\n        },\n        // 前边距，可用于露出前一项的一小部分，nvue和支付宝不支持\n        previousMargin: {\n            type: [String, Number],\n            default: () => defProps.swiper.previousMargin\n        },\n        // 后边距，可用于露出后一项的一小部分，nvue和支付宝不支持\n        nextMargin: {\n            type: [String, Number],\n            default: () => defProps.swiper.nextMargin\n        },\n        // 当开启时，会根据滑动速度，连续滑动多屏，支付宝不支持\n        acceleration: {\n            type: Boolean,\n            default: () => defProps.swiper.acceleration\n        },\n        // 同时显示的滑块数量，nvue、支付宝小程序不支持\n        displayMultipleItems: {\n            type: Number,\n            default: () => defProps.swiper.displayMultipleItems\n        },\n        // 指定swiper切换缓动动画类型，有效值：default、linear、easeInCubic、easeOutCubic、easeInOutCubic\n        // 只对微信小程序有效\n        easingFunction: {\n            type: String,\n            default: () => defProps.swiper.easingFunction\n        },\n        // list数组中指定对象的目标属性名\n        keyName: {\n            type: String,\n            default: () => defProps.swiper.keyName\n        },\n        // 图片的裁剪模式\n        imgMode: {\n            type: String,\n            default: () => defProps.swiper.imgMode\n        },\n        // 组件高度\n        height: {\n            type: [String, Number],\n            default: () => defProps.swiper.height\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: () => defProps.swiper.bgColor\n        },\n        // 组件圆角，数值或带单位的字符串\n        radius: {\n            type: [String, Number],\n            default: () => defProps.swiper.radius\n        },\n        // 是否加载中\n        loading: {\n            type: Boolean,\n            default: () => defProps.swiper.loading\n        },\n        // 是否显示标题，要求数组对象中有title属性\n        showTitle: {\n            type: Boolean,\n            default: () => defProps.swiper.showTitle\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACpB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}