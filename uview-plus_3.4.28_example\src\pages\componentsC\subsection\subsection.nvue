<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<up-subsection
					:list="list"
					mode="subsection"
					:current="current1"
					@change="change1"
				></up-subsection>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮模式</text>
			<view class="u-demo-block__content">
				<up-subsection
					:list="list"
					mode="button"
					:current="current2"
					@change="change2"
				></up-subsection>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">更换主题</text>
			<view class="u-demo-block__content">
				<up-subsection
					:list="list"
					mode="subsection"
					:current="current3"
					activeColor="#f56c6c"
					@change="change3"
				></up-subsection>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">默认位置</text>
			<view class="u-demo-block__content">
				<up-subsection
					:list="list"
					mode="button"
					:current="current4"
					activeColor="#f9ae3d"
					@change="change4"
				></up-subsection>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				current1: 0,
				current2: 0,
				current3: 0,
				current4: 1,
				list: ['未付款', '待评价', '已付款'],
				// 或者如下，也可以配置keyName参数修改对象键值
				// list: [{name: '未付款'}, {name: '待评价'}, {name: '已付款'}],
				current: 1
			}
		},
		methods: {
			change1(index) {
				this.current1 = index
			},
			change2(index) {
				this.current2 = index
			},
			change3(index) {
				this.current3 = index
			},
			change4(index) {
				this.current4 = index
			}
		},
	}
</script>

<style lang="scss">
	.album {
		@include flex;
		align-items: flex-start;

		&__avatar {
			background-color: $u-bg-color;
			padding: 5px;
			border-radius: 3px;
		}

		&__content {
			margin-left: 10px;
			flex: 1;
		}
	}
</style>
