"use strict";
const uviewPlus_3_4_28_libs_vue = require("../../libs/vue.js");
const uviewPlus_3_4_28_libs_config_props = require("../../libs/config/props.js");
const props = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    // 是否展示组件
    show: {
      type: <PERSON><PERSON><PERSON>,
      default: () => uviewPlus_3_4_28_libs_config_props.props.transition.show
    },
    // 使用的动画模式
    mode: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.transition.mode
    },
    // 动画的执行时间，单位ms
    duration: {
      type: [String, Number],
      default: () => uviewPlus_3_4_28_libs_config_props.props.transition.duration
    },
    // 使用的动画过渡函数
    timingFunction: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.transition.timingFunction
    }
  }
});
exports.props = props;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-transition/props.js.map
