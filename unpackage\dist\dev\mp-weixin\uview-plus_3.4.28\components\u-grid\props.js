"use strict";
const uviewPlus_3_4_28_libs_vue = require("../../libs/vue.js");
const uviewPlus_3_4_28_libs_config_props = require("../../libs/config/props.js");
const props = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    // 分成几列
    col: {
      type: [String, Number],
      default: () => uviewPlus_3_4_28_libs_config_props.props.grid.col
    },
    // 是否显示边框
    border: {
      type: <PERSON><PERSON><PERSON>,
      default: () => uviewPlus_3_4_28_libs_config_props.props.grid.border
    },
    // 宫格对齐方式，表现为数量少的时候，靠左，居中，还是靠右
    align: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.grid.align
    },
    // 间隔
    gap: {
      type: String,
      default: "0px"
    }
  }
});
exports.props = props;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-grid/props.js.map
