{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-list-item/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 用于滚动到指定item\n        anchor: {\n            type: [String, Number],\n            default: () => defProps.listItem.anchor\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,yCAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}