import config from '@/utils/config.js';

/**
 * 封装uni.request网络请求
 * @param {Object} options - 请求配置
 * @returns {Promise} - 返回Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      url: config.apiBaseUrl + options.url,
      data: options.data || options.params,
      method: options.method || 'GET',
      header: {
        'content-type': 'application/json;charset=utf-8',
        ...options.header
      },
      timeout: 10000, // 请求超时时间，单位ms
      dataType: 'json',
      success: (res) => {
        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 请求成功但状态码异常
          console.error('请求异常:', res);
          reject(res);
        }
      },
      fail: (err) => {
        // 请求失败
        console.error('请求失败:', err);
        reject(err);
      }
    };

    // 发起请求
    uni.request(requestOptions);
  });
};

/**
 * GET请求
 * @param {String} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回Promise对象
 */
const get = (url, params = {}, header = {}) => {
  return request({
    url,
    params,
    method: 'GET',
    header
  });
};

/**
 * POST请求
 * @param {String} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回Promise对象
 */
const post = (url, data = {}, header = {}) => {
  return request({
    url,
    data,
    method: 'POST',
    header
  });
};

export default {
  request,
  get,
  post
};
