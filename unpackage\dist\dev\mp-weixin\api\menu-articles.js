"use strict";
const common_vendor = require("../common/vendor.js");
const api_request = require("./request.js");
const getMenuArticles = async (menuId) => {
  try {
    const result = await api_request.request.get(`/api/menu-articles/menu/${menuId}`);
    return result;
  } catch (error) {
    common_vendor.index.__f__("error", "at api/menu-articles.js:16", "获取菜单文章列表失败:", error);
    throw error;
  }
};
const getArticleDetail = async (id) => {
  try {
    const result = await api_request.request.get(`/api/menu-articles/${id}`);
    return result;
  } catch (error) {
    common_vendor.index.__f__("error", "at api/menu-articles.js:31", "获取菜单文章详情失败:", error);
    throw error;
  }
};
exports.getArticleDetail = getArticleDetail;
exports.getMenuArticles = getMenuArticles;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/menu-articles.js.map
