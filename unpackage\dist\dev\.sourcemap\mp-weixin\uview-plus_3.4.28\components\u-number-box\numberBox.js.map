{"version": 3, "file": "numberBox.js", "sources": ["uview-plus_3.4.28/components/u-number-box/numberBox.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:11:46\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/numberBox.js\n */\nexport default {\n    // 步进器组件\n    numberBox: {\n        name: '',\n        value: 0,\n        min: 1,\n        max: Number.MAX_SAFE_INTEGER,\n        step: 1,\n        integer: false,\n        disabled: false,\n        disabledInput: false,\n        asyncChange: false,\n        inputWidth: 35,\n        showMinus: true,\n        showPlus: true,\n        decimalLength: null,\n        longPress: true,\n        color: '#323233',\n        buttonWidth: 30,\n        buttonSize: 30,\n        buttonRadius: '0px',\n        bgColor: '#EBECEE',\n        inputBgColor: '#EBECEE',\n        cursorSpacing: 100,\n        disableMinus: false,\n        disablePlus: false,\n        iconStyle: '',\n        miniMode: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,KAAK,OAAO;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,OAAO;AAAA,IACP,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,SAAS;AAAA,IACT,cAAc;AAAA,IACd,eAAe;AAAA,IACf,cAAc;AAAA,IACd,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,EACb;AACL;;"}