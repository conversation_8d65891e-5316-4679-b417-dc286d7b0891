"use strict";const e=require("../../../common/vendor.js"),t=require("../function/index.js");const i=(new class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(e){return"/"===e[0]?e:`/${e}`}mixinParam(e,i){e=e&&this.addRootPath(e);let a="";return/.*\/.*\?.*=.*/.test(e)?(a=t.queryParams(i,!1),e+`&${a}`):(a=t.queryParams(i),e+a)}async route(i={},a={}){let n={};if("string"==typeof i?(n.url=this.mixinParam(i,a),n.type="navigateTo"):(n=t.deepMerge(this.config,i),n.url=this.mixinParam(i.url,i.params)),n.url!==t.page())if(a.intercept&&(this.config.intercept=a.intercept),n.params=a,n=t.deepMerge(this.config,n),"function"==typeof e.index.$u.routeIntercept){await new Promise(((t,i)=>{e.index.$u.routeIntercept(n,t)}))&&this.openPage(n)}else this.openPage(n)}openPage(t){const{url:i,type:a,delta:n,animationType:r,animationDuration:o}=t;"navigateTo"!=t.type&&"to"!=t.type||e.index.navigateTo({url:i,animationType:r,animationDuration:o}),"redirectTo"!=t.type&&"redirect"!=t.type||e.index.redirectTo({url:i}),"switchTab"!=t.type&&"tab"!=t.type||e.index.switchTab({url:i}),"reLaunch"!=t.type&&"launch"!=t.type||e.index.reLaunch({url:i}),"navigateBack"!=t.type&&"back"!=t.type||e.index.navigateBack({delta:n})}}).route;exports.route=i;
