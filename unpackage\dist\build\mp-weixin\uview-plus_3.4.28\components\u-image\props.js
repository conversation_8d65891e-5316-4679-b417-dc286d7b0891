"use strict";const e=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),p=e.defineMixin({props:{src:{type:String,default:()=>o.props.image.src},mode:{type:String,default:()=>o.props.image.mode},width:{type:[String,Number],default:()=>o.props.image.width},height:{type:[String,Number],default:()=>o.props.image.height},shape:{type:String,default:()=>o.props.image.shape},radius:{type:[String,Number],default:()=>o.props.image.radius},lazyLoad:{type:<PERSON>olean,default:()=>o.props.image.lazyLoad},showMenuByLongpress:{type:Boolean,default:()=>o.props.image.showMenuByLongpress},loadingIcon:{type:String,default:()=>o.props.image.loadingIcon},errorIcon:{type:String,default:()=>o.props.image.errorIcon},showLoading:{type:Boolean,default:()=>o.props.image.showLoading},showError:{type:Boolean,default:()=>o.props.image.showError},fade:{type:Boolean,default:()=>o.props.image.fade},webp:{type:Boolean,default:()=>o.props.image.webp},duration:{type:[String,Number],default:()=>o.props.image.duration},bgColor:{type:String,default:()=>o.props.image.bgColor}}});exports.props=p;
