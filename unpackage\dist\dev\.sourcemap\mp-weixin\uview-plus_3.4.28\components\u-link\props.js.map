{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-link/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 文字颜色\n        color: {\n            type: String,\n            default: () => defProps.link.color\n        },\n        // 字体大小，单位px\n        fontSize: {\n            type: [String, Number],\n            default: () => defProps.link.fontSize\n        },\n        // 是否显示下划线\n        underLine: {\n            type: Boolean,\n            default: () => defProps.link.underLine\n        },\n        // 要跳转的链接\n        href: {\n            type: String,\n            default: () => defProps.link.href\n        },\n        // 小程序中复制到粘贴板的提示语\n        mpTips: {\n            type: String,\n            default: () => defProps.link.mpTips\n        },\n        // 下划线颜色\n        lineColor: {\n            type: String,\n            default: () => defProps.link.lineColor\n        },\n        // 超链接的问题，不使用slot形式传入，是因为nvue下无法修改颜色\n        text: {\n            type: String,\n            default: () => defProps.link.text\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}