{"version": 3, "file": "skeleton.js", "sources": ["uview-plus_3.4.28/components/u-skeleton/skeleton.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:20:14\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/skeleton.js\n */\nexport default {\n    // skeleton\n    skeleton: {\n        loading: true,\n        animate: true,\n        rows: 0,\n        rowsWidth: '100%',\n        rowsHeight: 18,\n        title: true,\n        titleWidth: '50%',\n        titleHeight: 18,\n        avatar: false,\n        avatarSize: 32,\n        avatarShape: 'circle'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,EAChB;AACL;;"}