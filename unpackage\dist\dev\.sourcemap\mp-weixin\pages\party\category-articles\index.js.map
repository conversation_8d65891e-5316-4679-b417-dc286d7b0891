{"version": 3, "file": "index.js", "sources": ["pages/party/category-articles/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcGFydHkvY2F0ZWdvcnktYXJ0aWNsZXMvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"common-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar :title=\"category.name || '文章列表'\" :showBack=\"true\" :showHome=\"true\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 文章列表内容 -->\n\t\t<view class=\"articles-content\">\n\t\t\t<view class=\"content-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<u-icon name=\"list\" color=\"#D9001B\" size=\"20\"></u-icon>\n\t\t\t\t\t<text>文章列表</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"section-body\">\n\t\t\t\t\t<u-list :enable-flex=\"true\">\n\t\t\t\t\t\t<u-cell v-for=\"(item, index) in articleList\" :key=\"index\" :title=\"item.title\"\n\t\t\t\t\t\t\t:titleStyle=\"{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}\"\n\t\t\t\t\t\t\tisLink @click=\"onArticleClick(item)\">\n\t\t\t\t\t\t</u-cell>\n\t\t\t\t\t</u-list>\n\n\t\t\t\t\t<!-- 空状态 -->\n\t\t\t\t\t<u-empty v-if=\"articleList.length === 0\" mode=\"list\" text=\"暂无文章\"></u-empty>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"category-footer\">\n\t\t\t<u-button type=\"primary\" text=\"返回上一页\" @click=\"() => uni.navigateBack()\" :customStyle=\"{backgroundColor: '#D9001B'}\"></u-button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { categoryApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcategoryId: null,\n\t\t\t\tcategory: {},\n\t\t\t\tarticleList: []\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取路由参数中的分类ID\n\t\t\tif (options.id) {\n\t\t\t\tthis.categoryId = options.id;\n\t\t\t\tconsole.log('分类ID:', this.categoryId);\n\t\t\t\t// 获取分类文章列表\n\t\t\t\tthis.fetchCategoryArticles();\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '参数错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.navigateBack();\n\t\t\t\t}, 1500);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取分类文章列表\n\t\t\tasync fetchCategoryArticles() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\tconst result = await categoryApi.getCategoryArticles(this.categoryId);\n\t\t\t\t\tconsole.log('获取分类文章列表成功:', result);\n\n\t\t\t\t\tif (result && result.success) {\n\t\t\t\t\t\t// 设置分类信息\n\t\t\t\t\t\tif (result.data.category) {\n\t\t\t\t\t\t\tthis.category = result.data.categoryId;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 设置文章列表\n\t\t\t\t\t\tif (Array.isArray(result.data)) {\n\t\t\t\t\t\t\tthis.articleList = result.data;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取分类文章列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取文章列表失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 文章点击事件\n\t\t\tonArticleClick(item) {\n\t\t\t\tconsole.log('点击了文章:', item);\n\n\t\t\t\t// 党建版块下所有文章都是党建文章，直接跳转到统一的文章详情页面\n\t\t\t\tconsole.log('跳转到党建文章详情页');\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/article/detail?id=${item.id}&type=party`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 返回上一页方法已由导航栏组件自动处理\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t.articles-content {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.section-body {\n\t\tpadding: 10rpx 0;\n\t}\n\n\t/* 使用全局样式文件中的.category-footer */\n</style>\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/party/category-articles/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "categoryApi.getCategoryArticles"], "mappings": ";;;AAyCC,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,MACZ,UAAU,CAAE;AAAA,MACZ,aAAa,CAAC;AAAA,IACf;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AAEf,QAAI,QAAQ,IAAI;AACf,WAAK,aAAa,QAAQ;AAC1BA,oBAAA,MAAA,MAAA,OAAA,iDAAY,SAAS,KAAK,UAAU;AAEpC,WAAK,sBAAqB;AAAA,WACpB;AACNA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AACD,iBAAW,MAAM;AAChBA,sBAAG,MAAC,aAAY;AAAA,MAChB,GAAE,IAAI;AAAA,IACR;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,wBAAwB;AAC7B,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMC,eAAAA,oBAAgC,KAAK,UAAU;AACpED,sBAAA,MAAA,MAAA,OAAA,iDAAY,eAAe,MAAM;AAEjC,YAAI,UAAU,OAAO,SAAS;AAE7B,cAAI,OAAO,KAAK,UAAU;AACzB,iBAAK,WAAW,OAAO,KAAK;AAAA,UAC7B;AAGA,cAAI,MAAM,QAAQ,OAAO,IAAI,GAAG;AAC/B,iBAAK,cAAc,OAAO;AAAA,UAC3B;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,iDAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAED,eAAe,MAAM;AACpBA,yFAAY,UAAU,IAAI;AAG1BA,oBAAAA,MAAY,MAAA,OAAA,kDAAA,YAAY;AACxBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,KAAK,EAAE;AAAA,QACxC,MAAM,CAAC,QAAQ;AACdA,wBAAA,MAAA,MAAA,SAAA,kDAAc,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,EAEF;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzHD,GAAG,WAAW,eAAe;"}