{"version": 3, "file": "calendar.js", "sources": ["uview-plus_3.4.28/components/u-calendar/calendar.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 16:52:43\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/calendar.js\r\n */\r\nexport default {\r\n    // calendar 组件\r\n    calendar: {\r\n        title: '日期选择',\r\n        showTitle: true,\r\n        showSubtitle: true,\r\n        mode: 'single',\r\n        startText: '开始',\r\n        endText: '结束',\r\n        customList: [],\r\n        color: '#3c9cff',\r\n        minDate: 0,\r\n        maxDate: 0,\r\n        defaultDate: null,\r\n        maxCount: Number.MAX_SAFE_INTEGER, // Infinity\r\n        rowHeight: 56,\r\n        formatter: null,\r\n        showLunar: false,\r\n        showMark: true,\r\n        confirmText: '确定',\r\n        confirmDisabledText: '确定',\r\n        show: false,\r\n        closeOnClickOverlay: false,\r\n        readonly: false,\r\n        showConfirm: true,\r\n        maxRange: Number.MAX_SAFE_INTEGER, // Infinity\r\n        rangePrompt: '',\r\n        showRangePrompt: true,\r\n        allowSameDay: false,\r\n\t\tround: 0,\r\n\t\tmonthNum: 3,\r\n        weekText: ['一', '二', '三', '四', '五', '六', '日'],\r\n        forbidDays: [],\r\n        forbidDaysToast: '该日期已禁用',\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,OAAO;AAAA,IACP,WAAW;AAAA,IACX,cAAc;AAAA,IACd,MAAM;AAAA,IACN,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY,CAAE;AAAA,IACd,OAAO;AAAA,IACP,SAAS;AAAA,IACT,SAAS;AAAA,IACT,aAAa;AAAA,IACb,UAAU,OAAO;AAAA;AAAA,IACjB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,WAAW;AAAA,IACX,UAAU;AAAA,IACV,aAAa;AAAA,IACb,qBAAqB;AAAA,IACrB,MAAM;AAAA,IACN,qBAAqB;AAAA,IACrB,UAAU;AAAA,IACV,aAAa;AAAA,IACb,UAAU,OAAO;AAAA;AAAA,IACjB,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACpB,OAAO;AAAA,IACP,UAAU;AAAA,IACJ,UAAU,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAAA,IAC5C,YAAY,CAAE;AAAA,IACd,iBAAiB;AAAA,EACpB;AACL;;"}