{"version": 3, "file": "transition.js", "sources": ["uview-plus_3.4.28/components/u-transition/transition.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:59:00\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/transition.js\n */\nexport default {\n    // transition动画组件的props\n    transition: {\n        show: false,\n        mode: 'fade',\n        duration: '300',\n        timingFunction: 'ease-out'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,gBAAgB;AAAA,EACnB;AACL;;"}