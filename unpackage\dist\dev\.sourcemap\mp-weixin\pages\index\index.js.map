{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"common-container red-theme\">\r\n\t\t<!-- 自定义导航栏 -->\r\n\t\t<custom-navbar title=\"红色环投\" :showBack=\"false\" :showHome=\"false\">\r\n\t\t\t<template #right>\r\n\t\t\t\t<view class=\"navbar-right\">\r\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\r\n\t\t\t\t\t<u-icon name=\"camera-fill\" color=\"#FFFFFF\" size=\"20\" style=\"margin-left: 15px;\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</custom-navbar>\r\n\r\n\t\t<!-- 搜索框 -->\r\n\t\t<view class=\"search-box\">\r\n\t\t\t<u-search placeholder=\"搜索\" placeholderColor=\"#000000\" :disabled=\"true\" @search=\"onSearchClick\" shape=\"round\"></u-search>\r\n\t\t</view>\r\n\r\n\t\t<!-- 广告图 -->\r\n\t<view class=\"ad-section\">\r\n\t\t<u-image\r\n\t\t\t:src=\"adImageUrl\"\r\n\t\t\twidth=\"100%\"\r\n\t\t\theight=\"120\"\r\n\t\t\tmode=\"widthFix\"\r\n\t\t\t:fade=\"true\"\r\n\t\t\t:borderRadius=\"8\"\r\n\t\t\t@click=\"onAdClick\"\r\n\t\t></u-image>\r\n\t</view>\r\n\r\n\t\t<!-- 新闻动态轮播图 -->\r\n\t\t<view class=\"swiper-section\">\r\n\t\t\t<u-swiper\r\n\t\t\t\t:list=\"swiperList\"\r\n\t\t\t\theight=\"300\"\r\n\t\t\t\tradius=\"8\"\r\n\t\t\t\t:autoplay=\"true\"\r\n\t\t\t\t:interval=\"3000\"\r\n\t\t\t\t:indicator=\"true\"\r\n\t\t\t\tindicatorMode=\"dot\"\r\n\t\t\t\tkeyName=\"src\"\r\n\t\t\t\tshowTitle\r\n\t\t\t\t@click=\"onSwiperClick\"\r\n\t\t\t></u-swiper>\r\n\t\t</view>\r\n\r\n\t\t<!-- 公告通知 -->\r\n\t\t<view class=\"notice-section\">\r\n\t\t\t<view class=\"section-content\">\r\n\t\t\t\t<u-notice-bar\r\n\t\t\t\t\t:text=\"noticeList.map(item => item.title)\"\r\n\t\t\t\t\tdirection=\"column\"\r\n\t\t\t\t\t:duration=\"3000\"\r\n\t\t\t\t\t:speed=\"80\"\r\n\t\t\t\t\tcolor=\"#333333\"\r\n\t\t\t\t\tbgColor=\"#ffffff\"\r\n\t\t\t\t\tjustifyContent=\"flex-start\"\r\n\t\t\t\t\t@click=\"onNoticeClick\"\r\n\t\t\t\t></u-notice-bar>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 快捷入口 -->\r\n\t\t<view class=\"quick-entry\">\r\n\t\t\t<view class=\"entry-grid\">\r\n\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t<u-text text=\"快捷入口\" size=\"16\" bold color=\"#333333\"></u-text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-grid :col=\"4\" :border=\"false\" @click=\"onGridClick\">\r\n\t\t\t\t\t<u-grid-item v-for=\"(item, index) in quickEntryList\" :key=\"index\" :index=\"index\">\r\n\t\t\t\t\t\t<view class=\"grid-item\">\r\n\t\t\t\t\t\t\t<view class=\"icon-circle\">\r\n\t\t\t\t\t\t\t\t<u-icon :name=\"item.icon || 'star-fill'\" size=\"36\" color=\"#E51C23\"></u-icon>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<u-text :text=\"item.title\" align=\"center\" size=\"16\" margin-top=\"4\"></u-text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</u-grid-item>\r\n\t\t\t\t</u-grid>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 党建要闻 -->\r\n\t\t<view class=\"news-section\">\r\n\t\t\t<view class=\"section-content\">\r\n\t\t\t\t<view class=\"section-title\">\r\n\t\t\t\t\t<u-text text=\"党建信息\" size=\"16\" bold color=\"#333333\"></u-text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-list :enable-flex=\"true\" height=\"500px\">\r\n\t\t\t\t\t<u-cell v-for=\"(item, index) in newsList\" :key=\"index\" :title=\"item.title\" :titleStyle=\"{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}\" isLink @click=\"onNewsClick(item)\">\r\n\t\t\t\t\t</u-cell>\r\n\t\t\t\t</u-list>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 底部导航 -->\r\n\t\t<view class=\"bottom-nav\">\r\n\t\t\t<u-grid :col=\"5\" :border=\"false\" @click=\"onNavClick\">\r\n\t\t\t\t<u-grid-item v-for=\"(item, index) in bottomNavList\" :key=\"index\" :index=\"index\">\r\n\t\t\t\t\t<view class=\"nav-item\">\r\n\t\t\t\t\t\t<u-icon :name=\"item.icon || 'home'\" size=\"24\" color=\"#FF0000\"></u-icon>\r\n\t\t\t\t\t\t<u-text :text=\"item.name\" align=\"center\" size=\"14\" margin-top=\"4\"></u-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</u-grid-item>\r\n\t\t\t</u-grid>\r\n\t\t</view>\r\n\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { articleApi, testGetArticleList, menuApi } from '@/api/index.js';\r\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\r\n\timport { fetchImageById, getFullImageUrl } from '@/utils/image.js';\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tCustomNavbar\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 轮播图数据\r\n\t\t\t\tswiperList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: '/static/images/1.png',\r\n\t\t\t\t\t\ttitle: '学习贯彻习近平新时代中国特色社会主义思想'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: '/static/images/2.png',\r\n\t\t\t\t\t\ttitle: '深入学习贯彻党的二十大精神'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tsrc: '/static/images/3.png',\r\n\t\t\t\t\t\ttitle: '喜迎建党102周年系列活动'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 公告通知数据\r\n\t\t\t\tnoticeList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '关于开展2023年度党员民主评议工作的通知'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '关于召开2023年度组织生活会的通知'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\ttitle: '关于开展党史学习教育的通知'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 快捷入口数据\r\n\t\t\t\tquickEntryList: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '党建工作',\r\n\t\t\t\t\t\ticon: 'home-fill',\r\n\t\t\t\t\t\turl: '/pages/party/index'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '基层动态',\r\n\t\t\t\t\t\ticon: 'bell-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\ttitle: '党建品牌',\r\n\t\t\t\t\t\ticon: 'star-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\ttitle: '红旗支部',\r\n\t\t\t\t\t\ticon: 'heart-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 5,\r\n\t\t\t\t\t\ttitle: '先锋党员',\r\n\t\t\t\t\t\ticon: 'account-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 6,\r\n\t\t\t\t\t\ttitle: '学习资料',\r\n\t\t\t\t\t\ticon: 'bookmark-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 7,\r\n\t\t\t\t\t\ttitle: '志愿服务',\r\n\t\t\t\t\t\ticon: 'gift-fill',\r\n\t\t\t\t\t\turl: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 8,\r\n\t\t\t\t\t\ttitle: '党费交纳',\r\n\t\t\t\t\t\ticon: 'rmb-circle-fill',\r\n\t\t\t\t\t\turl: '',\r\n\t\t\t\t\t\tappId: 'wxc324e5e2fb50b1c6' // 党费通小程序的appId\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t// 党建要闻数据\r\n\t\t\t\tnewsList: [],\r\n\t\t\t\t// 底部导航数据\r\n\t\t\t\tbottomNavList: [],\r\n\t\t\t\t// 菜单图标映射\r\n\t\t\t\tmenuIconMap: {\r\n\t\t\t\t\t'党建信息': 'home-fill',\r\n\t\t\t\t\t'阵地建设': 'star-fill',\r\n\t\t\t\t\t'学习园地': 'bookmark-fill',\r\n\t\t\t\t\t'示范典型': 'heart-fill',\r\n\t\t\t\t\t'群团工作': 'account-fill'\r\n\t\t\t\t},\r\n\t\t\t\t// 广告图数据\r\n\t\t\t\tadImage: null,\r\n\t\t\t\t// 广告图URL\r\n\t\t\t\tadImageUrl: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时的逻辑\r\n\t\t\tconsole.log('开始测试API请求...');\r\n\r\n\t\t\t// 方法1：直接调用测试函数\r\n\t\t\ttestGetArticleList();\r\n\r\n\t\t\t// 方法2：使用API函数获取文章列表\r\n\t\t\tthis.fetchArticleList();\r\n\r\n\t\t\t// 获取菜单数据\r\n\t\t\tthis.fetchMenuList();\r\n\r\n\t\t\t// 获取广告图片数据\r\n\t\t\tthis.fetchAdImage();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取文章列表\r\n\t\t\tasync fetchArticleList() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst result = await articleApi.getArticleList();\r\n\t\t\t\t\tconsole.log('获取文章列表成功:', result);\r\n\t\t\t\t\t// 将API返回的数据赋值给页面上的变量，替换模拟数据\r\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\r\n\t\t\t\t\t\tthis.newsList = result.data;\r\n\t\t\t\t\t\tconsole.log('文章列表数据已更新');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取文章列表失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 轮播图点击事件\r\n\t\t\tonSwiperClick(index) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '点击了轮播图：' + this.swiperList[index].title,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 搜索点击事件\r\n\t\t\tonSearchClick(e) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '搜索内容：' + (e.value || ''),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 公告点击事件\r\n\t\t\tonNoticeClick(index) {\r\n\t\t\t\tconst item = this.noticeList[index];\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '点击了公告：' + item.title,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 快捷入口点击事件\r\n\t\t\tonGridClick(index) {\r\n\t\t\t\tconst item = this.quickEntryList[index]\r\n\t\t\t\tconsole.log('点击了快捷入口:', item);\r\n\r\n\t\t\t\t// 如果有配置appId，则跳转到小程序\r\n\t\t\t\tif (item.appId) {\r\n\t\t\t\t\tconsole.log('跳转到小程序，appId:', item.appId);\r\n\t\t\t\t\tthis.navigateToMiniProgram(item);\r\n\t\t\t\t} else if (item.url) {\r\n\t\t\t\t\t// 如果有配置URL，则跳转到对应页面\r\n\t\t\t\t\tconsole.log('跳转到URL:', item.url);\r\n\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: item.url,\r\n\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 没有配置URL或appId的入口项显示提示\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '暂未配置：' + item.title,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 跳转到小程序\r\n\t\t\tnavigateToMiniProgram(item) {\r\n\t\t\t\tconsole.log('准备跳转到小程序:', item.title, 'appId:', item.appId);\r\n\r\n\t\t\t\tuni.navigateToMiniProgram({\r\n\t\t\t\t\tappId: item.appId,\r\n\t\t\t\t\tpath: '', // 打开小程序首页，可以根据需要指定具体页面\r\n\t\t\t\t\textraData: {\r\n\t\t\t\t\t\t// 可以传递一些数据给目标小程序\r\n\t\t\t\t\t\tsource: '红色环投',\r\n\t\t\t\t\t\tfrom: 'quickEntry'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tenvVersion: 'release', // 正式版\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\tconsole.log('跳转小程序成功:', res);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\tconsole.error('跳转小程序失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '无法打开' + item.title,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 新闻点击事件\r\n\t\t\tonNewsClick(item) {\r\n\t\t\t\tconsole.log('点击了文章:', item);\r\n\t\t\t\t// 跳转到统一的文章详情页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/article/detail?id=${item.id}&type=news`,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取菜单列表\r\n\t\t\tasync fetchMenuList() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst result = await menuApi.getMenuList();\r\n\t\t\t\t\tconsole.log('获取菜单列表成功:', result);\r\n\r\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\r\n\t\t\t\t\t\t// 过滤出一级菜单作为底部导航\r\n\t\t\t\t\t\tconst firstLevelMenus = result.data.filter(item => item.level === 1);\r\n\r\n\t\t\t\t\t\t// 为每个菜单项添加图标\r\n\t\t\t\t\t\tthis.bottomNavList = firstLevelMenus.map(item => {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t...item,\r\n\t\t\t\t\t\t\t\ticon: this.menuIconMap[item.name] || 'home-fill'\r\n\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tconsole.log('底部导航菜单数据已更新:', this.bottomNavList);\r\n\r\n\t\t\t\t\t\t// 处理快捷入口的URL\r\n\t\t\t\t\t\t// 获取所有二级菜单\r\n\t\t\t\t\t\tconst secondLevelMenus = result.data.filter(item => item.level === 2);\r\n\r\n\t\t\t\t\t\t// 遍历快捷入口列表\r\n\t\t\t\t\t\tthis.quickEntryList.forEach((quickEntry, index) => {\r\n\t\t\t\t\t\t\t// 特殊处理\"党建工作\"，保持原有URL\r\n\t\t\t\t\t\t\t// if (quickEntry.title === \"党建工作\") {\r\n\t\t\t\t\t\t\t// \treturn;\r\n\t\t\t\t\t\t\t// }\r\n\r\n\t\t\t\t\t\t\t// 查找与快捷入口标题匹配的二级菜单\r\n\t\t\t\t\t\t\tconst matchedMenu = secondLevelMenus.find(menu => menu.name === quickEntry.title);\r\n\r\n\t\t\t\t\t\t\tif (matchedMenu) {\r\n\t\t\t\t\t\t\t\t// 找到匹配的二级菜单，设置URL，包含一级菜单ID和二级菜单ID\r\n\t\t\t\t\t\t\t\tconst parentMenu = firstLevelMenus.find(menu => menu.id === matchedMenu.parentId);\r\n\r\n\t\t\t\t\t\t\t\tif (parentMenu) {\r\n\t\t\t\t\t\t\t\t\t// 设置URL，格式为：/pages/menus/index?id=一级菜单ID&name=一级菜单名称&subMenuId=二级菜单ID\r\n\t\t\t\t\t\t\t\t\tthis.quickEntryList[index].url = `/pages/menus/index?id=${parentMenu.id}&name=${encodeURIComponent(parentMenu.name)}&subMenuId=${matchedMenu.id}`;\r\n\t\t\t\t\t\t\t\t\tconsole.log(`已设置快捷入口 \"${quickEntry.title}\" 的URL:`, this.quickEntryList[index].url);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 如果没有找到匹配的二级菜单，保持原有URL不变\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取菜单列表失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\r\n\r\n\r\n\r\n\t\t\t// 广告图点击事件\r\n\t\t\tonAdClick() {\r\n\t\t\t\tconsole.log('点击了广告图');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '点击了广告图',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 底部导航点击事件\r\n\t\t\tonNavClick(index) {\r\n\t\t\t\tconst item = this.bottomNavList[index];\r\n\t\t\t\tconsole.log('点击了底部导航:', item);\r\n\r\n\t\t\t\t// 跳转到对应的内页\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/menus/index?id=${item.id}&name=${encodeURIComponent(item.name)}`,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 获取广告图片数据\r\n\t\t\tasync fetchAdImage() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 获取ID为36的图片信息\r\n\t\t\t\t\tconst imageInfo = await fetchImageById(36);\r\n\t\t\t\t\tconsole.log('获取广告图片信息成功:', imageInfo);\r\n\r\n\t\t\t\t\tif (imageInfo && imageInfo.url) {\r\n\t\t\t\t\t\tthis.adImage = imageInfo;\r\n\t\t\t\t\t\t// 使用工具函数获取完整的图片URL\r\n\t\t\t\t\t\tthis.adImageUrl = getFullImageUrl(imageInfo.url);\r\n\t\t\t\t\t\tconsole.log('广告图片URL:', this.adImageUrl);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('广告图片信息不完整');\r\n\t\t\t\t\t\t// 设置默认图片\r\n\t\t\t\t\t\tthis.adImageUrl = '/static/images/640.gif';\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取广告图片信息失败:', error);\r\n\t\t\t\t\t// 设置默认图片\r\n\t\t\t\t\tthis.adImageUrl = '/static/images/640.gif';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/* 使用全局样式文件中的.common-container */\r\n\r\n\t.search-box {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 16rpx 24rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tbox-shadow: 0 rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.section-content {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tpadding: 10rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.ad-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.swiper-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t/* 自定义轮播图样式 */\r\n\t:deep(.u-swiper__wrapper) {\r\n\t\tborder-radius: 12rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t:deep(.u-swiper-indicator__dot) {\r\n\t\twidth: 12rpx;\r\n\t\theight: 12rpx;\r\n\t}\r\n\r\n\t:deep(.u-swiper__title) {\r\n\t\tbackground-color: rgba(0, 0, 0, 0.4);\r\n\t\tpadding: 16rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.notice-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t/* 自定义NoticeBar样式 */\r\n\t:deep(.u-notice-bar) {\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t:deep(.u-notice-bar__content__text) {\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 12rpx 24rpx;\r\n\t\ttext-align: left;\r\n\t}\r\n\r\n\t.quick-entry {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.entry-grid {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.grid-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 8rpx;\r\n\t\tmargin: 4rpx;\r\n\t\theight: 110rpx;\r\n\t}\r\n\r\n\t.icon-circle {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tbackground-color: #ffeeee;\r\n\t\tmargin-bottom: 6rpx;\r\n\t}\r\n\r\n\t.news-section {\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t/* 由于使用了u-cell组件，不再需要news-item样式 */\r\n\r\n\t.bottom-nav {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 0;\r\n\t\tpadding: 6rpx 0;\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbox-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t\tz-index: 999;\r\n\t\tborder-top: 1rpx solid #f5f5f5;\r\n\t\theight: 100rpx;\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 6rpx 0;\r\n\t}\r\n\r\n\r\n</style>\r\n", "import MiniProgramPage from 'M:/win11DeskTop/zoujusai/RedProtectio/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "testGetArticleList", "articleApi.getArticleList", "menuApi.getMenuList", "fetchImageById", "getFullImageUrl"], "mappings": ";;;;;;AAgHC,MAAK,eAAgB,MAAW;AAGhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,YAAY;AAAA,QACX;AAAA,UACC,KAAK;AAAA,UACL,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,KAAK;AAAA,UACL,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,KAAK;AAAA,UACL,OAAO;AAAA,QACR;AAAA,MACA;AAAA;AAAA,MAED,YAAY;AAAA,QACX;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,QACR;AAAA,MACA;AAAA;AAAA,MAED,gBAAgB;AAAA,QACf;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,QACL;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,KAAK;AAAA,UACL,OAAO;AAAA;AAAA,QACR;AAAA,MACA;AAAA;AAAA,MAED,UAAU,CAAE;AAAA;AAAA,MAEZ,eAAe,CAAE;AAAA;AAAA,MAEjB,aAAa;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACR;AAAA;AAAA,MAED,SAAS;AAAA;AAAA,MAET,YAAY;AAAA,IACb;AAAA,EACA;AAAA,EACD,SAAS;AAERA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,cAAc;AAG1BC,aAAAA;AAGA,SAAK,iBAAgB;AAGrB,SAAK,cAAa;AAGlB,SAAK,aAAY;AAAA,EACjB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,mBAAmB;AACxB,UAAI;AACH,cAAM,SAAS,MAAMC,SAAAA;AACrBF,sBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,MAAM;AAE/B,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAC3D,eAAK,WAAW,OAAO;AACvBA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW;AAAA,QACxB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAED,cAAc,OAAO;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,YAAY,KAAK,WAAW,KAAK,EAAE;AAAA,QAC1C,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAED,cAAc,GAAG;AAChBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,WAAW,EAAE,SAAS;AAAA,QAC7B,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAED,cAAc,OAAO;AACpB,YAAM,OAAO,KAAK,WAAW,KAAK;AAClCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,WAAW,KAAK;AAAA,QACvB,MAAM;AAAA,OACN;AAAA,IACD;AAAA;AAAA,IAED,YAAY,OAAO;AAClB,YAAM,OAAO,KAAK,eAAe,KAAK;AACtCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,IAAI;AAG5B,UAAI,KAAK,OAAO;AACfA,yEAAY,iBAAiB,KAAK,KAAK;AACvC,aAAK,sBAAsB,IAAI;AAAA,MAChC,WAAW,KAAK,KAAK;AAEpBA,sBAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,GAAG;AAE/BA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,KAAK;AAAA,UACV,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,aACK;AAENA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,UAAU,KAAK;AAAA,UACtB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAED,sBAAsB,MAAM;AAC3BA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,OAAO,UAAU,KAAK,KAAK;AAEzDA,oBAAAA,MAAI,sBAAsB;AAAA,QACzB,OAAO,KAAK;AAAA,QACZ,MAAM;AAAA;AAAA,QACN,WAAW;AAAA;AAAA,UAEV,QAAQ;AAAA,UACR,MAAM;AAAA,QACN;AAAA,QACD,YAAY;AAAA;AAAA,QACZ,QAAQ,KAAK;AACZA,wBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,GAAG;AAAA,QAC3B;AAAA,QACD,KAAK,KAAK;AACTA,wBAAc,MAAA,MAAA,SAAA,gCAAA,YAAY,GAAG;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK;AAAA,YACrB,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAED,YAAY,MAAM;AACjBA,oBAAA,MAAA,MAAA,OAAA,gCAAY,UAAU,IAAI;AAE1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,4BAA4B,KAAK,EAAE;AAAA,QACxC,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,gCAAA,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAED,MAAM,gBAAgB;AACrB,UAAI;AACH,cAAM,SAAS,MAAMG,UAAAA;AACrBH,sBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,MAAM;AAE/B,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAE3D,gBAAM,kBAAkB,OAAO,KAAK,OAAO,UAAQ,KAAK,UAAU,CAAC;AAGnE,eAAK,gBAAgB,gBAAgB,IAAI,UAAQ;AAChD,mBAAO;AAAA,cACN,GAAG;AAAA,cACH,MAAM,KAAK,YAAY,KAAK,IAAI,KAAK;AAAA;UAEvC,CAAC;AAEDA,wBAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,KAAK,aAAa;AAI9C,gBAAM,mBAAmB,OAAO,KAAK,OAAO,UAAQ,KAAK,UAAU,CAAC;AAGpE,eAAK,eAAe,QAAQ,CAAC,YAAY,UAAU;AAOlD,kBAAM,cAAc,iBAAiB,KAAK,UAAQ,KAAK,SAAS,WAAW,KAAK;AAEhF,gBAAI,aAAa;AAEhB,oBAAM,aAAa,gBAAgB,KAAK,UAAQ,KAAK,OAAO,YAAY,QAAQ;AAEhF,kBAAI,YAAY;AAEf,qBAAK,eAAe,KAAK,EAAE,MAAM,yBAAyB,WAAW,EAAE,SAAS,mBAAmB,WAAW,IAAI,CAAC,cAAc,YAAY,EAAE;AAC/IA,8BAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY,WAAW,KAAK,WAAW,KAAK,eAAe,KAAK,EAAE,GAAG;AAAA,cAClF;AAAA,YACD;AAAA,UAED,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACA;AAAA;AAAA,IAOD,YAAY;AACXA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,OAAO;AACjB,YAAM,OAAO,KAAK,cAAc,KAAK;AACrCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,YAAY,IAAI;AAG5BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,yBAAyB,KAAK,EAAE,SAAS,mBAAmB,KAAK,IAAI,CAAC;AAAA,QAC3E,MAAM,CAAC,QAAQ;AACdA,wBAAc,MAAA,MAAA,SAAA,gCAAA,SAAS,GAAG;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,MAAM,eAAe;AACpB,UAAI;AAEH,cAAM,YAAY,MAAMI,2BAAe,EAAE;AACzCJ,sBAAY,MAAA,MAAA,OAAA,gCAAA,eAAe,SAAS;AAEpC,YAAI,aAAa,UAAU,KAAK;AAC/B,eAAK,UAAU;AAEf,eAAK,aAAaK,YAAAA,gBAAgB,UAAU,GAAG;AAC/CL,wBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,KAAK,UAAU;AAAA,eACjC;AACNA,wBAAAA,MAAA,MAAA,SAAA,gCAAc,WAAW;AAEzB,eAAK,aAAa;AAAA,QACnB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,eAAe,KAAK;AAElC,aAAK,aAAa;AAAA,MACnB;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpcD,GAAG,WAAW,eAAe;"}