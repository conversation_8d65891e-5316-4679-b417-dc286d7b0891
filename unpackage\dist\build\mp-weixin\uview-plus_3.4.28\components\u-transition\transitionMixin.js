"use strict";const e=require("../../../common/vendor.js"),t=require("../../libs/function/index.js"),i=e=>({enter:`u-${e}-enter u-${e}-enter-active`,"enter-to":`u-${e}-enter-to u-${e}-enter-active`,leave:`u-${e}-leave u-${e}-leave-active`,"leave-to":`u-${e}-leave-to u-${e}-leave-active`}),s={methods:{clickHandler(){this.$emit("click")},async vueEnter(){const s=i(this.mode);this.status="enter",this.$emit("beforeEnter"),this.inited=!0,this.display=!0,this.classes=s.enter,await e.nextTick$1(),await t.sleep(20),this.$emit("enter"),this.transitionEnded=!1,this.$emit("afterEnter"),this.classes=s["enter-to"]},async vueLeave(){if(!this.display)return;const t=i(this.mode);this.status="leave",this.$emit("beforeLeave"),this.classes=t.leave,await e.nextTick$1(),this.transitionEnded=!1,this.$emit("leave"),setTimeout(this.onTransitionEnd,this.duration),this.classes=t["leave-to"]},onTransitionEnd(){this.transitionEnded||(this.transitionEnded=!0,this.$emit("leave"===this.status?"afterLeave":"afterEnter"),!this.show&&this.display&&(this.display=!1,this.inited=!1))}}};exports.transitionMixin=s;
