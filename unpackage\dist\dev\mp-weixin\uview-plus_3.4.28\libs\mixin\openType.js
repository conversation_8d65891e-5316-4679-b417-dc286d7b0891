"use strict";
const uviewPlus_3_4_28_libs_vue = require("../vue.js");
const openType = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    openType: String
  },
  methods: {
    onGetUserInfo(event) {
      this.$emit("getuserinfo", event.detail);
    },
    onContact(event) {
      this.$emit("contact", event.detail);
    },
    onGetPhoneNumber(event) {
      this.$emit("getphonenumber", event.detail);
    },
    onError(event) {
      this.$emit("error", event.detail);
    },
    onLaunchApp(event) {
      this.$emit("launchapp", event.detail);
    },
    onOpenSetting(event) {
      this.$emit("opensetting", event.detail);
    }
  }
});
exports.openType = openType;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/libs/mixin/openType.js.map
