{"version": 3, "file": "popup.js", "sources": ["uview-plus_3.4.28/components/u-popup/popup.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:06:33\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/popup.js\n */\nexport default {\n    // popup组件\n    popup: {\n        show: false,\n        overlay: true,\n        mode: 'bottom',\n        duration: 300,\n        closeable: false,\n        overlayStyle: {},\n        closeOnClickOverlay: true,\n        zIndex: 10075,\n        safeAreaInsetBottom: true,\n        safeAreaInsetTop: false,\n        closeIconPos: 'top-right',\n        round: 0,\n        zoom: true,\n        bgColor: '',\n        overlayOpacity: 0.5\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,cAAc,CAAE;AAAA,IAChB,qBAAqB;AAAA,IACrB,QAAQ;AAAA,IACR,qBAAqB;AAAA,IACrB,kBAAkB;AAAA,IAClB,cAAc;AAAA,IACd,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,IACT,gBAAgB;AAAA,EACnB;AACL;;"}