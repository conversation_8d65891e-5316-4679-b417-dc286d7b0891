"use strict";const e=require("./props.js"),r=require("../../libs/mixin/mpMixin.js"),s=require("../../libs/mixin/mixin.js"),t=require("../../libs/function/index.js"),o=require("../../../common/vendor.js"),i={name:"u-list",mixins:[r.mpMixin,s.mixin,e.props],watch:{scrollIntoView(e){this.scrollIntoViewById(e)}},data:()=>({innerScrollTop:0,offset:0,sys:t.getWindowInfo()}),computed:{listStyle(){const e={};return 0!=this.width&&(e.width=t.addUnit(this.width)),0!=this.height&&(e.height=t.addUnit(this.height)),e.height||(e.height=t.addUnit(this.sys.windowHeight,"px")),t.deepMerge(e,t.addStyle(this.customStyle))}},provide(){return{uList:this}},created(){this.refs=[],this.children=[],this.anchors=[]},mounted(){},emits:["scroll","scrolltolower","scrolltoupper","refresherpulling","refresherrefresh","refresherrestore","refresherabort"],methods:{updateOffsetFromChild(e){this.offset=e},onScroll(e){let r=0;r=e.detail.scrollTop,this.innerScrollTop=r,this.$emit("scroll",r)},scrollIntoViewById(e){},scrolltolower(e){t.sleep(30).then((()=>{this.$emit("scrolltolower")}))},scrolltoupper(e){t.sleep(30).then((()=>{this.$emit("scrolltoupper"),this.offset=0}))},refresherpulling(e){this.$emit("refresherpulling",e)},refresherrefresh(e){this.$emit("refresherrefresh",e)},refresherrestore(e){this.$emit("refresherrestore",e)},refresherabort(e){this.$emit("refresherabort",e)}}};const l=o._export_sfc(i,[["render",function(e,r,s,t,i,l){return{a:e.scrollIntoView,b:o.s(l.listStyle),c:e.scrollable,d:Number(e.scrollTop),e:Number(e.lowerThreshold),f:Number(e.upperThreshold),g:e.showScrollbar,h:e.enableBackToTop,i:e.scrollWithAnimation,j:o.o(((...e)=>l.onScroll&&l.onScroll(...e))),k:o.o(((...e)=>l.scrolltolower&&l.scrolltolower(...e))),l:o.o(((...e)=>l.scrolltoupper&&l.scrolltoupper(...e))),m:e.refresherEnabled,n:e.refresherThreshold,o:e.refresherDefaultStyle,p:e.refresherBackground,q:e.refresherTriggered,r:o.o(((...e)=>l.refresherpulling&&l.refresherpulling(...e))),s:o.o(((...e)=>l.refresherrefresh&&l.refresherrefresh(...e))),t:o.o(((...e)=>l.refresherrestore&&l.refresherrestore(...e))),v:o.o(((...e)=>l.refresherabort&&l.refresherabort(...e)))}}],["__scopeId","data-v-1e2e62ff"]]);wx.createComponent(l);
