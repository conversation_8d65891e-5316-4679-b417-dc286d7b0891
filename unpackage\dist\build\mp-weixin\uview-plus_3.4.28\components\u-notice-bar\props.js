"use strict";const e=require("../../libs/vue.js"),t=require("../../libs/config/props.js"),r=e.defineMixin({props:{text:{type:[Array,String],default:()=>t.props.noticeBar.text},direction:{type:String,default:()=>t.props.noticeBar.direction},step:{type:<PERSON><PERSON><PERSON>,default:()=>t.props.noticeBar.step},icon:{type:String,default:()=>t.props.noticeBar.icon},mode:{type:String,default:()=>t.props.noticeBar.mode},color:{type:String,default:()=>t.props.noticeBar.color},bgColor:{type:String,default:()=>t.props.noticeBar.bgColor},speed:{type:[String,Number],default:()=>t.props.noticeBar.speed},fontSize:{type:[String,Number],default:()=>t.props.noticeBar.fontSize},duration:{type:[String,Number],default:()=>t.props.noticeBar.duration},disableTouch:{type:Boolean,default:()=>t.props.noticeBar.disableTouch},url:{type:String,default:()=>t.props.noticeBar.url},linkType:{type:String,default:()=>t.props.noticeBar.linkType},justifyContent:{type:String,default:()=>t.props.noticeBar.justifyContent}}});exports.props=r;
