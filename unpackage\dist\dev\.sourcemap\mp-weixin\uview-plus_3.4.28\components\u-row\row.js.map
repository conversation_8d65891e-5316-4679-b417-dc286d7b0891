{"version": 3, "file": "row.js", "sources": ["uview-plus_3.4.28/components/u-row/row.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:18:58\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/row.js\n */\nexport default {\n    // row\n    row: {\n        gutter: 0,\n        justify: 'start',\n        align: 'center'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,MAAA;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,OAAO;AAAA,EACV;AACL;;"}