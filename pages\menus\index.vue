<template>
	<view class="common-container bottom-layout">
		<!-- 自定义导航栏 -->
		<custom-navbar :title="parentMenuName" :showBack="true" :showHome="true">
			<template #right>
				<view class="navbar-right">
					<u-icon name="more-dot-fill" color="#FFFFFF" size="20"></u-icon>
				</view>
			</template>
		</custom-navbar>

		<!-- 顶部标签页 -->
		<view class="tabs-container">
			<u-tabs
				v-if="tabList && tabList.length > 0"
				:list="tabList"
				v-model:current="current"
				@change="onTabChange"
				lineColor="#D9001B"
				lineWidth="30"
				itemStyle="padding-left: 30rpx; padding-right: 30rpx; height: 80rpx;"
				:activeStyle="{
					color: '#D9001B',
					fontWeight: 'bold',
					fontSize: '28rpx'
				}"
				:inactiveStyle="{
					color: '#333333',
					fontSize: '28rpx'
				}"
			></u-tabs>
		</view>

		<!-- 内容区域 -->
		<view class="content-container">
			<!-- 组织架构组件 -->
			<view v-if="showPartyComponent" class="party-content">
				<view class="content-section">
					<view class="section-title">
						<u-icon name="grid-fill" color="#D9001B" size="20"></u-icon>
						<text>{{ currentTitle }}</text>
					</view>

					<view class="section-content">
						<u-list :enable-flex="true">
							<u-cell v-for="(item, index) in categoryList" :key="index"
								:title="item.name"
								:titleStyle="{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}"
								isLink
								@click="onCategoryClick(index)">
							</u-cell>
						</u-list>
					</view>
				</view>

				<!-- 底部操作栏 -->
				<view class="party-footer" v-if="categoryHistory.length > 0">
					<u-button type="primary" text="返回上一级" @click="goBackToParent" :customStyle="{backgroundColor: '#D9001B'}"></u-button>
				</view>
			</view>

			<!-- 文章列表 -->
			<view v-else>
				<view v-if="articleList.length > 0">
					<view
						class="article-item"
						v-for="(item, index) in articleList"
						:key="index"
						@click="goToArticleDetail(item)"
					>
						<view class="article-content">
							<view class="article-title u-line-2">{{ item.title }}</view>
							<view class="article-summary u-line-2" v-if="item.content">
								{{ getPlainTextSummary(item.content) }}
							</view>
							<view class="article-info">
								<text class="article-date">{{ formatDate(item.createdAt) }}</text>
							</view>
						</view>
						<view class="article-image" v-if="item.coverImage">
							<image :src="item.coverImage" mode="aspectFill"></image>
						</view>
					</view>
				</view>
				<view v-else class="empty-container">
					<u-empty mode="data" text="暂无数据"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { menuApi, menuArticleApi, categoryApi } from '@/api/index.js';
	import CustomNavbar from '@/components/common/custom-navbar.vue';
	import { fetchImageById, getFullImageUrl } from '@/utils/image.js';

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				parentMenuId: null, // 父级菜单ID
				parentMenuName: '组织工作', // 父级菜单名称
				tabList: [], // 标签列表
				current: 0, // 当前选中的标签索引
				articleList: [], // 文章列表
				statusBarHeight: 20, // 状态栏高度，默认值
				showPartyComponent: false, // 是否显示组织架构组件

				// 组织架构相关数据
				categoryList: [], // 基层党组织数据
				categoryHistory: [], // 分类历史记录，用于返回上一级
				currentLevel: 1, // 当前分类层级
				currentTitle: '基层党组织' // 当前分类标题
			}
		},
		computed: {
			// 计算顶部安全区域的高度
			safeAreaTop() {
				return this.statusBarHeight + 45; // 状态栏高度 + 导航栏高度(90rpx转为px的近似值)
			}
		},
		onLoad(options) {
			// 获取状态栏高度
			this.getStatusBarHeight();

			// 获取父级菜单ID
			if (options.id) {
				this.parentMenuId = Number(options.id);

				// 获取子菜单ID（如果有）
				const subMenuId = options.subMenuId ? Number(options.subMenuId) : null;

				// 加载子菜单，并传入子菜单ID
				this.loadSubMenus(subMenuId);
			}

			// 如果有菜单名称，则设置
			if (options.name) {
				this.parentMenuName = decodeURIComponent(options.name);
			}
		},
		methods: {
			// 获取状态栏高度
			getStatusBarHeight() {
				try {
					// 使用uni-app提供的API获取窗口信息
					const windowInfo = uni.getWindowInfo();
					this.statusBarHeight = windowInfo.statusBarHeight || 20;

					console.log('状态栏高度:', this.statusBarHeight);
					// 在小程序中不能直接设置CSS变量，我们使用计算属性来处理
				} catch (e) {
					console.error('获取状态栏高度失败:', e);
					// 设置默认值
					this.statusBarHeight = 20;
				}
			},

			// 返回上一页方法已由导航栏组件自动处理

			// 加载子菜单
			async loadSubMenus(subMenuId = null) {
				try {
					// 获取所有菜单
					const result = await menuApi.getMenuList();
					if (result && result.success && Array.isArray(result.data)) {
						// 过滤出指定父ID的二级菜单
						const subMenus = result.data.filter(item => item.parentId === this.parentMenuId);

						// 转换为标签格式
						this.tabList = subMenus.map(item => {
							return {
								name: item.name,
								id: item.id
							};
						});

						// 如果有子菜单
						if (this.tabList.length > 0) {
							// 如果指定了子菜单ID，则查找对应的索引
							if (subMenuId) {
								const index = this.tabList.findIndex(item => item.id === subMenuId);
								if (index !== -1) {
									// 找到了指定的子菜单，设置当前索引
									this.current = index;
									// 加载指定子菜单的文章
									this.loadArticles(subMenuId);
									return;
								}
							}

							// 如果没有指定子菜单ID或者找不到指定的子菜单，则默认加载第一个子菜单
							this.current = 0;
							// 直接加载第一个子菜单的文章
							this.loadArticles(this.tabList[0].id);
						}
					}
				} catch (error) {
					console.error('获取子菜单列表失败:', error);
					uni.showToast({
						title: '获取菜单失败',
						icon: 'none'
					});
				}
			},

			// 标签切换事件
			onTabChange(tabItem) {
				// 使用tabItem对象，它包含index和其他属性
				if (tabItem && tabItem.id) {
					this.loadArticles(tabItem.id);
				}
			},

			// 加载文章列表
			async loadArticles(menuId) {
				// 根据menuId找到对应的标签
				const currentTab = this.tabList.find(tab => tab.id === menuId);

				// 如果是"组织架构"标签
				if (currentTab && currentTab.name === "组织架构") {
					this.showPartyComponent = true;
					// 获取组织架构数据
					this.fetchCategoryList();
					return; // 不继续加载文章
				} else {
					this.showPartyComponent = false;
				}

				try {
					// 使用新的API接口获取菜单文章
					const result = await menuArticleApi.getMenuArticles(menuId);
					if (result && result.success && Array.isArray(result.data)) {
						// 处理文章列表数据
						const articles = result.data;

						// 只处理每篇文章的封面图
						for (let article of articles) {
							// 处理封面图
							if (article.coverImageId && !article.coverImage) {
								try {
									const imageData = await fetchImageById(article.coverImageId);
									if (imageData && imageData.url) {
										article.coverImage = getFullImageUrl(imageData.url);
									}
								} catch (err) {
									console.error('获取文章封面图失败:', err);
								}
							}
						}

						this.articleList = articles;
					} else {
						this.articleList = [];
					}
				} catch (error) {
					console.error('获取菜单文章列表失败:', error);
					this.articleList = [];
					uni.showToast({
						title: '获取文章失败',
						icon: 'none'
					});
				}
			},

			// 获取纯文本摘要
			getPlainTextSummary(htmlContent) {
				// 移除HTML标签
				const plainText = htmlContent.replace(/<[^>]+>/g, '');
				// 移除多余空格和换行
				const trimmedText = plainText.replace(/\s+/g, ' ').trim();
				// 截取前100个字符作为摘要
				return trimmedText.length > 100 ? trimmedText.substring(0, 100) + '...' : trimmedText;
			},

			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},

			// 跳转到文章详情
			goToArticleDetail(article) {
				// 跳转到统一的文章详情页面
				uni.navigateTo({
					url: `/pages/article/detail?id=${article.id}&type=menu`,
					fail: (err) => {
						console.error('跳转失败:', err);
						uni.showToast({
							title: '跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 以下是组织架构相关方法
			// 获取分类列表
			async fetchCategoryList() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					const result = await categoryApi.getCategoryTreeList();
					console.log('获取分类树形列表成功:', result);

					if (result && result.success && Array.isArray(result.data)) {
						this.categoryList = result.data;
						console.log('分类树形列表数据已更新');
					}
				} catch (error) {
					console.error('获取分类树形列表失败:', error);
					uni.showToast({
						title: '获取分类数据失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},

			// 分类点击事件
			onCategoryClick(index) {
				const item = this.categoryList[index];
				console.log('点击了基层党组织:', item);

				// 判断是否有子分类
				if (item.children && item.children.length > 0) {
					// 保存当前分类列表到历史记录
					this.categoryHistory.push({
						list: [...this.categoryList],
						title: this.currentTitle,
						level: this.currentLevel
					});

					// 有子分类，更新当前列表为子分类
					this.categoryList = item.children;

					// 更新当前标题和层级
					this.currentTitle = item.name;
					this.currentLevel++;
				} else {
					// 没有子分类，跳转到分类文章列表页面
					uni.navigateTo({
						url: `/pages/party/category-articles/index?id=${item.id}`,
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				}
			},

			// 返回上一级分类
			goBackToParent() {
				if (this.categoryHistory.length > 0) {
					// 从历史记录中获取上一级分类信息
					const prevCategory = this.categoryHistory.pop();

					// 恢复上一级分类列表
					this.categoryList = prevCategory.list;
					this.currentTitle = prevCategory.title;
					this.currentLevel = prevCategory.level;

					return true;
				}
				return false;
			}
		}
	}
</script>

<style lang="scss">
	/* 使用全局样式文件中的.common-container */

	.tabs-container {
		background-color: #FFF1F0;
		padding: 15rpx 20rpx;
		position: fixed;
		top: 85px; /* 导航栏高度 + 额外空间，减少间距 */
		left: 0;
		right: 0;
		z-index: 998;
		// border-bottom: 1px solid #f0f0f0;
	}

	/* 使用全局样式文件中的.navbar-right */

	/* 组织架构相关样式 */
	.party-content {
		margin-bottom: 30rpx;
		padding: 20rpx;

		.content-section {
			background-color: #FFFFFF;
			border-radius: 8rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;

			.section-title {
				display: flex;
				align-items: center;
				padding-bottom: 20rpx;
				border-bottom: 1px solid #f0f0f0;
				margin-bottom: 20rpx;

				text {
					margin-left: 10rpx;
					font-size: 30rpx;
					font-weight: bold;
					color: #333333;
				}
			}

			.section-content {
				min-height: 200rpx;
			}
		}
	}

	.party-footer {
		padding: 20rpx;
		margin-top: 20rpx;
	}

	.content-container {
		flex: 1;
		padding: 15rpx;
		margin-top: 30px; /* 减少顶部间距，使布局更紧凑 */

		.article-item {
			display: flex;
			justify-content: space-between;
			background-color: #FFFFFF;
			padding: 20rpx;
			margin-bottom: 15rpx;
			border-radius: 4rpx;

			.article-content {
				flex: 1;
				margin-right: 20rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				min-height: 120rpx; /* 与图片高度一致 */

				.article-title {
					font-size: 28rpx;
					color: #333333;
					margin-bottom: 10rpx;
					font-weight: 500;
					line-height: 1.4;
					overflow: hidden;
				}

				.article-summary {
					font-size: 24rpx;
					color: #666666;
					margin-bottom: 10rpx;
					line-height: 1.4;
					overflow: hidden;
				}

				.article-info {
					display: flex;
					font-size: 24rpx;
					color: #999999;
					align-items: center;
					margin-top: auto; /* 推到底部 */

					.article-date {
						margin-right: 20rpx;
					}
				}
			}

			.article-image {
				width: 180rpx;
				height: 120rpx;
				border-radius: 4rpx;
				overflow: hidden;
				flex-shrink: 0;
				align-self: center; /* 垂直居中 */

				image {
					width: 100%;
					height: 100%;
				}
			}
		}

		.empty-container {
			padding: 100rpx 0;
		}
	}
</style>
