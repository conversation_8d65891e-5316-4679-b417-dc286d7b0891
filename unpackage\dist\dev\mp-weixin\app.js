"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const uviewPlus_3_4_28_index = require("./uview-plus_3.4.28/index.js");
const utils_uviewConfig = require("./utils/uview-config.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/party/index.js";
  "./pages/party/category-articles/index.js";
  "./pages/menus/index.js";
  "./pages/article/detail.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
    if (!this.$options.fontLoaded) {
      this.$options.fontLoaded = true;
      common_vendor.index.loadFontFace({
        family: "uicon-iconfont",
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          common_vendor.index.__f__("log", "at App.vue:14", "字体加载成功");
        },
        fail(err) {
          common_vendor.index.__f__("error", "at App.vue:17", "字体加载失败", err);
        }
      });
    }
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:24", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:27", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(uviewPlus_3_4_28_index.uviewPlus, () => {
    return {
      options: {
        // 修改$u.config对象的属性
        config: {
          // 修改默认单位为rpx
          unit: "rpx",
          // 使用官方字体图标
          iconUrl: utils_uviewConfig.uviewConfig.iconUrl,
          customIcon: utils_uviewConfig.uviewConfig.customIcon
        }
      }
    };
  });
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
