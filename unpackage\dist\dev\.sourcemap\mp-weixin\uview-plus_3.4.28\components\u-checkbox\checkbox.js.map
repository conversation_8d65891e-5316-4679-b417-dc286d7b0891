{"version": 3, "file": "checkbox.js", "sources": ["uview-plus_3.4.28/components/u-checkbox/checkbox.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-23 21:06:59\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkbox.js\n */\nexport default {\n    // checkbox组件\n    checkbox: {\n        name: '',\n        shape: '',\n        size: '',\n        checkbox: false,\n        disabled: '',\n        activeColor: '',\n        inactiveColor: '',\n        iconSize: '',\n        iconColor: '',\n        label: '',\n        labelSize: '',\n        labelColor: '',\n        labelDisabled: ''\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAClB;AACL;;"}