{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-empty/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 内置图标名称，或图片路径，建议绝对路径\n        icon: {\n            type: String,\n            default: () => defProps.empty.icon\n        },\n        // 提示文字\n        text: {\n            type: String,\n            default: () => defProps.empty.text\n        },\n        // 文字颜色\n        textColor: {\n            type: String,\n            default: () => defProps.empty.textColor\n        },\n        // 文字大小\n        textSize: {\n            type: [String, Number],\n            default: () => defProps.empty.textSize\n        },\n        // 图标的颜色\n        iconColor: {\n            type: String,\n            default: () => defProps.empty.iconColor\n        },\n        // 图标的大小\n        iconSize: {\n            type: [String, Number],\n            default: () => defProps.empty.iconSize\n        },\n        // 选择预置的图标类型\n        mode: {\n            type: String,\n            default: () => defProps.empty.mode\n        },\n        //  图标宽度，单位px\n        width: {\n            type: [String, Number],\n            default: () => defProps.empty.width\n        },\n        // 图标高度，单位px\n        height: {\n            type: [String, Number],\n            default: () => defProps.empty.height\n        },\n        // 是否显示组件\n        show: {\n            type: Boolean,\n            default: () => defProps.empty.show\n        },\n        // 组件距离上一个元素之间的距离，默认px单位\n        marginTop: {\n            type: [String, Number],\n            default: () => defProps.empty.marginTop\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}