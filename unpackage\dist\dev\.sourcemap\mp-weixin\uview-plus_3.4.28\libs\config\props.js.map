{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/libs/config/props.js"], "sourcesContent": ["/**\n * 此文件的作用为统一配置所有组件的props参数\n * 借此用户可以全局覆盖组件的props默认值\n * 无需在每个引入组件的页面中都配置一次\n */\nimport config from './config'\n// 各个需要fixed的地方的z-index配置文件\nimport zIndex from './zIndex.js'\n// 关于颜色的配置，特殊场景使用\nimport color from './color.js'\n// http\nimport http from '../function/http.js'\nimport { shallowMerge } from '../function/index.js'\n// 组件props\nimport ActionSheet from '../../components/u-action-sheet/actionSheet'\nimport Album from '../../components/u-album/album'\nimport Alert from '../../components/u-alert/alert'\nimport Avatar from '../../components/u-avatar/avatar'\nimport AvatarGroup from '../../components/u-avatar-group/avatarGroup'\nimport Backtop from '../../components/u-back-top/backtop'\nimport Badge from '../../components/u-badge/badge'\nimport Button from '../../components/u-button/button'\nimport Calendar from '../../components/u-calendar/calendar'\nimport CarKeyboard from '../../components/u-car-keyboard/carKeyboard'\nimport Card from '../../components/u-card/card'\nimport Cell from '../../components/u-cell/cell'\nimport CellGroup from '../../components/u-cell-group/cellGroup'\nimport Checkbox from '../../components/u-checkbox/checkbox'\nimport CheckboxGroup from '../../components/u-checkbox-group/checkboxGroup'\nimport CircleProgress from '../../components/u-circle-progress/circleProgress'\nimport Code from '../../components/u-code/code'\nimport CodeInput from '../../components/u-code-input/codeInput'\nimport Col from '../../components/u-col/col'\nimport Collapse from '../../components/u-collapse/collapse'\nimport CollapseItem from '../../components/u-collapse-item/collapseItem'\nimport ColumnNotice from '../../components/u-column-notice/columnNotice'\nimport CountDown from '../../components/u-count-down/countDown'\nimport CountTo from '../../components/u-count-to/countTo'\nimport DatetimePicker from '../../components/u-datetime-picker/datetimePicker'\nimport Divider from '../../components/u-divider/divider'\nimport Empty from '../../components/u-empty/empty'\nimport Form from '../../components/u-form/form'\nimport GormItem from '../../components/u-form-item/formItem'\nimport Gap from '../../components/u-gap/gap'\nimport Grid from '../../components/u-grid/grid'\nimport GridItem from '../../components/u-grid-item/gridItem'\nimport Icon from '../../components/u-icon/icon'\nimport Image from '../../components/u-image/image'\nimport IndexAnchor from '../../components/u-index-anchor/indexAnchor'\nimport IndexList from '../../components/u-index-list/indexList'\nimport Input from '../../components/u-input/input'\nimport Keyboard from '../../components/u-keyboard/keyboard'\nimport Line from '../../components/u-line/line'\nimport LineProgress from '../../components/u-line-progress/lineProgress'\nimport Link from '../../components/u-link/link'\nimport List from '../../components/u-list/list'\nimport ListItem from '../../components/u-list-item/listItem'\nimport LoadingIcon from '../../components/u-loading-icon/loadingIcon'\nimport LoadingPage from '../../components/u-loading-page/loadingPage'\nimport Loadmore from '../../components/u-loadmore/loadmore'\nimport Modal from '../../components/u-modal/modal'\nimport Navbar from '../../components/u-navbar/navbar'\nimport NoNetwork from '../../components/u-no-network/noNetwork'\nimport NoticeBar from '../../components/u-notice-bar/noticeBar'\nimport Notify from '../../components/u-notify/notify'\nimport NumberBox from '../../components/u-number-box/numberBox'\nimport NumberKeyboard from '../../components/u-number-keyboard/numberKeyboard'\nimport Overlay from '../../components/u-overlay/overlay'\nimport Parse from '../../components/u-parse/parse'\nimport Picker from '../../components/u-picker/picker'\nimport Popup from '../../components/u-popup/popup'\nimport Radio from '../../components/u-radio/radio'\nimport RadioGroup from '../../components/u-radio-group/radioGroup'\nimport Rate from '../../components/u-rate/rate'\nimport ReadMore from '../../components/u-read-more/readMore'\nimport Row from '../../components/u-row/row'\nimport RowNotice from '../../components/u-row-notice/rowNotice'\nimport ScrollList from '../../components/u-scroll-list/scrollList'\nimport Search from '../../components/u-search/search'\nimport Section from '../../components/u-section/section'\nimport Skeleton from '../../components/u-skeleton/skeleton'\nimport Slider from '../../components/u-slider/slider'\nimport StatusBar from '../../components/u-status-bar/statusBar'\nimport Steps from '../../components/u-steps/steps'\nimport StepsItem from '../../components/u-steps-item/stepsItem'\nimport Sticky from '../../components/u-sticky/sticky'\nimport Subsection from '../../components/u-subsection/subsection'\nimport SwipeAction from '../../components/u-swipe-action/swipeAction'\nimport SwipeActionItem from '../../components/u-swipe-action-item/swipeActionItem'\nimport Swiper from '../../components/u-swiper/swiper'\nimport SwipterIndicator from '../../components/u-swiper-indicator/swipterIndicator'\nimport Switch from '../../components/u-switch/switch'\nimport Tabbar from '../../components/u-tabbar/tabbar'\nimport TabbarItem from '../../components/u-tabbar-item/tabbarItem'\nimport Tabs from '../../components/u-tabs/tabs'\nimport Tag from '../../components/u-tag/tag'\nimport Text from '../../components/u-text/text'\nimport Textarea from '../../components/u-textarea/textarea'\nimport Toast from '../../components/u-toast/toast'\nimport Toolbar from '../../components/u-toolbar/toolbar'\nimport Tooltip from '../../components/u-tooltip/tooltip'\nimport Transition from '../../components/u-transition/transition'\nimport Upload from '../../components/u-upload/upload'\n\nconst props = {\n    ...ActionSheet,\n    ...Album,\n    ...Alert,\n    ...Avatar,\n    ...AvatarGroup,\n    ...Backtop,\n    ...Badge,\n    ...Button,\n    ...Calendar,\n    ...CarKeyboard,\n    ...Card,\n    ...Cell,\n    ...CellGroup,\n    ...Checkbox,\n    ...CheckboxGroup,\n    ...CircleProgress,\n    ...Code,\n    ...CodeInput,\n    ...Col,\n    ...Collapse,\n    ...CollapseItem,\n    ...ColumnNotice,\n    ...CountDown,\n    ...CountTo,\n    ...DatetimePicker,\n    ...Divider,\n    ...Empty,\n    ...Form,\n    ...GormItem,\n    ...Gap,\n    ...Grid,\n    ...GridItem,\n    ...Icon,\n    ...Image,\n    ...IndexAnchor,\n    ...IndexList,\n    ...Input,\n    ...Keyboard,\n    ...Line,\n    ...LineProgress,\n    ...Link,\n    ...List,\n    ...ListItem,\n    ...LoadingIcon,\n    ...LoadingPage,\n    ...Loadmore,\n    ...Modal,\n    ...Navbar,\n    ...NoNetwork,\n    ...NoticeBar,\n    ...Notify,\n    ...NumberBox,\n    ...NumberKeyboard,\n    ...Overlay,\n    ...Parse,\n    ...Picker,\n    ...Popup,\n    ...Radio,\n    ...RadioGroup,\n    ...Rate,\n    ...ReadMore,\n    ...Row,\n    ...RowNotice,\n    ...ScrollList,\n    ...Search,\n    ...Section,\n    ...Skeleton,\n    ...Slider,\n    ...StatusBar,\n    ...Steps,\n    ...StepsItem,\n    ...Sticky,\n    ...Subsection,\n    ...SwipeAction,\n    ...SwipeActionItem,\n    ...Swiper,\n    ...SwipterIndicator,\n    ...Switch,\n    ...Tabbar,\n    ...TabbarItem,\n    ...Tabs,\n    ...Tag,\n    ...Text,\n    ...Textarea,\n    ...Toast,\n    ...Toolbar,\n    ...Tooltip,\n    ...Transition,\n    ...Upload\n}\n\nfunction setConfig(configs) {\n\tshallowMerge(config, configs.config || {})\n\tshallowMerge(props, configs.props || {})\n\tshallowMerge(color, configs.color || {})\n\tshallowMerge(zIndex, configs.zIndex || {})\n}\n\n// 初始化自定义配置\nif (uni && uni.upuiParams) {\n\tconsole.log('setting uview-plus')\n\tlet temp = uni.upuiParams()\n\tif (temp.httpIns) {\n\t\ttemp.httpIns(http)\n\t}\n\tif (temp.options) {\n\t\tsetConfig(temp.options)\n\t}\n}\n\nexport default props\n"], "names": ["ActionSheet", "Album", "<PERSON><PERSON>", "Avatar", "AvatarGroup", "Backtop", "Badge", "<PERSON><PERSON>", "Calendar", "CarKeyboard", "Card", "Cell", "CellGroup", "Checkbox", "CheckboxGroup", "CircleProgress", "Code", "CodeInput", "Col", "Collapse", "CollapseItem", "ColumnNotice", "CountDown", "<PERSON><PERSON><PERSON>", "DatetimePicker", "Divider", "Empty", "Form", "GormItem", "Gap", "Grid", "GridItem", "Icon", "Image", "IndexAnchor", "IndexList", "Input", "Keyboard", "Line", "LineProgress", "Link", "List", "ListItem", "LoadingIcon", "LoadingPage", "Loadmore", "Modal", "<PERSON><PERSON><PERSON>", "NoNetwork", "NoticeBar", "Notify", "NumberBox", "NumberKeyboard", "Overlay", "Parse", "Picker", "Popup", "Radio", "RadioGroup", "Rate", "ReadMore", "Row", "RowNotice", "ScrollList", "Search", "Section", "Skeleton", "Slide<PERSON>", "StatusBar", "Steps", "StepsItem", "<PERSON>y", "Subsection", "SwipeAction", "SwipeActionItem", "Swiper", "SwipterIndicator", "Switch", "Ta<PERSON><PERSON>", "TabbarItem", "Tabs", "Tag", "Text", "Textarea", "Toast", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Transition", "Upload", "shallowMerge", "config", "color", "zIndex", "uni", "http"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGK,MAAC,QAAQ;AAAA,EACV,GAAGA,qDAAW;AAAA,EACd,GAAGC,yCAAK;AAAA,EACR,GAAGC,yCAAK;AAAA,EACR,GAAGC,2CAAM;AAAA,EACT,GAAGC,qDAAW;AAAA,EACd,GAAGC,6CAAO;AAAA,EACV,GAAGC,yCAAK;AAAA,EACR,GAAGC,2CAAM;AAAA,EACT,GAAGC,+CAAQ;AAAA,EACX,GAAGC,qDAAW;AAAA,EACd,GAAGC,uCAAI;AAAA,EACP,GAAGC,uCAAI;AAAA,EACP,GAAGC,iDAAS;AAAA,EACZ,GAAGC,+CAAQ;AAAA,EACX,GAAGC,yDAAa;AAAA,EAChB,GAAGC,2DAAc;AAAA,EACjB,GAAGC,uCAAI;AAAA,EACP,GAAGC,iDAAS;AAAA,EACZ,GAAGC,qCAAG;AAAA,EACN,GAAGC,+CAAQ;AAAA,EACX,GAAGC,uDAAY;AAAA,EACf,GAAGC,uDAAY;AAAA,EACf,GAAGC,iDAAS;AAAA,EACZ,GAAGC,6CAAO;AAAA,EACV,GAAGC,2DAAc;AAAA,EACjB,GAAGC,6CAAO;AAAA,EACV,GAAGC,yCAAK;AAAA,EACR,GAAGC,uCAAI;AAAA,EACP,GAAGC,+CAAQ;AAAA,EACX,GAAGC,qCAAG;AAAA,EACN,GAAGC,uCAAI;AAAA,EACP,GAAGC,+CAAQ;AAAA,EACX,GAAGC,uCAAI;AAAA,EACP,GAAGC,yCAAK;AAAA,EACR,GAAGC,qDAAW;AAAA,EACd,GAAGC,iDAAS;AAAA,EACZ,GAAGC,yCAAK;AAAA,EACR,GAAGC,+CAAQ;AAAA,EACX,GAAGC,uCAAI;AAAA,EACP,GAAGC,uDAAY;AAAA,EACf,GAAGC,uCAAI;AAAA,EACP,GAAGC,uCAAI;AAAA,EACP,GAAGC,+CAAQ;AAAA,EACX,GAAGC,qDAAW;AAAA,EACd,GAAGC,qDAAW;AAAA,EACd,GAAGC,+CAAQ;AAAA,EACX,GAAGC,yCAAK;AAAA,EACR,GAAGC,2CAAM;AAAA,EACT,GAAGC,iDAAS;AAAA,EACZ,GAAGC,iDAAS;AAAA,EACZ,GAAGC,2CAAM;AAAA,EACT,GAAGC,iDAAS;AAAA,EACZ,GAAGC,2DAAc;AAAA,EACjB,GAAGC,6CAAO;AAAA,EACV,GAAGC,yCAAK;AAAA,EACR,GAAGC,2CAAM;AAAA,EACT,GAAGC,yCAAK;AAAA,EACR,GAAGC,yCAAK;AAAA,EACR,GAAGC,mDAAU;AAAA,EACb,GAAGC,uCAAI;AAAA,EACP,GAAGC,+CAAQ;AAAA,EACX,GAAGC,qCAAG;AAAA,EACN,GAAGC,iDAAS;AAAA,EACZ,GAAGC,mDAAU;AAAA,EACb,GAAGC,2CAAM;AAAA,EACT,GAAGC,6CAAO;AAAA,EACV,GAAGC,+CAAQ;AAAA,EACX,GAAGC,2CAAM;AAAA,EACT,GAAGC,iDAAS;AAAA,EACZ,GAAGC,yCAAK;AAAA,EACR,GAAGC,iDAAS;AAAA,EACZ,GAAGC,2CAAM;AAAA,EACT,GAAGC,mDAAU;AAAA,EACb,GAAGC,qDAAW;AAAA,EACd,GAAGC,6DAAe;AAAA,EAClB,GAAGC,2CAAM;AAAA,EACT,GAAGC,8DAAgB;AAAA,EACnB,GAAGC,2CAAM;AAAA,EACT,GAAGC,2CAAM;AAAA,EACT,GAAGC,mDAAU;AAAA,EACb,GAAGC,uCAAI;AAAA,EACP,GAAGC,qCAAG;AAAA,EACN,GAAGC,uCAAI;AAAA,EACP,GAAGC,+CAAQ;AAAA,EACX,GAAGC,yCAAK;AAAA,EACR,GAAGC,6CAAO;AAAA,EACV,GAAGC,6CAAO;AAAA,EACV,GAAGC,mDAAU;AAAA,EACb,GAAGC,2CAAM;AACb;AAEA,SAAS,UAAU,SAAS;AAC3BC,uCAAAA,aAAaC,oCAAM,QAAE,QAAQ,UAAU,CAAA,CAAE;AACzCD,uCAAAA,aAAa,OAAO,QAAQ,SAAS,CAAA,CAAE;AACvCA,uCAAAA,aAAaE,mCAAK,OAAE,QAAQ,SAAS,CAAA,CAAE;AACvCF,uCAAAA,aAAaG,oCAAM,QAAE,QAAQ,UAAU,CAAA,CAAE;AAC1C;AAGA,IAAIC,cAAG,SAAIA,cAAG,MAAC,YAAY;AAC1BA,gBAAAA,MAAA,MAAA,OAAA,iDAAY,oBAAoB;AAChC,MAAI,OAAOA,cAAG,MAAC,WAAY;AAC3B,MAAI,KAAK,SAAS;AACjB,SAAK,QAAQC,wCAAI;AAAA,EACjB;AACD,MAAI,KAAK,SAAS;AACjB,cAAU,KAAK,OAAO;AAAA,EACtB;AACF;;"}