"use strict";
const common_vendor = require("../common/vendor.js");
const utils_config = require("./config.js");
const api_images = require("../api/images.js");
function getFullImageUrl(url) {
  if (!url)
    return "";
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }
  const cleanUrl = url.startsWith("/") ? url.substring(1) : url;
  const baseUrl = utils_config.config.apiBaseUrl.endsWith("/") ? utils_config.config.apiBaseUrl.substring(0, utils_config.config.apiBaseUrl.length - 1) : utils_config.config.apiBaseUrl;
  return `${baseUrl}/${cleanUrl}`;
}
async function fetchImageById(imageId) {
  if (!imageId)
    return null;
  try {
    const result = await api_images.getImageInfo(imageId);
    if (result && result.success && result.data) {
      return result.data;
    }
    return null;
  } catch (error) {
    common_vendor.index.__f__("error", "at utils/image.js:48", "获取图片信息失败:", error);
    return null;
  }
}
exports.fetchImageById = fetchImageById;
exports.getFullImageUrl = getFullImageUrl;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/image.js.map
