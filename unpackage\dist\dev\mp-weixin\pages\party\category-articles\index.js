"use strict";
const common_vendor = require("../../../common/vendor.js");
const api_categories = require("../../../api/categories.js");
const CustomNavbar = () => "../../../components/common/custom-navbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      categoryId: null,
      category: {},
      articleList: []
    };
  },
  onLoad(options) {
    if (options.id) {
      this.categoryId = options.id;
      common_vendor.index.__f__("log", "at pages/party/category-articles/index.vue:59", "分类ID:", this.categoryId);
      this.fetchCategoryArticles();
    } else {
      common_vendor.index.showToast({
        title: "参数错误",
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取分类文章列表
    async fetchCategoryArticles() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const result = await api_categories.getCategoryArticles(this.categoryId);
        common_vendor.index.__f__("log", "at pages/party/category-articles/index.vue:81", "获取分类文章列表成功:", result);
        if (result && result.success) {
          if (result.data.category) {
            this.category = result.data.categoryId;
          }
          if (Array.isArray(result.data)) {
            this.articleList = result.data;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/party/category-articles/index.vue:95", "获取分类文章列表失败:", error);
        common_vendor.index.showToast({
          title: "获取文章列表失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 文章点击事件
    onArticleClick(item) {
      common_vendor.index.__f__("log", "at pages/party/category-articles/index.vue:106", "点击了文章:", item);
      common_vendor.index.__f__("log", "at pages/party/category-articles/index.vue:109", "跳转到党建文章详情页");
      common_vendor.index.navigateTo({
        url: `/pages/article/detail?id=${item.id}&type=party`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/party/category-articles/index.vue:113", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    }
    // 返回上一页方法已由导航栏组件自动处理
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _easycom_u_cell2 = common_vendor.resolveComponent("u-cell");
  const _easycom_u_list2 = common_vendor.resolveComponent("u-list");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _component_custom_navbar + _easycom_u_cell2 + _easycom_u_list2 + _easycom_u_empty2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../../components/u-icon/u-icon.js";
const _easycom_u_cell = () => "../../../uview-plus_3.4.28/components/u-cell/u-cell.js";
const _easycom_u_list = () => "../../../uview-plus_3.4.28/components/u-list/u-list.js";
const _easycom_u_empty = () => "../../../uview-plus_3.4.28/components/u-empty/u-empty.js";
const _easycom_u_button = () => "../../../uview-plus_3.4.28/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_cell + _easycom_u_list + _easycom_u_empty + _easycom_u_button)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      name: "more-dot-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    b: common_vendor.p({
      title: $data.category.name || "文章列表",
      showBack: true,
      showHome: true
    }),
    c: common_vendor.p({
      name: "list",
      color: "#D9001B",
      size: "20"
    }),
    d: common_vendor.f($data.articleList, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.onArticleClick(item), index),
        c: "feafa478-4-" + i0 + ",feafa478-3",
        d: common_vendor.p({
          title: item.title,
          titleStyle: {
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          },
          isLink: true
        })
      };
    }),
    e: common_vendor.p({
      ["enable-flex"]: true
    }),
    f: $data.articleList.length === 0
  }, $data.articleList.length === 0 ? {
    g: common_vendor.p({
      mode: "list",
      text: "暂无文章"
    })
  } : {}, {
    h: common_vendor.o(() => _ctx.uni.navigateBack()),
    i: common_vendor.p({
      type: "primary",
      text: "返回上一页",
      customStyle: {
        backgroundColor: "#D9001B"
      }
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/party/category-articles/index.js.map
