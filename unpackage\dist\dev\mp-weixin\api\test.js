"use strict";
const common_vendor = require("../common/vendor.js");
const api_news = require("./news.js");
function testGetArticleList() {
  common_vendor.index.__f__("log", "at api/test.js:7", "开始测试获取文章列表...");
  api_news.getArticleList().then((res) => {
    common_vendor.index.__f__("log", "at api/test.js:11", "获取文章列表成功:", res);
    return res;
  }).catch((err) => {
    common_vendor.index.__f__("error", "at api/test.js:15", "获取文章列表失败:", err);
  });
}
exports.testGetArticleList = testGetArticleList;
//# sourceMappingURL=../../.sourcemap/mp-weixin/api/test.js.map
