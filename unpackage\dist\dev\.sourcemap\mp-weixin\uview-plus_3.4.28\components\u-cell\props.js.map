{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-cell/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 标题\n        title: {\n            type: [String, Number],\n            default: () => defProps.cell.title\n        },\n        // 标题下方的描述信息\n        label: {\n            type: [String, Number],\n            default: () => defProps.cell.label\n        },\n        // 右侧的内容\n        value: {\n            type: [String, Number],\n            default: () => defProps.cell.value\n        },\n        // 左侧图标名称，或者图片链接(本地文件建议使用绝对地址)\n        icon: {\n            type: String,\n            default: () => defProps.cell.icon\n        },\n        // 是否禁用cell\n        disabled: {\n            type: Boolean,\n            default: () => defProps.cell.disabled\n        },\n        // 是否显示下边框\n        border: {\n            type: Boolean,\n            default: () => defProps.cell.border\n        },\n        // 内容是否垂直居中(主要是针对右侧的value部分)\n        center: {\n            type: Boolean,\n            default: () => defProps.cell.center\n        },\n        // 点击后跳转的URL地址\n        url: {\n            type: String,\n            default: () => defProps.cell.url\n        },\n        // 链接跳转的方式，内部使用的是uView封装的route方法，可能会进行拦截操作\n        linkType: {\n            type: String,\n            default: () => defProps.cell.linkType\n        },\n        // 是否开启点击反馈(表现为点击时加上灰色背景)\n        clickable: {\n            type: Boolean,\n            default: () => defProps.cell.clickable\n        },\n        // 是否展示右侧箭头并开启点击反馈\n        isLink: {\n            type: Boolean,\n            default: () => defProps.cell.isLink\n        },\n        // 是否显示表单状态下的必填星号(此组件可能会内嵌入input组件)\n        required: {\n            type: Boolean,\n            default: () => defProps.cell.required\n        },\n        // 右侧的图标箭头\n        rightIcon: {\n            type: String,\n            default: () => defProps.cell.rightIcon\n        },\n        // 右侧箭头的方向，可选值为：left，up，down\n        arrowDirection: {\n            type: String,\n            default: () => defProps.cell.arrowDirection\n        },\n        // 左侧图标样式\n        iconStyle: {\n            type: [Object, String],\n            default: () => {\n\t\t\t\treturn defProps.cell.iconStyle\n\t\t\t}\n        },\n        // 右侧箭头图标的样式\n        rightIconStyle: {\n            type: [Object, String],\n            default: () => {\n\t\t\t\treturn defProps.cell.rightIconStyle\n\t\t\t}\n        },\n        // 标题的样式\n        titleStyle: {\n            type: [Object, String],\n\t\t\tdefault: () => {\n\t\t\t\treturn defProps.cell.titleStyle\n\t\t\t}\n        },\n        // 单位元的大小，可选值为large\n        size: {\n            type: String,\n            default: () => defProps.cell.size\n        },\n        // 点击cell是否阻止事件传播\n        stop: {\n            type: Boolean,\n            default: () => defProps.cell.stop\n        },\n        // 标识符，cell被点击时返回\n        name: {\n            type: [Number, String],\n            default: () => defProps.cell.name\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM;AACvB,eAAOA,mCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAM;AACvB,eAAOA,mCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MAC9B,SAAS,MAAM;AACd,eAAOA,mCAAAA,MAAS,KAAK;AAAA,MACrB;AAAA,IACK;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}