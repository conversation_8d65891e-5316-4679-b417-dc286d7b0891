"use strict";
const Picker = {
  // picker
  picker: {
    show: false,
    popupMode: "bottom",
    showToolbar: true,
    title: "",
    columns: [],
    loading: false,
    itemHeight: 44,
    cancelText: "取消",
    confirmText: "确定",
    cancelColor: "#909193",
    confirmColor: "",
    visibleItemCount: 5,
    keyName: "text",
    closeOnClickOverlay: false,
    defaultIndex: [],
    immediateChange: true,
    zIndex: 10076,
    disabled: false,
    disabledColor: "",
    placeholder: "请选择",
    inputProps: {}
  }
};
exports.Picker = Picker;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-picker/picker.js.map
