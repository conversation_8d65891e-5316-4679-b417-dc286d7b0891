<template>
    <view class="wrap">
        <up-waterfall v-model="flowList" ref="uWaterfallRef">
            <template v-slot:left="{ leftList }">
                <view class="demo-warter" v-for="(item, index) in leftList" :key="index">
                    <!-- 微信小程序需要hx2.8.11版本才支持在template中引入其他组件，比如下方的u-lazy-load组件 -->
                    <up-lazy-load threshold="-450" height="120" border-radius="10" :image="item.image"
                        :index="index"></up-lazy-load>
                    <view class="demo-title">{{ item.title }}</view>
                    <view class="demo-price">{{ item.price }}元</view>
                    <view class="demo-tag">
                        <view class="demo-tag-owner">
							<text class="text">自营</text>
						</view>
                        <view class="demo-tag-text">
							<text class="text">放心购</text>
						</view>
                    </view>
                    <view class="demo-shop">{{ item.shop }}</view>
                    <view class="u-close">
                        <up-icon name="close-circle-fill" color="#fa3534" size="16" @click="remove(item.id)"></up-icon>
                    </view>
                </view>
            </template>
            <template v-slot:right="{ rightList }">
                <view class="demo-warter" v-for="(item, index) in rightList" :key="index">
                    <up-lazy-load threshold="-450" height="120" border-radius="10" :image="item.image"
                        :index="index"></up-lazy-load>
                    <view class="demo-title">{{ item.title }}</view>
                    <view class="demo-price">{{ item.price }}元</view>
                    <view class="demo-tag">
                        <view class="demo-tag-owner">
                        	<text class="text">自营</text>
                        </view>
                        <view class="demo-tag-text">
                        	<text class="text">放心购</text>
                        </view>
                    </view>
                    <view class="demo-shop">{{ item.shop }}</view>
                    <view class="u-close">
                        <up-icon name="close-circle-fill" color="#fa3534" size="34" @click="remove(item.id)"></up-icon>
                    </view>
                </view>
            </template>
        </up-waterfall>
        <up-loadmore bg-color="rgb(240, 240, 240)" :status="loadStatus" @loadmore="addRandomData"></up-loadmore>
    </view>
</template>

<script>
    import {
        random,
        guid
    } from '@/uni_modules/uview-plus';
    export default {
        data() {
            return {
                loadStatus: 'loadmore',
                flowList: [],
                list: [{
                        price: 35,
                        title: '北国风光，千里冰封，万里雪飘',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png'
                    },
                    {
                        price: 75,
                        title: '望长城内外，惟余莽莽',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png'
                    },
                    {
                        price: 385,
                        title: '大河上下，顿失滔滔',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png'
                    },
                    {
                        price: 784,
                        title: '欲与天公试比高',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png'
                    },
                    {
                        price: 7891,
                        title: '须晴日，看红装素裹，分外妖娆',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png'
                    },
                    {
                        price: 2341,
                        shop: '李白杜甫白居易旗舰店',
                        title: '江山如此多娇，引无数英雄竞折腰',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png'
                    },
                    {
                        price: 661,
                        shop: '李白杜甫白居易旗舰店',
                        title: '惜秦皇汉武，略输文采',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png'
                    },
                    {
                        price: 1654,
                        title: '唐宗宋祖，稍逊风骚',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png'
                    },
                    {
                        price: 1678,
                        title: '一代天骄，成吉思汗',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper3.png'
                    },
                    {
                        price: 924,
                        title: '只识弯弓射大雕',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper1.png'
                    },
                    {
                        price: 8243,
                        title: '俱往矣，数风流人物，还看今朝',
                        shop: '李白杜甫白居易旗舰店',
                        image: 'https://uview-plus.jiangruyi.com/uview/swiper/swiper2.png'
                    }
                ]
            };
        },
        onLoad() {
            this.addRandomData();
        },
        onReachBottom() {
            this.loadStatus = 'loading';
            // 模拟数据加载
            setTimeout(() => {
                this.addRandomData();
                this.loadStatus = 'loadmore';
            }, 1000);
        },
        methods: {
            addRandomData() {
                for (let i = 0; i < 10; i++) {
                    let index = random(0, this.list.length - 1);
                    // 先转成字符串再转成对象，避免数组对象引用导致数据混乱
                    let item = JSON.parse(JSON.stringify(this.list[index]));
                    item.id = guid();
                    this.flowList.push(item);
                }
            },
            remove(id) {
                this.$refs.uWaterfallRef.remove(id);
            },
            clear() {
                this.$refs.uWaterfallRef.clear();
            }
        }
    };
</script>

<style>
    /* page不能写带scope的style标签中，否则无效 */
	/* #ifndef APP-NVUE */
	page {
	    background-color: rgb(240, 240, 240);
	}
	/* #endif */
</style>

<style lang="scss" scoped>
    .demo-warter {
        border-radius: 8px;
        margin: 5px;
        background-color: #ffffff;
        padding: 8px;
        position: relative;
        /* #ifdef H5 */
        cursor: pointer;
        /* #endif */
        .u-close {
            position: absolute;
            top: -7px;
            right: 3px;
            opacity: 0;
        }
        /* #ifdef H5 */
        &:hover {
            .u-close {
                opacity: 1;
            }
        }
        /* #endif */
    }

    .demo-img-wrap {}

    .demo-image {
        width: 100%;
        border-radius: 4px;
    }

    .demo-title {
        font-size: 30rpx;
        margin-top: 5px;
        color: $u-main-color;
        /* #ifndef APP-NVUE */
        word-break: break-all;
        /* #endif */
    }

    .demo-tag {
        display: flex;
        flex-direction: row;
        margin-top: 5px;
    }

    .demo-tag-owner {
        background-color: $u-error;
        display: flex;
        align-items: center;
		justify-content: center;
        padding: 2px 7px;
        border-radius: 20px;
		line-height: 1;
		.text {
			font-size: 12px;
			color: #ffffff;
		}
    }

    .demo-tag-text {
        border: 1px solid $u-primary;
        margin-left: 10px;
        border-radius: 50rpx;
        line-height: 1;
        padding: 2px 7px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        border-radius: 20px;
		.text {
			font-size: 12px;
			 color: $u-primary;
		}
    }

    .demo-price {
        font-size: 30rpx;
        color: $u-error;
        margin-top: 5px;
    }

    .demo-shop {
        font-size: 22rpx;
        color: $u-tips-color;
        margin-top: 5px;
    }
</style>