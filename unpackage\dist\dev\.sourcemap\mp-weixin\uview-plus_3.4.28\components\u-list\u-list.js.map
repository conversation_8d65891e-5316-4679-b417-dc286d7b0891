{"version": 3, "file": "u-list.js", "sources": ["uview-plus_3.4.28/components/u-list/u-list.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtbGlzdC91LWxpc3QudnVl"], "sourcesContent": ["<template>\n\t<!-- #ifdef APP-NVUE -->\n\t<list\n\t\tclass=\"u-list\"\n\t\t:enableBackToTop=\"enableBackToTop\"\n\t\t:loadmoreoffset=\"lowerThreshold\"\n\t\t:showScrollbar=\"showScrollbar\"\n\t\t:style=\"[listStyle]\"\n\t\t:offset-accuracy=\"Number(offsetAccuracy)\"\n\t\t@scroll=\"onScroll\"\n\t\t@loadmore=\"scrolltolower\"\n\t>\n\t\t<slot />\n\t</list>\n\t<!-- #endif -->\n\t<!-- #ifndef APP-NVUE -->\n\t<scroll-view\n\t\tclass=\"u-list\"\n\t\t:scroll-into-view=\"scrollIntoView\"\n\t\t:style=\"[listStyle]\"\n\t\t:scroll-y=\"scrollable\"\n\t\t:scroll-top=\"Number(scrollTop)\"\n\t\t:lower-threshold=\"Number(lowerThreshold)\"\n\t\t:upper-threshold=\"Number(upperThreshold)\"\n\t\t:show-scrollbar=\"showScrollbar\"\n\t\t:enable-back-to-top=\"enableBackToTop\"\n\t\t:scroll-with-animation=\"scrollWithAnimation\"\n\t\t@scroll=\"onScroll\"\n\t\t@scrolltolower=\"scrolltolower\"\n\t\t@scrolltoupper=\"scrolltoupper\"\n\t\t:refresher-enabled=\"refresherEnabled\"\n\t\t:refresher-threshold=\"refresherThreshold\"\n\t\t:refresher-default-style=\"refresherDefaultStyle\"\n\t\t:refresher-background=\"refresherBackground\"\n\t\t:refresher-triggered=\"refresherTriggered\"\n\t\t@refresherpulling=\"refresherpulling\"\n\t\t@refresherrefresh=\"refresherrefresh\"\n\t\t@refresherrestore=\"refresherrestore\"\n\t\t@refresherabort=\"refresherabort\"\n\t\t:scroll-anchoring=\"true\"\n\t>\n\t\t<view>\n\t\t\t<slot />\n\t\t</view>\n\t</scroll-view>\n\t<!-- #endif -->\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge, sleep, getWindowInfo } from '../../libs/function/index';\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * List 列表\n\t * @description 该组件为高性能列表组件\n\t * @tutorial https://ijry.github.io/uview-plus/components/list.html\n\t * @property {Boolean}\t\t\tshowScrollbar\t\t控制是否出现滚动条，仅nvue有效 （默认 false ）\n\t * @property {String ｜ Number}\tlowerThreshold\t\t距底部多少时触发scrolltolower事件 （默认 50 ）\n\t * @property {String ｜ Number}\tupperThreshold\t\t距顶部多少时触发scrolltoupper事件，非nvue有效 （默认 0 ）\n\t * @property {String ｜ Number}\tscrollTop\t\t\t设置竖向滚动条位置（默认 0 ）\n\t * @property {String ｜ Number}\toffsetAccuracy\t\t控制 onscroll 事件触发的频率，仅nvue有效（默认 10 ）\n\t * @property {Boolean}\t\t\tenableFlex\t\t\t启用 flexbox 布局。开启后，当前节点声明了display: flex就会成为flex container，并作用于其孩子节点，仅微信小程序有效（默认 false ）\n\t * @property {Boolean}\t\t\tpagingEnabled\t\t是否按分页模式显示List，（默认 false ）\n\t * @property {Boolean}\t\t\tscrollable\t\t\t是否允许List滚动（默认 true ）\n\t * @property {String}\t\t\tscrollIntoView\t\t值应为某子元素id（id不能以数字开头）\n\t * @property {Boolean}\t\t\tscrollWithAnimation\t在设置滚动条位置时使用动画过渡 （默认 false ）\n\t * @property {Boolean}\t\t\tenableBackToTop\t\tiOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只对微信小程序有效 （默认 false ）\n\t * @property {String ｜ Number}\theight\t\t\t\t列表的高度 （默认 0 ）\n\t * @property {String ｜ Number}\twidth\t\t\t\t列表宽度 （默认 0 ）\n\t * @property {String ｜ Number}\tpreLoadScreen\t\t列表前后预渲染的屏数，1代表一个屏幕的高度，1.5代表1个半屏幕高度  （默认 1 ）\n\t * @property {Object}\t\t\tcustomStyle\t\t\t定义需要用到的外部样式\n\t *\n\t * @example <u-list @scrolltolower=\"scrolltolower\"></u-list>\n\t */\n\texport default {\n\t\tname: 'u-list',\n\t\tmixins: [mpMixin, mixin, props],\n\t\twatch: {\n\t\t\tscrollIntoView(n) {\n\t\t\t\tthis.scrollIntoViewById(n)\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 记录内部滚动的距离\n\t\t\t\tinnerScrollTop: 0,\n\t\t\t\t// vue下，scroll-view在上拉加载时的偏移值\n\t\t\t\toffset: 0,\n\t\t\t\tsys: getWindowInfo()\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tlistStyle() {\n\t\t\t\tconst style = {};\n\t\t\t\tif (this.width != 0) style.width = addUnit(this.width)\n\t\t\t\tif (this.height != 0) style.height = addUnit(this.height)\n\t\t\t\t// 如果没有定义列表高度，则默认使用屏幕高度\n\t\t\t\tif (!style.height) style.height = addUnit(this.sys.windowHeight, 'px')\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\tprovide() {\n\t\t\treturn {\n\t\t\t\tuList: this\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.refs = []\n\t\t\tthis.children = []\n\t\t\tthis.anchors = []\n\t\t},\n\t\tmounted() {},\n\t\temits: [\"scroll\", \"scrolltolower\", \"scrolltoupper\",\n\t\t\t\"refresherpulling\", \"refresherrefresh\", \"refresherrestore\", \"refresherabort\"],\n\t\tmethods: {\n\t\t\tupdateOffsetFromChild(top) {\n\t\t\t\tthis.offset = top\n\t\t\t},\n\t\t\tonScroll(e) {\n\t\t\t\tlet scrollTop = 0\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tscrollTop = e.contentOffset.y\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tscrollTop = e.detail.scrollTop\n\t\t\t\t// #endif\n\t\t\t\tthis.innerScrollTop = scrollTop\n\t\t\t\tthis.$emit('scroll', scrollTop)\n\t\t\t},\n\t\t\tscrollIntoViewById(id) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 根据id参数，找到所有u-list-item中匹配的节点，再通过dom模块滚动到对应的位置\n\t\t\t\tconst item = this.refs.find(item => item.$refs[id] ? true : false)\n\t\t\t\tdom.scrollToElement(item.$refs[id], {\n\t\t\t\t\t// 是否需要滚动动画\n\t\t\t\t\tanimated: this.scrollWithAnimation\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t// 滚动到底部触发事件\n\t\t\tscrolltolower(e) {\n\t\t\t\tsleep(30).then(() => {\n\t\t\t\t\tthis.$emit('scrolltolower')\n\t\t\t\t})\n\t\t\t},\n\t\t\t// #ifndef APP-NVUE\n\t\t\t// 滚动到底部时触发，非nvue有效\n\t\t\tscrolltoupper(e) {\n\t\t\t\tsleep(30).then(() => {\n\t\t\t\t\tthis.$emit('scrolltoupper')\n\t\t\t\t\t// 这一句很重要，能绝对保证在性功能障碍的webview，滚动条到顶时，取消偏移值，让页面置顶\n\t\t\t\t\tthis.offset = 0\n\t\t\t\t})\n\t\t\t},\n\t\t\trefresherpulling(e) {\n\t\t\t\tthis.$emit('refresherpulling', e)\n\t\t\t},\n\t\t\trefresherrefresh(e) {\n\t\t\t\tthis.$emit('refresherrefresh', e)\n\t\t\t},\n\t\t\trefresherrestore(e) {\n\t\t\t\tthis.$emit('refresherrestore', e)\n\t\t\t},\n\t\t\trefresherabort(e) {\n\t\t\t\tthis.$emit('refresherabort', e)\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-list {\n\t\t@include flex(column);\n\n\t}\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-list/u-list.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "getWindowInfo", "addUnit", "deepMerge", "addStyle", "sleep"], "mappings": ";;;;;;AA8EC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,6CAAK;AAAA,EAC9B,OAAO;AAAA,IACN,eAAe,GAAG;AACjB,WAAK,mBAAmB,CAAC;AAAA,IAC1B;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,gBAAgB;AAAA;AAAA,MAEhB,QAAQ;AAAA,MACR,KAAKC,qCAAAA,cAAc;AAAA,IACpB;AAAA,EACA;AAAA,EACD,UAAU;AAAA,IACT,YAAY;AACX,YAAM,QAAQ,CAAA;AACd,UAAI,KAAK,SAAS;AAAG,cAAM,QAAQC,qCAAO,QAAC,KAAK,KAAK;AACrD,UAAI,KAAK,UAAU;AAAG,cAAM,SAASA,qCAAO,QAAC,KAAK,MAAM;AAExD,UAAI,CAAC,MAAM;AAAQ,cAAM,SAASA,qCAAO,QAAC,KAAK,IAAI,cAAc,IAAI;AACrE,aAAOC,qCAAS,UAAC,OAAOC,qCAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AAAA,EACD,UAAU;AACT,WAAO;AAAA,MACN,OAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,UAAU;AACT,SAAK,OAAO,CAAC;AACb,SAAK,WAAW,CAAC;AACjB,SAAK,UAAU,CAAC;AAAA,EAChB;AAAA,EACD,UAAU;AAAA,EAAE;AAAA,EACZ,OAAO;AAAA,IAAC;AAAA,IAAU;AAAA,IAAiB;AAAA,IAClC;AAAA,IAAoB;AAAA,IAAoB;AAAA,IAAoB;AAAA,EAAgB;AAAA,EAC7E,SAAS;AAAA,IACR,sBAAsB,KAAK;AAC1B,WAAK,SAAS;AAAA,IACd;AAAA,IACD,SAAS,GAAG;AACX,UAAI,YAAY;AAKhB,kBAAY,EAAE,OAAO;AAErB,WAAK,iBAAiB;AACtB,WAAK,MAAM,UAAU,SAAS;AAAA,IAC9B;AAAA,IACD,mBAAmB,IAAI;AAAA,IAStB;AAAA;AAAA,IAED,cAAc,GAAG;AAChBC,iDAAM,EAAE,EAAE,KAAK,MAAM;AACpB,aAAK,MAAM,eAAe;AAAA,OAC1B;AAAA,IACD;AAAA;AAAA,IAGD,cAAc,GAAG;AAChBA,iDAAM,EAAE,EAAE,KAAK,MAAM;AACpB,aAAK,MAAM,eAAe;AAE1B,aAAK,SAAS;AAAA,OACd;AAAA,IACD;AAAA,IACD,iBAAiB,GAAG;AACnB,WAAK,MAAM,oBAAoB,CAAC;AAAA,IAChC;AAAA,IACD,iBAAiB,GAAG;AACnB,WAAK,MAAM,oBAAoB,CAAC;AAAA,IAChC;AAAA,IACD,iBAAiB,GAAG;AACnB,WAAK,MAAM,oBAAoB,CAAC;AAAA,IAChC;AAAA,IACD,eAAe,GAAG;AACjB,WAAK,MAAM,kBAAkB,CAAC;AAAA,IAC/B;AAAA,EAEA;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3KD,GAAG,gBAAgB,SAAS;"}