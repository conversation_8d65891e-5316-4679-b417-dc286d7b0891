{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-tabs/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\nexport const props = defineMixin({\r\n    props: {\r\n        // 滑块的移动过渡时间，单位ms\r\n        duration: {\r\n            type: Number,\r\n            default: () => defProps.tabs.duration\r\n        },\r\n        // tabs标签数组\r\n        list: {\r\n            type: Array,\r\n            default: () => defProps.tabs.list\r\n        },\r\n        // 滑块颜色\r\n        lineColor: {\r\n            type: String,\r\n            default: () => defProps.tabs.lineColor\r\n        },\r\n        // 菜单选择中时的样式\r\n        activeStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.tabs.activeStyle\r\n        },\r\n        // 菜单非选中时的样式\r\n        inactiveStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.tabs.inactiveStyle\r\n        },\r\n        // 滑块长度\r\n        lineWidth: {\r\n            type: [String, Number],\r\n            default: () => defProps.tabs.lineWidth\r\n        },\r\n        // 滑块高度\r\n        lineHeight: {\r\n            type: [String, Number],\r\n            default: () => defProps.tabs.lineHeight\r\n        },\r\n        // 滑块背景显示大小，当滑块背景设置为图片时使用\r\n        lineBgSize: {\r\n            type: String,\r\n            default: () => defProps.tabs.lineBgSize\r\n        },\r\n        // 菜单item的样式\r\n        itemStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.tabs.itemStyle\r\n        },\r\n        // 菜单是否可滚动\r\n        scrollable: {\r\n            type: Boolean,\r\n            default: () => defProps.tabs.scrollable\r\n        },\r\n\t\t// 当前选中标签的索引\r\n\t\tcurrent: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: () => defProps.tabs.current\r\n\t\t},\r\n\t\t// 默认读取的键名\r\n\t\tkeyName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.tabs.keyName\r\n\t\t},\r\n        // 左侧图标样式\r\n        iconStyle: {\r\n            type: [String, Object],\r\n            default: () => defProps.tabs.iconStyle\r\n        }\r\n    }\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAEP,SAAS;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAC7B;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAC7B;AAAA;AAAA,IAEK,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}