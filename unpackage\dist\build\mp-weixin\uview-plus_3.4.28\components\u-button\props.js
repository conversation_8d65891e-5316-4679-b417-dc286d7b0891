"use strict";const t=require("../../libs/vue.js"),e=require("../../libs/config/props.js"),o=t.defineMixin({props:{hairline:{type:<PERSON><PERSON>an,default:()=>e.props.button.hairline},type:{type:String,default:()=>e.props.button.type},size:{type:String,default:()=>e.props.button.size},shape:{type:String,default:()=>e.props.button.shape},plain:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.button.plain},disabled:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.button.disabled},loading:{type:<PERSON><PERSON><PERSON>,default:()=>e.props.button.loading},loadingText:{type:[String,Number],default:()=>e.props.button.loadingText},loadingMode:{type:String,default:()=>e.props.button.loadingMode},loadingSize:{type:[String,Number],default:()=>e.props.button.loadingSize},openType:{type:String,default:()=>e.props.button.openType},formType:{type:String,default:()=>e.props.button.formType},appParameter:{type:String,default:()=>e.props.button.appParameter},hoverStopPropagation:{type:Boolean,default:()=>e.props.button.hoverStopPropagation},lang:{type:String,default:()=>e.props.button.lang},sessionFrom:{type:String,default:()=>e.props.button.sessionFrom},sendMessageTitle:{type:String,default:()=>e.props.button.sendMessageTitle},sendMessagePath:{type:String,default:()=>e.props.button.sendMessagePath},sendMessageImg:{type:String,default:()=>e.props.button.sendMessageImg},showMessageCard:{type:Boolean,default:()=>e.props.button.showMessageCard},dataName:{type:String,default:()=>e.props.button.dataName},throttleTime:{type:[String,Number],default:()=>e.props.button.throttleTime},hoverStartTime:{type:[String,Number],default:()=>e.props.button.hoverStartTime},hoverStayTime:{type:[String,Number],default:()=>e.props.button.hoverStayTime},text:{type:[String,Number],default:()=>e.props.button.text},icon:{type:String,default:()=>e.props.button.icon},iconColor:{type:String,default:()=>e.props.button.icon},color:{type:String,default:()=>e.props.button.color},stop:{type:Boolean,default:()=>e.props.button.stop}}});exports.props=o;
