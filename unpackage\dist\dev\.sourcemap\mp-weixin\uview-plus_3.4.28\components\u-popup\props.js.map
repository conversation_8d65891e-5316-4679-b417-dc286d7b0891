{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-popup/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否展示弹窗\n        show: {\n            type: Boolean,\n            default: () => defProps.popup.show\n        },\n        // 是否显示遮罩\n        overlay: {\n            type: Boolean,\n            default: () => defProps.popup.overlay\n        },\n        // 弹出的方向，可选值为 top bottom right left center\n        mode: {\n            type: String,\n            default: () => defProps.popup.mode\n        },\n        // 动画时长，单位ms\n        duration: {\n            type: [String, Number],\n            default: () => defProps.popup.duration\n        },\n        // 是否显示关闭图标\n        closeable: {\n            type: Boolean,\n            default: () => defProps.popup.closeable\n        },\n        // 自定义遮罩的样式\n        overlayStyle: {\n            type: [Object, String],\n            default: () => defProps.popup.overlayStyle\n        },\n        // 点击遮罩是否关闭弹窗\n        closeOnClickOverlay: {\n            type: Boolean,\n            default: () => defProps.popup.closeOnClickOverlay\n        },\n        // 层级\n        zIndex: {\n            type: [String, Number],\n            default: () => defProps.popup.zIndex\n        },\n        // 是否为iPhoneX留出底部安全距离\n        safeAreaInsetBottom: {\n            type: Boolean,\n            default: () => defProps.popup.safeAreaInsetBottom\n        },\n        // 是否留出顶部安全距离（状态栏高度）\n        safeAreaInsetTop: {\n            type: Boolean,\n            default: () => defProps.popup.safeAreaInsetTop\n        },\n        // 自定义关闭图标位置，top-left为左上角，top-right为右上角，bottom-left为左下角，bottom-right为右下角\n        closeIconPos: {\n            type: String,\n            default: () => defProps.popup.closeIconPos\n        },\n        // 是否显示圆角\n        round: {\n            type: [Boolean, String, Number],\n            default: () => defProps.popup.round\n        },\n        // mode=center，也即中部弹出时，是否使用缩放模式\n        zoom: {\n            type: Boolean,\n            default: () => defProps.popup.zoom\n        },\n        // 弹窗背景色，设置为transparent可去除白色背景\n        bgColor: {\n            type: String,\n            default: () => defProps.popup.bgColor\n        },\n        // 遮罩的透明度，0-1之间\n        overlayOpacity: {\n            type: [Number, String],\n            default: () => defProps.popup.overlayOpacity\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,SAAS,QAAQ,MAAM;AAAA,MAC9B,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}