"use strict";function t(t="rgb(0, 0, 0)",n="rgb(255, 255, 255)",o=10){const l=e(t,!1),f=l[0],s=l[1],i=l[2],c=e(n,!1),g=(c[0]-f)/o,a=(c[1]-s)/o,u=(c[2]-i)/o,h=[];for(let e=0;e<o;e++){let l=r(`rgb(${Math.round(g*e+f)},${Math.round(a*e+s)},${Math.round(u*e+i)})`);0===e&&(l=r(t)),e===o-1&&(l=r(n)),h.push(l)}return h}function e(t,e=!0){if((t=String(t).toLowerCase())&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(t)){if(4===t.length){let e="#";for(let r=1;r<4;r+=1)e+=t.slice(r,r+1).concat(t.slice(r,r+1));t=e}const r=[];for(let e=1;e<7;e+=2)r.push(parseInt(`0x${t.slice(e,e+2)}`));return e?`rgb(${r[0]},${r[1]},${r[2]})`:r}if(/^(rgb|RGB)/.test(t)){return t.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map((t=>Number(t)))}return t}function r(t){const e=t;if(/^(rgb|RGB)/.test(e)){const t=e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",");let r="#";for(let e=0;e<t.length;e++){let n=Number(t[e]).toString(16);n=1==String(n).length?`0${n}`:n,"0"===n&&(n+=n),r+=n}return 7!==r.length&&(r=e),r}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e))return e;{const t=e.replace(/#/,"").split("");if(6===t.length)return e;if(3===t.length){let e="#";for(let r=0;r<t.length;r+=1)e+=t[r]+t[r];return e}}}const n={colorGradient:t,hexToRgb:e,rgbToHex:r,colorToRgba:function(t,e){t=r(t);let n=String(t).toLowerCase();if(n&&/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(n)){if(4===n.length){let t="#";for(let e=1;e<4;e+=1)t+=n.slice(e,e+1).concat(n.slice(e,e+1));n=t}const t=[];for(let e=1;e<7;e+=2)t.push(parseInt(`0x${n.slice(e,e+2)}`));return`rgba(${t.join(",")},${e})`}return n}};exports.colorGradient=n,exports.colorGradient$1=t;
