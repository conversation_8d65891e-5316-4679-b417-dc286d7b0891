{"version": 3, "file": "avatarGroup.js", "sources": ["uview-plus_3.4.28/components/u-avatar-group/avatarGroup.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:49:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/avatarGroup.js\n */\nexport default {\n    // avatarGroup 组件\n    avatarGroup: {\n        urls: [],\n        maxCount: 5,\n        shape: 'circle',\n        mode: 'scaleToFill',\n        showMore: true,\n        size: 40,\n        keyName: '',\n        gap: 0.5,\n\t\textraValue: 0\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,MAAM,CAAE;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,KAAK;AAAA,IACX,YAAY;AAAA,EACT;AACL;;"}