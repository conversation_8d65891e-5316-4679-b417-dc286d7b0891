<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value1"
					:maxlength="4"
					@change="change"
					@finish="finish"
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">横线模式</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value2"
					mode="line"
					:maxlength="4"
					:bold="true"
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">设置长度</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value3"
					:maxlength="6"
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">设置间距</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value4"
					mode="box"
					:space="0"
					:maxlength="4"
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">细边框</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value5"
					mode="box"
					:space="0"
					:maxlength="4"
					hairline
				></up-code-input>
			</view>
			<view
				class="u-demo-block__content"
				style="margin-top: 10px;"
			>
				<up-code-input
					v-model="value6"
					mode="line"
					:space="10"
					:maxlength="4"
					hairline
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">调整颜色</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value7"
					mode="box"
					:space="0"
					:maxlength="4"
					hairline
					color="#f56c6c"
					borderColor="#f56c6c"
				></up-code-input>
				<view class="u-demo-block__content" style="margin-top: 10px;">
					<up-code-input
						v-model="value10"
						mode="line"
						size="30"
						:maxlength="4"
						hairline
						color="#3c9cff"
						borderColor="#3c9cff"
					></up-code-input>
				</view>

			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">点模式</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value8"
					mode="box"
					dot
					:space="0"
					:maxlength="4"
					hairline
				></up-code-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">预置内容</text>
			<view class="u-demo-block__content">
				<up-code-input
					v-model="value9"
					mode="box"
					:space="0"
					:maxlength="4"
					hairline
					fontSize="17"
				></up-code-input>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				value1: '',
				value2: '',
				value3: '',
				value4: '',
				value5: '',
				value6: '',
				value7: '',
				value8: '',
				value9: '123',
				value10: '34'
			}
		},
		methods: {
			change(e) {
				console.log('change', e);
			},
			finish(e) {
				console.log('finish', e);
			}
		},
	}
</script>

<style lang="scss">

</style>
