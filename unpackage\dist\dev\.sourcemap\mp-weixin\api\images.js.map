{"version": 3, "file": "images.js", "sources": ["api/images.js"], "sourcesContent": ["/**\n * 图片相关API\n */\nimport request from './request.js';\n\n/**\n * 获取图片信息\n * @param {number} imageId - 图片ID\n * @returns {Promise} - 返回图片信息\n */\nfunction getImageInfo(imageId) {\n  return request.get(`/api/images/${imageId}/info`);\n}\n\n// 导出所有API函数\nexport {\n  getImageInfo\n}\n"], "names": ["request"], "mappings": ";;AAUA,SAAS,aAAa,SAAS;AAC7B,SAAOA,YAAAA,QAAQ,IAAI,eAAe,OAAO,OAAO;AAClD;;"}