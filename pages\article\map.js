/**
 * 文章类型与API函数映射关系
 * 简单的JSON映射，只包含文章类型与API的对应关系
 */
import { articleApi, menuArticleApi, partyArticleApi } from '@/api/index.js';

/**
 * 文章类型与API的映射
 * @type {Object}
 */
export const articleApiMap = {
  // 新闻文章
  news: articleApi.getArticleDetail,

  // 菜单文章
  menu: menuArticleApi.getArticleDetail,

  // 党建文章
  party: partyArticleApi.getPartyArticleDetail
};

/**
 * 获取文章类型对应的API函数
 * @param {string} type - 文章类型
 * @returns {Function} - 对应的API函数
 */
export function getArticleApi(type) {
  return articleApiMap[type] || articleApiMap.news;
}
