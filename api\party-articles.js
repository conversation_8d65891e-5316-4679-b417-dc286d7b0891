import request from './request.js';

/**
 * 获取党建文章列表
 * @param {Object} params - 查询参数
 * @returns {Promise} - 返回Promise对象
 */
export function getPartyArticleList(params = {}) {
  return request.get('/api/party-articles/', params);
}

/**
 * 获取党建文章详情
 * @param {String|Number} id - 文章ID
 * @returns {Promise} - 返回Promise对象
 */
export function getPartyArticleDetail(id) {
  return request.get(`/api/party-articles/${id}`);
}
