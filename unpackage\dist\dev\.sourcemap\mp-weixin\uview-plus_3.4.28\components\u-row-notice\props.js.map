{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-row-notice/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 显示的内容，字符串\n        text: {\n            type: String,\n            default: () => defProps.rowNotice.text\n        },\n        // 是否显示左侧的音量图标\n        icon: {\n            type: String,\n            default: () => defProps.rowNotice.icon\n        },\n        // 通告模式，link-显示右箭头，closable-显示右侧关闭图标\n        mode: {\n            type: String,\n            default: () => defProps.rowNotice.mode\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: () => defProps.rowNotice.color\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: () => defProps.rowNotice.bgColor\n        },\n        // 字体大小，单位px\n        fontSize: {\n            type: [String, Number],\n            default: () => defProps.rowNotice.fontSize\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: [String, Number],\n            default: () => defProps.rowNotice.speed\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA,EACJ;AACL,CAAC;;"}