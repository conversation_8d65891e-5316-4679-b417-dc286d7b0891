"use strict";
const common_vendor = require("../../common/vendor.js");
const api_categories = require("../../api/categories.js");
const CustomNavbar = () => "../../components/common/custom-navbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      // 基层党组织数据
      categoryList: [],
      // 分类历史记录，用于返回上一级
      categoryHistory: [],
      // 当前分类层级
      currentLevel: 1,
      // 当前分类标题
      currentTitle: "基层党组织"
    };
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/party/index.vue:71", "党建工作页面加载");
    this.fetchCategoryList();
  },
  methods: {
    // 获取分类列表
    async fetchCategoryList() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const result = await api_categories.getCategoryTreeList();
        common_vendor.index.__f__("log", "at pages/party/index.vue:84", "获取分类树形列表成功:", result);
        if (result && result.success && Array.isArray(result.data)) {
          this.categoryList = result.data;
          common_vendor.index.__f__("log", "at pages/party/index.vue:88", "分类树形列表数据已更新");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/party/index.vue:91", "获取分类树形列表失败:", error);
        common_vendor.index.showToast({
          title: "获取分类数据失败",
          icon: "none"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    // 分类点击事件
    onCategoryClick(index) {
      const item = this.categoryList[index];
      common_vendor.index.__f__("log", "at pages/party/index.vue:103", "点击了基层党组织:", item);
      if (item.children && item.children.length > 0) {
        this.categoryHistory.push({
          list: [...this.categoryList],
          title: this.currentTitle,
          level: this.currentLevel
        });
        this.categoryList = item.children;
        this.currentTitle = item.name;
        this.currentLevel++;
        common_vendor.index.setNavigationBarTitle({
          title: item.name
        });
      } else {
        common_vendor.index.navigateTo({
          url: `/pages/party/category-articles/index?id=${item.id}`,
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/party/index.vue:130", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      }
    },
    // 返回上一级分类
    goBackToParent() {
      if (this.categoryHistory.length > 0) {
        const prevCategory = this.categoryHistory.pop();
        this.categoryList = prevCategory.list;
        this.currentTitle = prevCategory.title;
        this.currentLevel = prevCategory.level;
        common_vendor.index.setNavigationBarTitle({
          title: this.currentTitle
        });
        return true;
      }
      return false;
    },
    // 返回首页或上一级
    goBack() {
      if (!this.goBackToParent()) {
        common_vendor.index.navigateBack({
          delta: 1
        });
      }
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _easycom_u_cell2 = common_vendor.resolveComponent("u-cell");
  const _easycom_u_list2 = common_vendor.resolveComponent("u-list");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _component_custom_navbar + _easycom_u_cell2 + _easycom_u_list2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../components/u-icon/u-icon.js";
const _easycom_u_cell = () => "../../uview-plus_3.4.28/components/u-cell/u-cell.js";
const _easycom_u_list = () => "../../uview-plus_3.4.28/components/u-list/u-list.js";
const _easycom_u_button = () => "../../uview-plus_3.4.28/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_cell + _easycom_u_list + _easycom_u_button)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      name: "more-dot-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    b: common_vendor.o($options.goBack),
    c: common_vendor.p({
      title: "党建工作",
      showBack: true,
      showHome: true
    }),
    d: common_vendor.p({
      name: "grid-fill",
      color: "#D9001B",
      size: "20"
    }),
    e: common_vendor.t($data.currentTitle),
    f: common_vendor.f($data.categoryList, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.onCategoryClick(index), index),
        c: "239aaf27-4-" + i0 + ",239aaf27-3",
        d: common_vendor.p({
          title: item.name,
          titleStyle: {
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          },
          isLink: true
        })
      };
    }),
    g: common_vendor.p({
      ["enable-flex"]: true
    }),
    h: common_vendor.o($options.goBack),
    i: common_vendor.p({
      type: "primary",
      text: $data.categoryHistory.length > 0 ? "返回上一级" : "返回首页",
      customStyle: {
        backgroundColor: "#D9001B"
      }
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/party/index.js.map
