/**
 * 图片相关工具函数
 */
import config from './config.js';
import { getImageInfo } from '@/api/images.js';

/**
 * 获取完整的图片URL
 * @param {string} url - 图片相对路径或完整URL
 * @returns {string} - 返回完整的图片URL
 */
export function getFullImageUrl(url) {
  if (!url) return '';

  // 如果已经是完整URL，则直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 规范化URL路径
  // 1. 移除开头的斜杠（如果有）
  const cleanUrl = url.startsWith('/') ? url.substring(1) : url;

  // 2. 确保apiBaseUrl不以斜杠结尾
  const baseUrl = config.apiBaseUrl.endsWith('/')
    ? config.apiBaseUrl.substring(0, config.apiBaseUrl.length - 1)
    : config.apiBaseUrl;

  // 3. 拼接URL，确保中间只有一个斜杠
  return `${baseUrl}/${cleanUrl}`;
}

/**
 * 通过图片ID获取图片信息
 * @param {number} imageId - 图片ID
 * @returns {Promise<Object|null>} - 返回图片信息对象，失败时返回null
 */
export async function fetchImageById(imageId) {
  if (!imageId) return null;

  try {
    const result = await getImageInfo(imageId);
    if (result && result.success && result.data) {
      return result.data;
    }
    return null;
  } catch (error) {
    console.error('获取图片信息失败:', error);
    return null;
  }
}
