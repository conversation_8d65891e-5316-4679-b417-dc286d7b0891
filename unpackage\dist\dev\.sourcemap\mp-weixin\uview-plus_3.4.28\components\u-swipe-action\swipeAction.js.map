{"version": 3, "file": "swipeAction.js", "sources": ["uview-plus_3.4.28/components/u-swipe-action/swipeAction.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:00:42\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeAction.js\n */\nexport default {\n    // swipe-action组件\n    swipeAction: {\n        autoClose: true\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,cAAA;AAAA;AAAA,EAEX,aAAa;AAAA,IACT,WAAW;AAAA,EACd;AACL;;"}