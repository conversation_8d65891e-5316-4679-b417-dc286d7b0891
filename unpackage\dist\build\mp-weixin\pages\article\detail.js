"use strict";const e=require("../../common/vendor.js"),t=require("../../utils/image.js"),i=require("./map.js"),o={components:{CustomNavbar:()=>"../../components/common/custom-navbar.js",ArticleDetail:()=>"../../components/article/article-detail.js"},data:()=>({articleId:null,articleType:"news",article:null,loading:!0,pageTitle:"文章详情"}),computed:{apiFunction(){return i.getArticleApi(this.articleType)}},onLoad(e){if(e){if(!e.id)return void this.showError("文章ID不存在");this.articleId=e.id,e.type&&(this.articleType=e.type),this.fetchArticleDetail()}else this.showError("参数不存在")},methods:{showError(t){e.index.showToast({title:t,icon:"none"}),setTimeout((()=>{e.index.navigateBack()}),1500)},async fetchArticleDetail(){try{let t;e.index.showLoading({title:"加载中..."}),t=await this.apiFunction(this.articleId),console.log(t),t&&t.success&&t.data?(this.article=t.data,this.pageTitle=this.article.title||"文章详情","news"!==this.articleType||this.article.content_markdown?this.article.content||(this.article.content=`这是一篇关于"${this.article.title}"的文章内容。\n\n由于API暂未提供完整内容，这里展示的是默认内容。`):this.article.content_markdown=`这是一篇关于"${this.article.title}"的文章内容。\n\n由于API暂未提供文章内容，这里展示的是默认内容。`,this.article.content&&this.article.content.includes("[pic:")&&await this.processArticleImages(this.article),this.article.coverImageId&&await this.fetchCoverImage(this.article.coverImageId)):this.showError("获取文章失败")}catch(t){console.error("获取文章详情失败:",t),this.showError("获取文章失败")}finally{e.index.hideLoading(),this.loading=!1}},async processArticleImages(e){if(!e.content)return;const i=/\[pic:(\d+)\]/g;let o,r=e.content;const c=[],a=[];for(;null!==(o=i.exec(e.content));){const e=parseInt(o[1]);a.push({fullMatch:o[0],imageId:e}),c.push(t.fetchImageById(e))}if(c.length>0){const i=await Promise.all(c);for(let e=0;e<a.length;e++){const o=i[e],{fullMatch:c}=a[e];if(o&&o.url){const e=t.getFullImageUrl(o.url);r=r.replace(c,`<img src="${e}" alt="${o.altText||"文章图片"}" style="max-width: 100%;">`)}else r=r.replace(c,"")}e.content=r}if(e.coverImageId&&!e.coverImage)try{const i=await t.fetchImageById(e.coverImageId);i&&i.url&&(e.coverImage=t.getFullImageUrl(i.url))}catch(l){console.error("获取文章封面图失败:",l)}},async fetchCoverImage(e){try{const i=await t.fetchImageById(e);console.log("获取封面图片成功:",i),i&&i.url&&("normal"!==this.articleType&&"party"!==this.articleType||(this.article.coverImage=t.getFullImageUrl(i.url)))}catch(i){console.error("获取封面图片失败:",i)}}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("custom-navbar")+e.resolveComponent("article-detail")+e.resolveComponent("u-loading-icon"))()}Math||((()=>"../../components/u-icon/u-icon.js")+(()=>"../../uview-plus_3.4.28/components/u-loading-icon/u-loading-icon.js"))();const r=e._export_sfc(o,[["render",function(t,i,o,r,c,a){return e.e({a:e.p({name:"more-dot-fill",color:"#FFFFFF",size:"20"}),b:e.p({title:c.pageTitle,showBack:!0,showHome:!0}),c:c.article},c.article?{d:e.p({article:c.article,articleType:c.articleType,showFooter:!0})}:{e:e.p({mode:"circle",size:"28"})})}]]);wx.createPage(r);
