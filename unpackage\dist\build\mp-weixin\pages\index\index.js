"use strict";const e=require("../../common/vendor.js"),t=require("../../api/news.js"),i=require("../../api/menus.js"),o=require("../../api/test.js"),n=require("../../utils/image.js"),s={components:{CustomNavbar:()=>"../../components/common/custom-navbar.js"},data:()=>({swiperList:[{src:"/static/images/1.png",title:"学习贯彻习近平新时代中国特色社会主义思想"},{src:"/static/images/2.png",title:"深入学习贯彻党的二十大精神"},{src:"/static/images/3.png",title:"喜迎建党102周年系列活动"}],noticeList:[{id:1,title:"关于开展2023年度党员民主评议工作的通知"},{id:2,title:"关于召开2023年度组织生活会的通知"},{id:3,title:"关于开展党史学习教育的通知"}],quickEntryList:[{id:1,title:"党建工作",icon:"home-fill",url:"/pages/party/index"},{id:2,title:"基层动态",icon:"bell-fill",url:""},{id:3,title:"党建品牌",icon:"star-fill",url:""},{id:4,title:"红旗支部",icon:"heart-fill",url:""},{id:5,title:"先锋党员",icon:"account-fill",url:""},{id:6,title:"学习资料",icon:"bookmark-fill",url:""},{id:7,title:"志愿服务",icon:"gift-fill",url:""},{id:8,title:"党费交纳",icon:"rmb-circle-fill",url:""}],newsList:[],bottomNavList:[],menuIconMap:{"党建信息":"home-fill","阵地建设":"star-fill","学习园地":"bookmark-fill","示范典型":"heart-fill","群团工作":"account-fill"},adImage:null,adImageUrl:""}),onLoad(){console.log("开始测试API请求..."),o.testGetArticleList(),this.fetchArticleList(),this.fetchMenuList(),this.fetchAdImage()},methods:{async fetchArticleList(){try{const e=await t.getArticleList();console.log("获取文章列表成功:",e),e&&e.success&&Array.isArray(e.data)&&(this.newsList=e.data,console.log("文章列表数据已更新"))}catch(e){console.error("获取文章列表失败:",e)}},onSwiperClick(t){e.index.showToast({title:"点击了轮播图："+this.swiperList[t].title,icon:"none"})},onSearchClick(t){e.index.showToast({title:"搜索内容："+(t.value||""),icon:"none"})},onNoticeClick(t){const i=this.noticeList[t];e.index.showToast({title:"点击了公告："+i.title,icon:"none"})},onGridClick(t){const i=this.quickEntryList[t];console.log("点击了快捷入口:",i),i.url?(console.log("跳转到URL:",i.url),e.index.navigateTo({url:i.url,fail:t=>{console.error("跳转失败:",t),e.index.showToast({title:"跳转失败",icon:"none"})}})):e.index.showToast({title:"暂未配置："+i.title,icon:"none"})},onNewsClick(t){console.log("点击了文章:",t),e.index.navigateTo({url:`/pages/article/detail?id=${t.id}&type=news`,fail:t=>{console.error("跳转失败:",t),e.index.showToast({title:"跳转失败",icon:"none"})}})},async fetchMenuList(){try{const e=await i.getMenuList();if(console.log("获取菜单列表成功:",e),e&&e.success&&Array.isArray(e.data)){const t=e.data.filter((e=>1===e.level));this.bottomNavList=t.map((e=>({...e,icon:this.menuIconMap[e.name]||"home-fill"}))),console.log("底部导航菜单数据已更新:",this.bottomNavList);const i=e.data.filter((e=>2===e.level));this.quickEntryList.forEach(((e,o)=>{const n=i.find((t=>t.name===e.title));if(n){const i=t.find((e=>e.id===n.parentId));i&&(this.quickEntryList[o].url=`/pages/menus/index?id=${i.id}&name=${encodeURIComponent(i.name)}&subMenuId=${n.id}`,console.log(`已设置快捷入口 "${e.title}" 的URL:`,this.quickEntryList[o].url))}}))}}catch(e){console.error("获取菜单列表失败:",e)}},onAdClick(){console.log("点击了广告图"),e.index.showToast({title:"点击了广告图",icon:"none"})},onNavClick(t){const i=this.bottomNavList[t];console.log("点击了底部导航:",i),e.index.navigateTo({url:`/pages/menus/index?id=${i.id}&name=${encodeURIComponent(i.name)}`,fail:t=>{console.error("跳转失败:",t),e.index.showToast({title:"跳转失败",icon:"none"})}})},async fetchAdImage(){try{const e=await n.fetchImageById(36);console.log("获取广告图片信息成功:",e),e&&e.url?(this.adImage=e,this.adImageUrl=n.getFullImageUrl(e.url),console.log("广告图片URL:",this.adImageUrl)):(console.error("广告图片信息不完整"),this.adImageUrl="/static/images/640.gif")}catch(e){console.error("获取广告图片信息失败:",e),this.adImageUrl="/static/images/640.gif"}}}};if(!Array){(e.resolveComponent("u-icon")+e.resolveComponent("custom-navbar")+e.resolveComponent("u-search")+e.resolveComponent("u-image")+e.resolveComponent("u-swiper")+e.resolveComponent("u-notice-bar")+e.resolveComponent("u-text")+e.resolveComponent("u-grid-item")+e.resolveComponent("u-grid")+e.resolveComponent("u-cell")+e.resolveComponent("u-list"))()}Math||((()=>"../../components/u-icon/u-icon.js")+(()=>"../../uview-plus_3.4.28/components/u-search/u-search.js")+(()=>"../../uview-plus_3.4.28/components/u-image/u-image.js")+(()=>"../../uview-plus_3.4.28/components/u-swiper/u-swiper.js")+(()=>"../../uview-plus_3.4.28/components/u-notice-bar/u-notice-bar.js")+(()=>"../../uview-plus_3.4.28/components/u-text/u-text.js")+(()=>"../../uview-plus_3.4.28/components/u-grid-item/u-grid-item.js")+(()=>"../../uview-plus_3.4.28/components/u-grid/u-grid.js")+(()=>"../../uview-plus_3.4.28/components/u-cell/u-cell.js")+(()=>"../../uview-plus_3.4.28/components/u-list/u-list.js"))();const l=e._export_sfc(s,[["render",function(t,i,o,n,s,l){return{a:e.p({name:"more-dot-fill",color:"#FFFFFF",size:"20"}),b:e.p({name:"camera-fill",color:"#FFFFFF",size:"20"}),c:e.p({title:"红色环投",showBack:!1,showHome:!1}),d:e.o(l.onSearchClick),e:e.p({placeholder:"搜索",placeholderColor:"#000000",disabled:!0,shape:"round"}),f:e.o(l.onAdClick),g:e.p({src:s.adImageUrl,width:"100%",height:"120",mode:"widthFix",fade:!0,borderRadius:8}),h:e.o(l.onSwiperClick),i:e.p({list:s.swiperList,height:"300",radius:"8",autoplay:!0,interval:3e3,indicator:!0,indicatorMode:"dot",keyName:"src",showTitle:!0}),j:e.o(l.onNoticeClick),k:e.p({text:s.noticeList.map((e=>e.title)),direction:"column",duration:3e3,speed:80,color:"#333333",bgColor:"#ffffff",justifyContent:"flex-start"}),l:e.p({text:"快捷入口",size:"16",bold:!0,color:"#333333"}),m:e.f(s.quickEntryList,((t,i,o)=>({a:"13664613-10-"+o+",13664613-9-"+o,b:e.p({name:t.icon||"star-fill",size:"36",color:"#E51C23"}),c:"13664613-11-"+o+",13664613-9-"+o,d:e.p({text:t.title,align:"center",size:"16","margin-top":"4"}),e:i,f:"13664613-9-"+o+",13664613-8",g:e.p({index:i})}))),n:e.o(l.onGridClick),o:e.p({col:4,border:!1}),p:e.p({text:"党建要闻",size:"16",bold:!0,color:"#333333"}),q:e.f(s.newsList,((t,i,o)=>({a:i,b:e.o((e=>l.onNewsClick(t)),i),c:"13664613-14-"+o+",13664613-13",d:e.p({title:t.title,titleStyle:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},isLink:!0})}))),r:e.p({"enable-flex":!0,height:"500px"}),s:e.f(s.bottomNavList,((t,i,o)=>({a:"13664613-17-"+o+",13664613-16-"+o,b:e.p({name:t.icon||"home",size:"24",color:"#FF0000"}),c:"13664613-18-"+o+",13664613-16-"+o,d:e.p({text:t.name,align:"center",size:"14","margin-top":"4"}),e:i,f:"13664613-16-"+o+",13664613-15",g:e.p({index:i})}))),t:e.o(l.onNavClick),v:e.p({col:5,border:!1})}}]]);wx.createPage(l);
