"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),o=require("../../../common/vendor.js"),r={name:"u-badge",mixins:[t.mpMixin,e.props,i.mixin],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],i=this.offset[1]||t;e.top=s.addUnit(t),e.right=s.addUnit(i)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}},methods:{addStyle:s.addStyle}};const u=o._export_sfc(r,[["render",function(e,t,i,s,r,u){return o.e({a:e.show&&(0!==Number(e.value)||e.showZero||e.isDot)},e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?{b:o.t(e.isDot?"":u.showValue),c:o.n(e.isDot?"u-badge--dot":"u-badge--not-dot"),d:o.n(e.inverted&&"u-badge--inverted"),e:o.n("horn"===e.shape&&"u-badge--horn"),f:o.n(`u-badge--${e.type}${e.inverted?"--inverted":""}`),g:o.s(u.addStyle(e.customStyle)),h:o.s(u.badgeStyle)}:{})}],["__scopeId","data-v-cc3e320c"]]);wx.createComponent(u);
