"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),s=require("../../libs/function/index.js"),r=require("../../../common/vendor.js"),n={name:"u-grid",mixins:[t.mpMixin,i.mixin,e.props],data:()=>({index:0,width:0}),watch:{parentData(){this.children.length&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))}},created(){this.children=[]},computed:{parentData(){return[this.hoverClass,this.col,this.size,this.border]},gridStyle(){let e={};switch(this.align){case"left":default:e.justifyContent="flex-start";break;case"center":e.justifyContent="center";break;case"right":e.justifyContent="flex-end"}return s.deepMerge(e,s.addStyle(this.customStyle))}},emits:["click"],options:{},methods:{childClick(e){this.$emit("click",e)}}},a=()=>{r.useCssVars((e=>({"1d4f6e32":e.gap,"1d4f88de":e.col})))},c=n.setup;n.setup=c?(e,t)=>(a(),c(e,t)):a;const d=r._export_sfc(n,[["render",function(e,t,i,s,n,a){return{a:r.s(a.gridStyle),b:r.s(e.__cssVars())}}],["__scopeId","data-v-65424099"]]);wx.createComponent(d);
