<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基本案例</text>
			<view>
				<up-grid
					:border="false"
					@click="click"
					align="center"
				>
					<up-grid-item
						v-for="(baseListItem,baseListIndex) in baseList"
						@click="click('test')"
						:key="baseListIndex"
					>
						<up-icon
							:customStyle="{paddingTop:20+'rpx'}"
							:name="baseListItem.name"
							:size="22"
						></up-icon>
						<text class="grid-text">{{baseListItem.title}}</text>
					</up-grid-item>
				</up-grid>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">显示边框</text>
			<view>
				<up-grid :border="true">
					<up-grid-item
						v-for="(listItem,listIndex) in list"
						:key="listIndex"
						customStyle="padding-top: 10px; padding-bottom: 10px" 
					>
						<up-icon
							:customStyle="{paddingTop:20+'rpx'}"
							:name="listItem.name"
							:size="22"
						></up-icon>
						<text class="grid-text">{{listItem.title}}</text>
					</up-grid-item>
				</up-grid>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">绑定点击事件&自定义列数</text>
			<view>
				<up-grid
					:border="false"
					col="4"
				>
					<up-grid-item
						v-for="(listItem,listIndex) in list"
						:key="listIndex"
						customStyle="padding-top: 10px; padding-bottom: 10px"
					>
						<up-icon
							:customStyle="{paddingTop:20+'rpx'}"
							:name="listItem.name"
							:size="22"
						></up-icon>
						<text class="grid-text">{{listItem.title}}</text>
					</up-grid-item>
				</up-grid>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">可滑动</text>
			<view>
				<swiper
					:indicator-dots="true"
					class="swiper"
				>
					<swiper-item>
						<up-grid :border="true">
							<up-grid-item
								v-for="(item, index) in swiperList"
								:index="index"
								:key="index"
							>
								<up-icon
									:customStyle="{paddingTop:20+'rpx'}"
									:name="item"
									:size="22"
								></up-icon>
								<text class="grid-text">{{ '宫格' + (index + 1) }}</text>
							</up-grid-item>
						</up-grid>
					</swiper-item>
					<swiper-item>
						<up-grid :border="true">
							<up-grid-item
								v-for="(item, index) in swiperList"
								:index="index + 9"
								:key="index"
							>
								<up-icon
									:customStyle="{paddingTop:20+'rpx'}"
									:name="item"
									:size="22"
								></up-icon>
								<text class="grid-text">{{ '宫格' + (index + 1) }}</text>
							</up-grid-item>
						</up-grid>
					</swiper-item>
					<swiper-item>
						<up-grid :border="true">
							<up-grid-item
								v-for="(item, index) in swiperList"
								:index="index + 18"
								:key="index"
							>
								<up-icon
									:customStyle="{paddingTop:20+'rpx'}"
									:name="item"
									:size="22"
								></up-icon>
								<text class="grid-text">{{ "宫格" + (index + 1) }}</text>
							</up-grid-item>
						</up-grid>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<up-toast ref="uToastRef" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				baseList: [{
						name: 'photo',
						title: '图片'
					},
					{
						name: 'lock',
						title: '锁头'
					},
					// {
					// 	name: 'star',
					// 	title: '星星'
					// },
				],
				list: [{
						name: 'photo',
						title: '图片'
					},
					{
						name: 'lock',
						title: '锁头'
					},
					{
						name: 'star',
						title: '星星'
					},
					{
						name: 'hourglass',
						title: '沙漏'
					},
					{
						name: 'home',
						title: '首页'
					},
					{
						name: 'volume',
						title: '音量'
					},
				],
				swiperList: ['integral', 'kefu-ermai', 'coupon', 'gift', 'scan', 'pause-circle', 'wifi', 'email', 'list'],
			}
		},
		methods: {
			click(name) {
				console.log(name)
				this.$refs.uToastRef.success(`点击了第${name}个`)
			}
		}
	}
</script>

<style lang="scss">
	.swiper {
		height: 220px;
	}

	.grid-text {
		font-size: 14px;
		color: #909399;
		padding: 10rpx 0 20rpx 0rpx;
		/* #ifndef APP-PLUS */
		box-sizing: border-box;
		/* #endif */
	}
</style>
