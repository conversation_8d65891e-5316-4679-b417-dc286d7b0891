<template>
	<view class="article-detail">
		<!-- 文章标题 -->
		<view class="article-header">
			<view class="article-title">
				<text>{{ article.title || '加载中...' }}</text>
			</view>
			<view class="article-meta">
				<view v-if="article.created_at || article.createdAt">
					<text>发布时间：{{ formatDate(article.created_at || article.createdAt) }}</text>
				</view>
				<view v-if="article.category">
					<text>分类：{{ article.category }}</text>
				</view>
				<view v-if="article.author">
					<text>作者：{{ article.author }}</text>
				</view>
			</view>
		</view>

		<!-- 文章封面图 -->
		<view class="article-cover" v-if="coverImage">
			<image
				:show-menu-by-longpress="true"
				:src="getFullImageUrl(coverImage.url)"
				:alt="coverImage.altText || article.title"
				mode="widthFix"
				@click="previewCoverImage"
			></image>
		</view>

		<!-- 文章内容 -->
		<view class="article-content">
			<rich-text :nodes="articleContent || '加载中...'"></rich-text>
		</view>

		<!-- 底部操作栏 -->
		<view class="article-footer">
			<u-button type="primary" text="返回列表" @click="goBack" :customStyle="{backgroundColor: '#D9001B'}"></u-button>
		</view>
	</view>
</template>

<script>
	import { getFullImageUrl as getFullImageUrlUtil, fetchImageById } from '@/utils/image.js';

	export default {
		name: 'ArticleDetail',
		props: {
			// 文章对象
			article: {
				type: Object,
				default: () => ({
					id: null,
					title: '',
					content: '',
					content_markdown: '',
					created_at: '',
					createdAt: '',
					category: '',
					author: '',
					coverImageId: null
				})
			},
			// 文章类型：'normal'(普通文章) 或 'menu'(菜单文章)
			articleType: {
				type: String,
				default: 'normal',
				validator: (value) => ['normal', 'menu'].includes(value)
			},
			// 是否显示底部按钮
			showFooter: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				coverImage: null,
				loading: true
			}
		},
		computed: {
			// 处理后的文章内容
			articleContent() {
				if (this.articleType === 'normal') {
					// 普通文章，需要将Markdown转为HTML
					return this.formatContent(this.article.content_markdown);
				} else {
					// 菜单文章，内容已经是HTML格式
					return this.article.content;
				}
			}
		},
		watch: {
			// 监听文章变化，获取封面图
			article: {
				handler(newVal) {
					if (newVal && newVal.coverImageId) {
						this.fetchCoverImage(newVal.coverImageId);
					}
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			// 格式化日期
			formatDate(dateString) {
				if (!dateString) return '';
				const date = new Date(dateString);
				return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
			},

			// 格式化Markdown内容为HTML
			formatContent(markdown) {
				if (!markdown) return '';

				// 简单的Markdown转HTML处理
				let html = markdown
					// 段落
					.replace(/\n\n/g, '</p><p class="article-paragraph">')
					// 单行换行
					.replace(/\n/g, '<br>')
					// 标题
					.replace(/#{6}\s(.*?)\s*$/gm, '<h6>$1</h6>')
					.replace(/#{5}\s(.*?)\s*$/gm, '<h5>$1</h5>')
					.replace(/#{4}\s(.*?)\s*$/gm, '<h4>$1</h4>')
					.replace(/#{3}\s(.*?)\s*$/gm, '<h3>$1</h3>')
					.replace(/#{2}\s(.*?)\s*$/gm, '<h2>$1</h2>')
					.replace(/#{1}\s(.*?)\s*$/gm, '<h1>$1</h1>')
					// 加粗
					.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
					// 斜体
					.replace(/\*(.*?)\*/g, '<em>$1</em>')
					// 引用
					.replace(/>\s(.*?)\s*$/gm, '<blockquote>$1</blockquote>');

				// 确保内容被包裹在段落标签中
				if (!html.startsWith('<')) {
					html = '<p class="article-paragraph">' + html;
				}
				if (!html.endsWith('>')) {
					html = html + '</p>';
				}

				return html;
			},

			// 获取封面图片
			async fetchCoverImage(imageId) {
				try {
					const imageData = await fetchImageById(imageId);
					console.log('获取封面图片成功:', imageData);

					if (imageData) {
						this.coverImage = imageData;
					}
				} catch (error) {
					console.error('获取封面图片失败:', error);
				}
			},

			// 获取完整的图片URL
			getFullImageUrl(url) {
				return getFullImageUrlUtil(url);
			},

			// 预览封面图片
			previewCoverImage() {
				if (this.coverImage && this.coverImage.url) {
					const imageUrl = this.getFullImageUrl(this.coverImage.url);
					console.log('预览封面图片:', imageUrl);

					uni.previewImage({
						urls: [imageUrl],
						current: imageUrl,
						success: () => {
							console.log('图片预览成功');
						},
						fail: (err) => {
							console.error('图片预览失败:', err);
							uni.showToast({
								title: '图片预览失败',
								icon: 'none'
							});
						}
					});
				}
			},

			// 返回上一页
			goBack() {
				// 直接在组件内部处理返回操作，不再触发事件
				uni.navigateBack();
			}
		}
	}
</script>

<style lang="scss">
	.article-detail {
		padding: 30rpx;

		.article-header {
			margin-bottom: 30rpx;

			.article-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333333;
				line-height: 1.4;
				margin-bottom: 20rpx;
			}

			.article-meta {
				font-size: 24rpx;
				color: #999999;
				line-height: 1.6;

				view {
					margin-right: 20rpx;
					display: inline-block;
				}
			}
		}

		.article-cover {
			margin: 20rpx 0 30rpx;
			width: 100%;

			image {
				width: 100%;
				border-radius: 8rpx;
			}
		}

		.article-content {
			font-size: 28rpx;
			color: #333333;
			line-height: 1.6;
			margin-bottom: 40rpx;


		}

		.article-footer {
			margin-top: 40rpx;
			padding: 20rpx 0;
		}
	}
</style>
