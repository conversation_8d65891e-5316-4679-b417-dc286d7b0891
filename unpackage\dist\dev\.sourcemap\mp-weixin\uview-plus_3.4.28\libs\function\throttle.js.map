{"version": 3, "file": "throttle.js", "sources": ["uview-plus_3.4.28/libs/function/throttle.js"], "sourcesContent": ["let timer;\r\nlet flag;\r\n/**\r\n * 节流原理：在一定时间内，只能触发一次\r\n *\r\n * @param {Function} func 要执行的回调函数\r\n * @param {Number} wait 延时的时间\r\n * @param {Boolean} immediate 是否立即执行\r\n * @return null\r\n */\r\nexport function throttle(func, wait = 500, immediate = true) {\r\n    if (immediate) {\r\n        if (!flag) {\r\n            flag = true\r\n            // 如果是立即执行，则在wait毫秒内开始时执行\r\n            typeof func === 'function' && func()\r\n            timer = setTimeout(() => {\r\n                flag = false\r\n            }, wait)\r\n        }\r\n    } else if (!flag) {\r\n        flag = true\r\n        // 如果是非立即执行，则在wait毫秒内的结束处执行\r\n        timer = setTimeout(() => {\r\n            flag = false\r\n            typeof func === 'function' && func()\r\n        }, wait)\r\n    }\r\n}\r\nexport default throttle\r\n"], "names": [], "mappings": ";AACA,IAAI;AASG,SAAS,SAAS,MAAM,OAAO,KAAK,YAAY,MAAM;AACzD,MAAI,WAAW;AACX,QAAI,CAAC,MAAM;AACP,aAAO;AAEP,aAAO,SAAS,cAAc,KAAM;AAC5B,iBAAW,MAAM;AACrB,eAAO;AAAA,MACV,GAAE,IAAI;AAAA,IACV;AAAA,EACT,WAAe,CAAC,MAAM;AACd,WAAO;AAEC,eAAW,MAAM;AACrB,aAAO;AACP,aAAO,SAAS,cAAc,KAAM;AAAA,IACvC,GAAE,IAAI;AAAA,EACV;AACL;;"}