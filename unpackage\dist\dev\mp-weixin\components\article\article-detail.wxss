/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.article-detail {
  padding: 30rpx;
}
.article-detail .article-header {
  margin-bottom: 30rpx;
}
.article-detail .article-header .article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}
.article-detail .article-header .article-meta {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.6;
}
.article-detail .article-header .article-meta view {
  margin-right: 20rpx;
  display: inline-block;
}
.article-detail .article-cover {
  margin: 20rpx 0 30rpx;
  width: 100%;
}
.article-detail .article-cover image {
  width: 100%;
  border-radius: 8rpx;
}
.article-detail .article-cover .miniapp-link {
  margin-top: 20rpx;
  text-align: center;
  padding: 15rpx 0;
}
.article-detail .article-cover .miniapp-link .link-text {
  color: #D9001B;
  font-size: 28rpx;
  font-weight: 500;
  display: inline-block;
  padding: 15rpx 30rpx;
  border-radius: 25rpx;
  background-color: rgba(217, 0, 27, 0.1);
  border: 2rpx solid #D9001B;
  transition: all 0.3s ease;
}
.article-detail .article-cover .miniapp-link .link-text:active {
  opacity: 0.8;
  background-color: #D9001B;
  color: #FFFFFF;
}
.article-detail .article-content {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.article-detail .article-footer {
  margin-top: 40rpx;
  padding: 20rpx 0;
}