"use strict";const t=require("./config.js"),r=require("../api/images.js");exports.fetchImageById=async function(t){if(!t)return null;try{const s=await r.getImageInfo(t);return s&&s.success&&s.data?s.data:null}catch(s){return console.error("获取图片信息失败:",s),null}},exports.getFullImageUrl=function(r){if(!r)return"";if(r.startsWith("http://")||r.startsWith("https://"))return r;const s=r.startsWith("/")?r.substring(1):r;return`${t.config.apiBaseUrl.endsWith("/")?t.config.apiBaseUrl.substring(0,t.config.apiBaseUrl.length-1):t.config.apiBaseUrl}/${s}`};
