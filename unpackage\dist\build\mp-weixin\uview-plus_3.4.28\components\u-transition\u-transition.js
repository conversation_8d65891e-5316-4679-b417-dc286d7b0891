"use strict";const e=require("./props.js"),i=require("../../libs/mixin/mpMixin.js"),n=require("../../libs/mixin/mixin.js"),t=require("../../libs/function/index.js"),r=require("./transitionMixin.js"),s=require("../../../common/vendor.js"),o={name:"u-transition",data:()=>({inited:!1,viewStyle:{},status:"",transitionEnded:!1,display:!1,classes:""}),emits:["click","beforeEnter","enter","afterEnter","beforeLeave","leave","afterLeave"],computed:{mergeStyle(){const{viewStyle:e,customStyle:i}=this;return{transitionDuration:`${this.duration}ms`,transitionTimingFunction:this.timingFunction,...t.addStyle(i),...e}}},mixins:[i.mpMixin,n.mixin,r.transitionMixin,e.props],watch:{show:{handler(e){e?this.vueEnter():this.vueLeave()},immediate:!0}}};const a=s._export_sfc(o,[["render",function(e,i,n,t,r,o){return s.e({a:r.inited},r.inited?{b:s.o(((...i)=>e.clickHandler&&e.clickHandler(...i))),c:s.n(r.classes),d:s.s(o.mergeStyle),e:s.o(((...i)=>e.noop&&e.noop(...i)))}:{})}],["__scopeId","data-v-f7ec718f"]]);wx.createComponent(a);
