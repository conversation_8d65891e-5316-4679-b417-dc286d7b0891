.u-block {
	padding: 14px;
	&__section {
		margin-bottom:10px;
	}
	&__title {
		margin-top:10px;
		font-size: 15px;
		color: $u-content-color;
		margin-bottom:10px;
	}
	&__flex {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
	}
}

// 使用了cell组件的icon图片样式
.u-cell-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 8rpx;
}

.u-page {
	padding: 15px 15px 40px 15px;
	&__item {
		flex: 1;
		// margin-bottom: 23px;
		&__title {
			font-size: 14px;
			color: rgb(143, 156, 162);
			// margin-bottom: 8px;
			@include flex;
		}
	}
}

.u-demo-block {
	flex: 1;
	margin-bottom: 23px;
	
	&__content {
		@include flex(column);
	}
	
	&__title {
		font-size: 14px;
		color: rgb(143, 156, 162);
		margin-bottom: 8px;
		@include flex;
	}
}

