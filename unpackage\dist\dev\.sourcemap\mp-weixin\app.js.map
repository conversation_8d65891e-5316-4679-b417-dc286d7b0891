{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch');\r\n\r\n\t\t\t// 在App启动时加载字体，确保只加载一次\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (!this.$options.fontLoaded) {\r\n\t\t\t\tthis.$options.fontLoaded = true;\r\n\t\t\t\tuni.loadFontFace({\r\n\t\t\t\t\tfamily: 'uicon-iconfont',\r\n\t\t\t\t\tsource: 'url(\"https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf\")',\r\n\t\t\t\t\tsuccess() {\r\n\t\t\t\t\t\tconsole.log('字体加载成功');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\tconsole.error('字体加载失败', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/*每个页面公共css */\r\n\t@import \"@/uview-plus_3.4.28/index.scss\";\r\n\t@import \"@/static/styles/global.scss\";\r\n\r\n\t/* 全局定义字体图标，确保只加载一次 */\r\n\t/* #ifdef MP-WEIXIN */\r\n\t@font-face {\r\n\t\tfont-family: 'uicon-iconfont';\r\n\t\tsrc: url('https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf') format('truetype');\r\n\t}\r\n\t/* #endif */\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nimport uviewPlus from './uview-plus_3.4.28/index'\nimport uviewConfig from './utils/uview-config'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n\n  // 使用uview-plus\n  app.use(uviewPlus, () => {\n    return {\n      options: {\n        // 修改$u.config对象的属性\n        config: {\n          // 修改默认单位为rpx\n          unit: 'rpx',\n          // 使用官方字体图标\n          iconUrl: uviewConfig.iconUrl,\n          customIcon: uviewConfig.customIcon\n        }\n      }\n    }\n  })\n\n  // 字体加载已移至 App.vue 的 onLaunch 方法中，确保只加载一次\n\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "App", "uviewPlus", "uviewConfig"], "mappings": ";;;;;;;;;;;;AACC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAIxB,QAAI,CAAC,KAAK,SAAS,YAAY;AAC9B,WAAK,SAAS,aAAa;AAC3BA,oBAAAA,MAAI,aAAa;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AACTA,wBAAAA,oCAAY,QAAQ;AAAA,QACpB;AAAA,QACD,KAAK,KAAK;AACTA,wBAAc,MAAA,MAAA,SAAA,iBAAA,UAAU,GAAG;AAAA,QAC5B;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EAEA;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AACD;ACVM,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAG5B,MAAI,IAAIC,uBAAAA,WAAW,MAAM;AACvB,WAAO;AAAA,MACL,SAAS;AAAA;AAAA,QAEP,QAAQ;AAAA;AAAA,UAEN,MAAM;AAAA;AAAA,UAEN,SAASC,kBAAW,YAAC;AAAA,UACrB,YAAYA,kBAAW,YAAC;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AAAA,EACL,CAAG;AAID,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}