<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">演示案例</text>
			<view class="u-page__swipe-action-item">
				<up-swipe-action>
					<up-swipe-action-item
						v-if="show1"
						:closeOnClick="false"
						v-model:show="swshow1"
						:options="options1"
						@click="click"
					>
						<view class="swipe-action u-border-top u-border-bottom">
							<view class="swipe-action__content">
								<text class="swipe-action__content__text">基础使用</text>
							</view>
						</view>
					</up-swipe-action-item>
				</up-swipe-action>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮组</text>
			<view class="u-page__swipe-action-item">
				<up-swipe-action>
					<up-swipe-action-item closeOnClick :options="options2">
						<view class="swipe-action u-border-top u-border-bottom">
							<view class="swipe-action__content">
								<text class="swipe-action__content__text">两个按钮并列</text>
							</view>
						</view>
					</up-swipe-action-item>
				</up-swipe-action>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">带图标</text>
			<view class="u-page__swipe-action-item">
				<up-swipe-action>
					<up-swipe-action-item :options="options3">
						<view class="swipe-action u-border-top u-border-bottom">
							<view class="swipe-action__content">
								<text class="swipe-action__content__text">自定义图标</text>
							</view>
						</view>
					</up-swipe-action-item>
				</up-swipe-action>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">组合使用</text>
			<view class="u-page__swipe-action-item">
				<up-swipe-action>
					<up-swipe-action-item
						:options="item.options"
						v-for="(item, index) in options4"
						:disabled="item.disabled"
						:key="index"
					>
						<view
							class="swipe-action u-border-top"
							:class="[index === options4.length - 1 && 'u-border-bottom']"
						>
							<view class="swipe-action__content">
								<text class="swipe-action__content__text">{{ item.text }}</text>
							</view>
						</view>
					</up-swipe-action-item>
				</up-swipe-action>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义按钮形状</text>
			<view class="u-page__swipe-action-item">
				<up-swipe-action>
					<up-swipe-action-item :options="options5">
						<view class="swipe-action u-border-top u-border-bottom">
							<view class="swipe-action__content">
								<text class="swipe-action__content__text">圆形按钮</text>
							</view>
						</view>
					</up-swipe-action-item>
				</up-swipe-action>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				swshow1: true,
				show1: true,
				moveX: 0,
				showText: '当前状态：关',
				showStatus: false,
				options1: [{
					text: '删除',
					style: {
						backgroundColor: '#f56c6c'
					}
				}],
				options2: [{
					text: '收藏',
					style: {
						backgroundColor: '#3c9cff'
					}
				}, {
					text: '删除',
					style: {
						backgroundColor: '#f56c6c'
					}
				}],
				options3: [{
					text: '收藏',
					icon: 'star-fill',
					iconSize: '20',
					style: {
						backgroundColor: '#f9ae3d'
					}
				}],
				options4: [{
					text: '禁用状态',
					disabled: true,
					options: [{
							text: '置顶',
							style: {
								backgroundColor: '#3c9cff',
							}
						},
						{
							text: '取消',
							style: {
								backgroundColor: '#f9ae3d',
							}
						},
					],
				}, {
					text: '正常状态',
					disabled: false,
					options: [{
							text: '置顶',
							style: {
								backgroundColor: '#3c9cff',
							}
						},
						{
							text: '取消',
							style: {
								backgroundColor: '#f9ae3d',
							}
						},
					],
				}, {
					text: '自动关闭',
					disabled: false,
					options: [{
							text: '置顶',
							style: {
								backgroundColor: '#3c9cff',
							}
						},
						{
							text: '取消',
							style: {
								backgroundColor: '#f9ae3d',
							}
						},
					],
				}],
				options5: [{
					icon: 'trash-fill',
					style: {
						backgroundColor: '#f56c6c',
						width: '40px',
						height: '40px',
						borderRadius: '100px',
						margin: '0 6px'
					}
				}, {
					icon: 'heart-fill',
					style: {
						backgroundColor: '#5ac725',
						width: '40px',
						height: '40px',
						borderRadius: '100px',
						margin: '0 6px'
					}
				}]
			}
		},
		methods: {
			click(index, cb) {
				console.log('click', index);
				uni.showModal({
					title: '温馨提示',
					content: '确定要删除吗？',
					success: res => {
						if (res.confirm) {
							this.swshow1 = false
							this.show1 = false
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}

	.u-demo-block__title {
		padding: 10px 0 2px 15px;
	}

	.swipe-action {
		&__content {
			padding: 25rpx 0;

			&__text {
				font-size: 15px;
				color: $u-main-color;
				padding-left: 30rpx;
			}
		}
	}
</style>
