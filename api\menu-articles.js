/**
 * 菜单文章相关API
 */
import requestApi from './request.js';

/**
 * 获取指定菜单ID的文章列表
 * @param {number} menuId - 菜单ID
 * @returns {Promise<Object>} - 返回文章列表数据
 */
export const getMenuArticles = async (menuId) => {
  try {
    const result = await requestApi.get(`/api/menu-articles/menu/${menuId}`);
    return result;
  } catch (error) {
    console.error('获取菜单文章列表失败:', error);
    throw error;
  }
};

/**
 * 获取菜单文章详情
 * @param {number} id - 文章ID
 * @returns {Promise<Object>} - 返回文章详情数据
 */
export const getArticleDetail = async (id) => {
  try {
    const result = await requestApi.get(`/api/menu-articles/${id}`);
    return result;
  } catch (error) {
    console.error('获取菜单文章详情失败:', error);
    throw error;
  }
};
