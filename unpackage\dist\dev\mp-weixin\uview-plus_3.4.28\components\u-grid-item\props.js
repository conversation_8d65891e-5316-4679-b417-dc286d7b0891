"use strict";
const uviewPlus_3_4_28_libs_vue = require("../../libs/vue.js");
const uviewPlus_3_4_28_libs_config_props = require("../../libs/config/props.js");
const props = uviewPlus_3_4_28_libs_vue.defineMixin({
  props: {
    // 宫格的name
    name: {
      type: [String, Number, null],
      default: () => uviewPlus_3_4_28_libs_config_props.props.gridItem.name
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: () => uviewPlus_3_4_28_libs_config_props.props.gridItem.bgColor
    }
  }
});
exports.props = props;
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/uview-plus_3.4.28/components/u-grid-item/props.js.map
