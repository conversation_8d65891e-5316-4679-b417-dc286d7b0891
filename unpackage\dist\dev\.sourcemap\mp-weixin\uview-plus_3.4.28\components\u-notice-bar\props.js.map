{"version": 3, "file": "props.js", "sources": ["uview-plus_3.4.28/components/u-notice-bar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 显示的内容，数组\n        text: {\n            type: [Array, String],\n            default: () => defProps.noticeBar.text\n        },\n        // 通告滚动模式，row-横向滚动，column-竖向滚动\n        direction: {\n            type: String,\n            default: () => defProps.noticeBar.direction\n        },\n        // direction = row时，是否使用步进形式滚动\n        step: {\n            type: Boolean,\n            default: () => defProps.noticeBar.step\n        },\n        // 是否显示左侧的音量图标\n        icon: {\n            type: String,\n            default: () => defProps.noticeBar.icon\n        },\n        // 通告模式，link-显示右箭头，closable-显示右侧关闭图标\n        mode: {\n            type: String,\n            default: () => defProps.noticeBar.mode\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: () => defProps.noticeBar.color\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: () => defProps.noticeBar.bgColor\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少px(px)，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: [String, Number],\n            default: () => defProps.noticeBar.speed\n        },\n        // 字体大小\n        fontSize: {\n            type: [String, Number],\n            default: () => defProps.noticeBar.fontSize\n        },\n        // 滚动一个周期的时间长，单位ms\n        duration: {\n            type: [String, Number],\n            default: () => defProps.noticeBar.duration\n        },\n        // 是否禁止用手滑动切换\n        // 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序\n        disableTouch: {\n            type: Boolean,\n            default: () => defProps.noticeBar.disableTouch\n        },\n        // 跳转的页面路径\n        url: {\n            type: String,\n            default: () => defProps.noticeBar.url\n        },\n        // 页面跳转的类型\n        linkType: {\n            type: String,\n            default: () => defProps.noticeBar.linkType\n        },\n\t\tjustifyContent: {\n            type: String,\n            default: () => defProps.noticeBar.justifyContent\n        },\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,0BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM,CAAC,OAAO,MAAM;AAAA,MACpB,SAAS,MAAMC,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA;AAAA,IAGD,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA,IACP,gBAAgB;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,yCAAS,UAAU;AAAA,IACrC;AAAA,EACJ;AACL,CAAC;;"}