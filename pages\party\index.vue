<template>
	<view class="common-container">
		<!-- 自定义导航栏 -->
		<custom-navbar title="党建工作" :showBack="true" :showHome="true" @leftClick="goBack">
			<template #right>
				<view class="navbar-right">
					<u-icon name="more-dot-fill" color="#FFFFFF" size="20"></u-icon>
				</view>
			</template>
		</custom-navbar>

		<!-- 党建工作标题区域 -->
		<view class="party-header">
			<view class="header-title">党建工作</view>
			<view class="header-desc">坚持党的领导，加强党的建设</view>
		</view>

		<!-- 党建工作内容 -->
		<view class="party-content">
			<view class="content-section">
				<view class="section-title">
					<u-icon name="grid-fill" color="#D9001B" size="20"></u-icon>
					<text>{{ currentTitle }}</text>
				</view>


				<view class="section-content">
					<u-list :enable-flex="true">
						<u-cell v-for="(item, index) in categoryList" :key="index"
							:title="item.name"
							:titleStyle="{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}"
							isLink
							@click="onCategoryClick(index)">
						</u-cell>
					</u-list>
				</view>


			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="party-footer">
			<u-button type="primary" :text="categoryHistory.length > 0 ? '返回上一级' : '返回首页'" @click="goBack" :customStyle="{backgroundColor: '#D9001B'}"></u-button>
		</view>
	</view>
</template>

<script>
	import { categoryApi } from '@/api/index.js';
	import CustomNavbar from '@/components/common/custom-navbar.vue';

	export default {
		components: {
			CustomNavbar
		},
		data() {
			return {
				// 基层党组织数据
				categoryList: [],
				// 分类历史记录，用于返回上一级
				categoryHistory: [],
				// 当前分类层级
				currentLevel: 1,
				// 当前分类标题
				currentTitle: '基层党组织'
			}
		},
		onLoad() {
			// 页面加载时的逻辑
			console.log('党建工作页面加载');
			// 获取分类数据
			this.fetchCategoryList();
		},
		methods: {
			// 获取分类列表
			async fetchCategoryList() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					const result = await categoryApi.getCategoryTreeList();
					console.log('获取分类树形列表成功:', result);

					if (result && result.success && Array.isArray(result.data)) {
						this.categoryList = result.data;
						console.log('分类树形列表数据已更新');
					}
				} catch (error) {
					console.error('获取分类树形列表失败:', error);
					uni.showToast({
						title: '获取分类数据失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			},
			// 分类点击事件
			onCategoryClick(index) {
				const item = this.categoryList[index];
				console.log('点击了基层党组织:', item);

				// 判断是否有子分类
				if (item.children && item.children.length > 0) {
					// 保存当前分类列表到历史记录
					this.categoryHistory.push({
						list: [...this.categoryList],
						title: this.currentTitle,
						level: this.currentLevel
					});

					// 有子分类，更新当前列表为子分类
					this.categoryList = item.children;

					// 更新当前标题和层级
					this.currentTitle = item.name;
					this.currentLevel++;

					// 更新页面标题
					uni.setNavigationBarTitle({
						title: item.name
					});
				} else {
					// 没有子分类，跳转到分类文章列表页面
					uni.navigateTo({
						url: `/pages/party/category-articles/index?id=${item.id}`,
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				}
			},

			// 返回上一级分类
			goBackToParent() {
				if (this.categoryHistory.length > 0) {
					// 从历史记录中获取上一级分类信息
					const prevCategory = this.categoryHistory.pop();

					// 恢复上一级分类列表
					this.categoryList = prevCategory.list;
					this.currentTitle = prevCategory.title;
					this.currentLevel = prevCategory.level;

					// 更新页面标题
					uni.setNavigationBarTitle({
						title: this.currentTitle
					});

					return true;
				}
				return false;
			},
			// 返回首页或上一级
			goBack() {
				// 先尝试返回上一级分类
				if (!this.goBackToParent()) {
					// 如果没有上一级分类，则返回首页
					uni.navigateBack({
						delta: 1
					});
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 使用全局样式文件中的.common-container */

	.party-content {
		margin-bottom: 30rpx;
	}

	/* 使用全局样式文件中的.party-footer */
</style>
