{"version": 3, "file": "list.js", "sources": ["uview-plus_3.4.28/components/u-list/list.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:14:53\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/list.js\n */\nexport default {\n    // list 组件\n    list: {\n        showScrollbar: false,\n        lowerThreshold: 50,\n        upperThreshold: 0,\n        scrollTop: 0,\n        offsetAccuracy: 10,\n        enableFlex: false,\n        pagingEnabled: false,\n        scrollable: true,\n        scrollIntoView: '',\n        scrollWithAnimation: false,\n        enableBackToTop: false,\n        height: 0,\n        width: 0,\n        preLoadScreen: 1\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,gBAAgB;AAAA,IAChB,qBAAqB;AAAA,IACrB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,eAAe;AAAA,EAClB;AACL;;"}