{"version": 3, "file": "u-grid-item.js", "sources": ["uview-plus_3.4.28/components/u-grid-item/u-grid-item.vue", "D:/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/TTovd2luMTFEZXNrVG9wL3pvdWp1c2FpL1JlZFByb3RlY3Rpby91dmlldy1wbHVzXzMuNC4yOC9jb21wb25lbnRzL3UtZ3JpZC1pdGVtL3UtZ3JpZC1pdGVtLnZ1ZQ"], "sourcesContent": ["<template>\n\t<!-- #ifndef APP-NVUE -->\n\t<view\n\t\tv-if=\"parentData.col > 0\"\n\t    class=\"u-grid-item\"\n\t    hover-class=\"u-grid-item--hover-class\"\n\t    :hover-stay-time=\"200\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"classes\"\n\t    :style=\"[itemStyle]\"\n\t>\n\t\t<slot />\n\t</view>\n\t<!-- #endif -->\n\t<!-- #ifdef APP-NVUE -->\n\t<view\n\t    class=\"u-grid-item\"\n\t    :hover-stay-time=\"200\"\n\t    @tap=\"clickHandler\"\n\t    :class=\"classes\"\n\t    :style=\"[itemStyle]\"\n\t>\n\t\t<slot />\n\t</view>\n\t<!-- #endif -->\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * gridItem 提示\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。搭配u-grid使用\n\t * @tutorial https://ijry.github.io/uview-plus/components/grid.html\n\t * @property {String | Number}\tname\t\t宫格的name ( 默认 null )\n\t * @property {String}\t\t\tbgColor\t\t宫格的背景颜色 （默认 'transparent' ）\n\t * @property {Object}\t\t\tcustomStyle\t自定义样式，对象形式\n\t * @event {Function} click 点击宫格触发\n\t * @example <u-grid-item></u-grid-item>\n\t */\n\texport default {\n\t\tname: \"u-grid-item\",\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tparentData: {\n\t\t\t\t\tcol: 0, // 父组件划分的宫格数\n\t\t\t\t\tborder: true, // 是否显示边框，根据父组件决定\n\t\t\t\t},\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\twidth: 0, // nvue下才这么计算，vue下放到computed中，否则会因为延时造成闪烁\n\t\t\t\t// #endif\n\t\t\t\tclasses: [], // 类名集合，用于判断是否显示右边和下边框\n\t\t\t};\n\t\t},\n\t\tmounted() {\n\t\t\tthis.init()\n\t\t},\n\t\temits: ['click'],\n\t\t//  微信小程序中 options 选项\n\t\t// #ifdef MP-WEIXIN\n\t\toptions: {\n\t\t    virtualHost: true ,//将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等\n\t\t},\n\t\t// #endif\n\t\tcomputed: {\n\t\t\titemStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tbackground: this.bgColor\n\t\t\t\t}\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tstyle['width'] = this.width\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tstyle['width'] = '100%'\n\t\t\t\t// #endif\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 用于在父组件u-grid的children中被添加入子组件时，\n\t\t\t\t// 重新计算item的边框\n\t\t\t\tuni.$on('$uGridItem', () => {\n\t\t\t\t\tthis.gridItemClasses()\n\t\t\t\t})\n\t\t\t\t// 父组件的实例\n\t\t\t\tthis.updateParentData()\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 获取元素该有的长度，nvue下要延时才准确\n\t\t\t\tthis.$nextTick(function(){\n\t\t\t\t\tthis.getItemWidth()\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// 发出事件，通知所有的grid-item都重新计算自己的边框\n\t\t\t\tuni.$emit('$uGridItem')\n\t\t\t\tthis.gridItemClasses()\n\t\t\t},\n\t\t\t// 获取父组件的参数\n\t\t\tupdateParentData() {\n\t\t\t\t// 此方法写在mixin中\n\t\t\t\tthis.getParentData('u-grid');\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\tlet name = this.name\n\t\t\t\t// 如果没有设置name属性，历遍父组件的children数组，判断当前的元素是否和本实例this相等，找出当前组件的索引\n\t\t\t\tconst children = this.parent?.children\n\t\t\t\tif(children && this.name === null) {\n\t\t\t\t\tname = children.findIndex(child => child === this)\n\t\t\t\t}\n\t\t\t\t// 调用父组件方法，发出事件\n\t\t\t\tthis.parent && this.parent.childClick(name)\n\t\t\t\tthis.$emit('click', name)\n\t\t\t},\n\t\t\tasync getItemWidth() {\n\t\t\t\t// 如果是nvue，不能使用百分比，只能使用固定宽度\n\t\t\t\tlet width = 0\n\t\t\t\tif(this.parent) {\n\t\t\t\t\t// 获取父组件宽度后，除以栅格数，得出每个item的宽度\n\t\t\t\t\tconst parentWidth = await this.getParentWidth()\n\t\t\t\t\twidth = parentWidth / Number(this.parentData.col) + 'px'\n\t\t\t\t}\n\t\t\t\tthis.width = width\n\t\t\t},\n\t\t\t// 获取父元素的尺寸\n\t\t\tgetParentWidth() {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 返回一个promise，让调用者可以用await同步获取\n\t\t\t\tconst dom = uni.requireNativePlugin('dom')\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\t// 调用父组件的ref\n\t\t\t\t\tdom.getComponentRect(this.parent.$refs['u-grid'], res => {\n\t\t\t\t\t\tresolve(res.size.width)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgridItemClasses() {\n\t\t\t\tif(this.parentData.border) {\n\t\t\t\t\tlet classes = []\n\t\t\t\t\tthis.parent.children.map((child, index) =>{\n\t\t\t\t\t\tif(this === child) {\n\t\t\t\t\t\t\tconst len = this.parent.children.length\n\t\t\t\t\t\t\t// 贴近右边屏幕边沿的child，并且最后一个（比如只有横向2个的时候），无需右边框\n\t\t\t\t\t\t\tif((index + 1) % this.parentData.col !== 0 && index + 1 !== len) {\n\t\t\t\t\t\t\t\tclasses.push('u-border-right')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 总的宫格数量对列数取余的值\n\t\t\t\t\t\t\t// 如果取余后，值为0，则意味着要将最后一排的宫格，都不需要下边框\n\t\t\t\t\t\t\tconst lessNum = len % this.parentData.col === 0 ? this.parentData.col : len % this.parentData.col\n\t\t\t\t\t\t\t// 最下面的一排child，无需下边框\n\t\t\t\t\t\t\tif(index < len - lessNum) {\n\t\t\t\t\t\t\t\tclasses.push('u-border-bottom')\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n\t\t\t\t\t// #ifdef MP-ALIPAY || MP-TOUTIAO\n\t\t\t\t\tclasses = classes.join(' ')\n\t\t\t\t\t// #endif\n\t\t\t\t\tthis.classes = classes\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tbeforeUnmount() {\n\t\t\t// 移除事件监听，释放性能\n\t\t\tuni.$off('$uGridItem')\n\t\t}\n\t};\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n      $u-grid-item-hover-class-opcatiy:.5 !default;\n      $u-grid-item-margin-top:1rpx !default;\n      $u-grid-item-border-right-width:0.5px !default;\n      $u-grid-item-border-bottom-width:0.5px !default;\n      $u-grid-item-border-right-color:$u-border-color !default;\n      $u-grid-item-border-bottom-color:$u-border-color !default;\n\t.u-grid-item {\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tposition: relative;\n\t\tflex-direction: column;\n\t\t/* #ifndef APP-NVUE */\n\t\tbox-sizing: border-box;\n\t\tdisplay: flex;\n\t\t/* #endif */\n\n\t\t/* #ifdef MP */\n\t\tposition: relative;\n\t\tfloat: left;\n\t\t/* #endif */\n\n\t\t/* #ifdef MP-WEIXIN */\n\t\tmargin-top:$u-grid-item-margin-top;\n\t\t/* #endif */\n\n\t\t&--hover-class {\n\t\t\topacity:$u-grid-item-hover-class-opcatiy;\n\t\t}\n\t}\n\n\t/* #ifdef APP-NVUE */\n\t// 由于nvue不支持组件内引入app.vue中再引入的样式，所以需要写在这里\n\t.u-border-right {\n\t\tborder-right-width:$u-grid-item-border-right-width;\n\t\tborder-color: $u-grid-item-border-right-color;\n\t}\n\n\t.u-border-bottom {\n\t\tborder-bottom-width:$u-grid-item-border-bottom-width;\n\t\tborder-color:$u-grid-item-border-bottom-color;\n\t}\n\n\t/* #endif */\n</style>\n", "import Component from 'M:/win11DeskTop/zoujusai/RedProtectio/uview-plus_3.4.28/components/u-grid-item/u-grid-item.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "addStyle", "uni"], "mappings": ";;;;;;AA0CC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,oCAAAA,SAASC,kCAAK,OAAEC,iDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,YAAY;AAAA,QACX,KAAK;AAAA;AAAA,QACL,QAAQ;AAAA;AAAA,MACR;AAAA,MAID,SAAS,CAAE;AAAA;AAAA;EAEZ;AAAA,EACD,UAAU;AACT,SAAK,KAAK;AAAA,EACV;AAAA,EACD,OAAO,CAAC,OAAO;AAAA;AAAA,EAGf,SAAS;AAAA,IACL,aAAa;AAAA;AAAA,EAChB;AAAA,EAED,UAAU;AAAA,IACT,YAAY;AACX,YAAM,QAAQ;AAAA,QACb,YAAY,KAAK;AAAA,MAClB;AAKA,YAAM,OAAO,IAAI;AAEjB,aAAOC,qCAAS,UAAC,OAAOC,qCAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AAAA,EACD,SAAS;AAAA,IACR,OAAO;AAGNC,0BAAI,IAAI,cAAc,MAAM;AAC3B,aAAK,gBAAgB;AAAA,OACrB;AAED,WAAK,iBAAiB;AAQtBA,oBAAG,MAAC,MAAM,YAAY;AACtB,WAAK,gBAAgB;AAAA,IACrB;AAAA;AAAA,IAED,mBAAmB;AAElB,WAAK,cAAc,QAAQ;AAAA,IAC3B;AAAA,IACD,eAAe;;AACd,UAAI,OAAO,KAAK;AAEhB,YAAM,YAAW,UAAK,WAAL,mBAAa;AAC9B,UAAG,YAAY,KAAK,SAAS,MAAM;AAClC,eAAO,SAAS,UAAU,WAAS,UAAU,IAAI;AAAA,MAClD;AAEA,WAAK,UAAU,KAAK,OAAO,WAAW,IAAI;AAC1C,WAAK,MAAM,SAAS,IAAI;AAAA,IACxB;AAAA,IACD,MAAM,eAAe;AAEpB,UAAI,QAAQ;AACZ,UAAG,KAAK,QAAQ;AAEf,cAAM,cAAc,MAAM,KAAK,eAAe;AAC9C,gBAAQ,cAAc,OAAO,KAAK,WAAW,GAAG,IAAI;AAAA,MACrD;AACA,WAAK,QAAQ;AAAA,IACb;AAAA;AAAA,IAED,iBAAiB;AAAA,IAWhB;AAAA,IACD,kBAAkB;AACjB,UAAG,KAAK,WAAW,QAAQ;AAC1B,YAAI,UAAU,CAAC;AACf,aAAK,OAAO,SAAS,IAAI,CAAC,OAAO,UAAS;AACzC,cAAG,SAAS,OAAO;AAClB,kBAAM,MAAM,KAAK,OAAO,SAAS;AAEjC,iBAAI,QAAQ,KAAK,KAAK,WAAW,QAAQ,KAAK,QAAQ,MAAM,KAAK;AAChE,sBAAQ,KAAK,gBAAgB;AAAA,YAC9B;AAGA,kBAAM,UAAU,MAAM,KAAK,WAAW,QAAQ,IAAI,KAAK,WAAW,MAAM,MAAM,KAAK,WAAW;AAE9F,gBAAG,QAAQ,MAAM,SAAS;AACzB,sBAAQ,KAAK,iBAAiB;AAAA,YAC/B;AAAA,UACD;AAAA,SACA;AAKD,aAAK,UAAU;AAAA,MAChB;AAAA,IACD;AAAA,EACA;AAAA,EACD,gBAAgB;AAEfA,kBAAG,MAAC,KAAK,YAAY;AAAA,EACtB;;;;;;;;;;;;ACxKF,GAAG,gBAAgB,SAAS;"}