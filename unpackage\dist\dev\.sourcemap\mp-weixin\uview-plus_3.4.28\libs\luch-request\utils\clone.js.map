{"version": 3, "file": "clone.js", "sources": ["uview-plus_3.4.28/libs/luch-request/utils/clone.js"], "sourcesContent": ["/* eslint-disable */\r\nvar clone = (function() {\r\n  'use strict';\r\n\r\n  function _instanceof(obj, type) {\r\n    return type != null && obj instanceof type;\r\n  }\r\n\r\n  var nativeMap;\r\n  try {\r\n    nativeMap = Map;\r\n  } catch(_) {\r\n    // maybe a reference error because no `Map`. Give it a dummy value that no\r\n    // value will ever be an instanceof.\r\n    nativeMap = function() {};\r\n  }\r\n\r\n  var nativeSet;\r\n  try {\r\n    nativeSet = Set;\r\n  } catch(_) {\r\n    nativeSet = function() {};\r\n  }\r\n\r\n  var nativePromise;\r\n  try {\r\n    nativePromise = Promise;\r\n  } catch(_) {\r\n    nativePromise = function() {};\r\n  }\r\n\r\n  /**\r\n   * Clones (copies) an Object using deep copying.\r\n   *\r\n   * This function supports circular references by default, but if you are certain\r\n   * there are no circular references in your object, you can save some CPU time\r\n   * by calling clone(obj, false).\r\n   *\r\n   * Caution: if `circular` is false and `parent` contains circular references,\r\n   * your program may enter an infinite loop and crash.\r\n   *\r\n   * @param `parent` - the object to be cloned\r\n   * @param `circular` - set to true if the object to be cloned may contain\r\n   *    circular references. (optional - true by default)\r\n   * @param `depth` - set to a number if the object is only to be cloned to\r\n   *    a particular depth. (optional - defaults to Infinity)\r\n   * @param `prototype` - sets the prototype to be used when cloning an object.\r\n   *    (optional - defaults to parent prototype).\r\n   * @param `includeNonEnumerable` - set to true if the non-enumerable properties\r\n   *    should be cloned as well. Non-enumerable properties on the prototype\r\n   *    chain will be ignored. (optional - false by default)\r\n   */\r\n  function clone(parent, circular, depth, prototype, includeNonEnumerable) {\r\n    if (typeof circular === 'object') {\r\n      depth = circular.depth;\r\n      prototype = circular.prototype;\r\n      includeNonEnumerable = circular.includeNonEnumerable;\r\n      circular = circular.circular;\r\n    }\r\n    // maintain two arrays for circular references, where corresponding parents\r\n    // and children have the same index\r\n    var allParents = [];\r\n    var allChildren = [];\r\n\r\n    var useBuffer = typeof Buffer != 'undefined';\r\n\r\n    if (typeof circular == 'undefined')\r\n      circular = true;\r\n\r\n    if (typeof depth == 'undefined')\r\n      depth = Infinity;\r\n\r\n    // recurse this function so we don't reset allParents and allChildren\r\n    function _clone(parent, depth) {\r\n      // cloning null always returns null\r\n      if (parent === null)\r\n        return null;\r\n\r\n      if (depth === 0)\r\n        return parent;\r\n\r\n      var child;\r\n      var proto;\r\n      if (typeof parent != 'object') {\r\n        return parent;\r\n      }\r\n\r\n      if (_instanceof(parent, nativeMap)) {\r\n        child = new nativeMap();\r\n      } else if (_instanceof(parent, nativeSet)) {\r\n        child = new nativeSet();\r\n      } else if (_instanceof(parent, nativePromise)) {\r\n        child = new nativePromise(function (resolve, reject) {\r\n          parent.then(function(value) {\r\n            resolve(_clone(value, depth - 1));\r\n          }, function(err) {\r\n            reject(_clone(err, depth - 1));\r\n          });\r\n        });\r\n      } else if (clone.__isArray(parent)) {\r\n        child = [];\r\n      } else if (clone.__isRegExp(parent)) {\r\n        child = new RegExp(parent.source, __getRegExpFlags(parent));\r\n        if (parent.lastIndex) child.lastIndex = parent.lastIndex;\r\n      } else if (clone.__isDate(parent)) {\r\n        child = new Date(parent.getTime());\r\n      } else if (useBuffer && Buffer.isBuffer(parent)) {\r\n        if (Buffer.from) {\r\n          // Node.js >= 5.10.0\r\n          child = Buffer.from(parent);\r\n        } else {\r\n          // Older Node.js versions\r\n          child = new Buffer(parent.length);\r\n          parent.copy(child);\r\n        }\r\n        return child;\r\n      } else if (_instanceof(parent, Error)) {\r\n        child = Object.create(parent);\r\n      } else {\r\n        if (typeof prototype == 'undefined') {\r\n          proto = Object.getPrototypeOf(parent);\r\n          child = Object.create(proto);\r\n        }\r\n        else {\r\n          child = Object.create(prototype);\r\n          proto = prototype;\r\n        }\r\n      }\r\n\r\n      if (circular) {\r\n        var index = allParents.indexOf(parent);\r\n\r\n        if (index != -1) {\r\n          return allChildren[index];\r\n        }\r\n        allParents.push(parent);\r\n        allChildren.push(child);\r\n      }\r\n\r\n      if (_instanceof(parent, nativeMap)) {\r\n        parent.forEach(function(value, key) {\r\n          var keyChild = _clone(key, depth - 1);\r\n          var valueChild = _clone(value, depth - 1);\r\n          child.set(keyChild, valueChild);\r\n        });\r\n      }\r\n      if (_instanceof(parent, nativeSet)) {\r\n        parent.forEach(function(value) {\r\n          var entryChild = _clone(value, depth - 1);\r\n          child.add(entryChild);\r\n        });\r\n      }\r\n\r\n      for (var i in parent) {\r\n        var attrs = Object.getOwnPropertyDescriptor(parent, i);\r\n        if (attrs) {\r\n          child[i] = _clone(parent[i], depth - 1);\r\n        }\r\n\r\n        try {\r\n          var objProperty = Object.getOwnPropertyDescriptor(parent, i);\r\n          if (objProperty.set === 'undefined') {\r\n            // no setter defined. Skip cloning this property\r\n            continue;\r\n          }\r\n          child[i] = _clone(parent[i], depth - 1);\r\n        } catch(e){\r\n          if (e instanceof TypeError) {\r\n            // when in strict mode, TypeError will be thrown if child[i] property only has a getter\r\n            // we can't do anything about this, other than inform the user that this property cannot be set.\r\n            continue\r\n          } else if (e instanceof ReferenceError) {\r\n            //this may happen in non strict mode\r\n            continue\r\n          }\r\n        }\r\n\r\n      }\r\n\r\n      if (Object.getOwnPropertySymbols) {\r\n        var symbols = Object.getOwnPropertySymbols(parent);\r\n        for (var i = 0; i < symbols.length; i++) {\r\n          // Don't need to worry about cloning a symbol because it is a primitive,\r\n          // like a number or string.\r\n          var symbol = symbols[i];\r\n          var descriptor = Object.getOwnPropertyDescriptor(parent, symbol);\r\n          if (descriptor && !descriptor.enumerable && !includeNonEnumerable) {\r\n            continue;\r\n          }\r\n          child[symbol] = _clone(parent[symbol], depth - 1);\r\n          Object.defineProperty(child, symbol, descriptor);\r\n        }\r\n      }\r\n\r\n      if (includeNonEnumerable) {\r\n        var allPropertyNames = Object.getOwnPropertyNames(parent);\r\n        for (var i = 0; i < allPropertyNames.length; i++) {\r\n          var propertyName = allPropertyNames[i];\r\n          var descriptor = Object.getOwnPropertyDescriptor(parent, propertyName);\r\n          if (descriptor && descriptor.enumerable) {\r\n            continue;\r\n          }\r\n          child[propertyName] = _clone(parent[propertyName], depth - 1);\r\n          Object.defineProperty(child, propertyName, descriptor);\r\n        }\r\n      }\r\n\r\n      return child;\r\n    }\r\n\r\n    return _clone(parent, depth);\r\n  }\r\n\r\n  /**\r\n   * Simple flat clone using prototype, accepts only objects, usefull for property\r\n   * override on FLAT configuration object (no nested props).\r\n   *\r\n   * USE WITH CAUTION! This may not behave as you wish if you do not know how this\r\n   * works.\r\n   */\r\n  clone.clonePrototype = function clonePrototype(parent) {\r\n    if (parent === null)\r\n      return null;\r\n\r\n    var c = function () {};\r\n    c.prototype = parent;\r\n    return new c();\r\n  };\r\n\r\n// private utility functions\r\n\r\n  function __objToStr(o) {\r\n    return Object.prototype.toString.call(o);\r\n  }\r\n  clone.__objToStr = __objToStr;\r\n\r\n  function __isDate(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object Date]';\r\n  }\r\n  clone.__isDate = __isDate;\r\n\r\n  function __isArray(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object Array]';\r\n  }\r\n  clone.__isArray = __isArray;\r\n\r\n  function __isRegExp(o) {\r\n    return typeof o === 'object' && __objToStr(o) === '[object RegExp]';\r\n  }\r\n  clone.__isRegExp = __isRegExp;\r\n\r\n  function __getRegExpFlags(re) {\r\n    var flags = '';\r\n    if (re.global) flags += 'g';\r\n    if (re.ignoreCase) flags += 'i';\r\n    if (re.multiline) flags += 'm';\r\n    return flags;\r\n  }\r\n  clone.__getRegExpFlags = __getRegExpFlags;\r\n\r\n  return clone;\r\n})();\r\n\r\nexport default clone\r\n"], "names": ["clone", "parent", "depth"], "mappings": ";AACG,IAAC,QAAS,WAAW;AAGtB,WAAS,YAAY,KAAK,MAAM;AAC9B,WAAO,QAAQ,QAAQ,eAAe;AAAA,EACvC;AAED,MAAI;AACJ,MAAI;AACF,gBAAY;AAAA,EACb,SAAO,GAAG;AAGT,gBAAY,WAAW;AAAA,IAAA;AAAA,EACxB;AAED,MAAI;AACJ,MAAI;AACF,gBAAY;AAAA,EACb,SAAO,GAAG;AACT,gBAAY,WAAW;AAAA,IAAA;AAAA,EACxB;AAED,MAAI;AACJ,MAAI;AACF,oBAAgB;AAAA,EACjB,SAAO,GAAG;AACT,oBAAgB,WAAW;AAAA,IAAA;AAAA,EAC5B;AAuBD,WAASA,OAAM,QAAQ,UAAU,OAAO,WAAW,sBAAsB;AACvE,QAAI,OAAO,aAAa,UAAU;AAChC,cAAQ,SAAS;AACjB,kBAAY,SAAS;AACrB,6BAAuB,SAAS;AAChC,iBAAW,SAAS;AAAA,IACrB;AAGD,QAAI,aAAa,CAAA;AACjB,QAAI,cAAc,CAAA;AAElB,QAAI,YAAY,OAAO,UAAU;AAEjC,QAAI,OAAO,YAAY;AACrB,iBAAW;AAEb,QAAI,OAAO,SAAS;AAClB,cAAQ;AAGV,aAAS,OAAOC,SAAQC,QAAO;AAE7B,UAAID,YAAW;AACb,eAAO;AAET,UAAIC,WAAU;AACZ,eAAOD;AAET,UAAI;AACJ,UAAI;AACJ,UAAI,OAAOA,WAAU,UAAU;AAC7B,eAAOA;AAAA,MACR;AAED,UAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,gBAAQ,IAAI;MACb,WAAU,YAAYA,SAAQ,SAAS,GAAG;AACzC,gBAAQ,IAAI;MACb,WAAU,YAAYA,SAAQ,aAAa,GAAG;AAC7C,gBAAQ,IAAI,cAAc,SAAU,SAAS,QAAQ;AACnD,UAAAA,QAAO,KAAK,SAAS,OAAO;AAC1B,oBAAQ,OAAO,OAAOC,SAAQ,CAAC,CAAC;AAAA,UACjC,GAAE,SAAS,KAAK;AACf,mBAAO,OAAO,KAAKA,SAAQ,CAAC,CAAC;AAAA,UACzC,CAAW;AAAA,QACX,CAAS;AAAA,MACF,WAAUF,OAAM,UAAUC,OAAM,GAAG;AAClC,gBAAQ,CAAA;AAAA,MACT,WAAUD,OAAM,WAAWC,OAAM,GAAG;AACnC,gBAAQ,IAAI,OAAOA,QAAO,QAAQ,iBAAiBA,OAAM,CAAC;AAC1D,YAAIA,QAAO;AAAW,gBAAM,YAAYA,QAAO;AAAA,MAChD,WAAUD,OAAM,SAASC,OAAM,GAAG;AACjC,gBAAQ,IAAI,KAAKA,QAAO,QAAS,CAAA;AAAA,MAClC,WAAU,aAAa,OAAO,SAASA,OAAM,GAAG;AAC/C,YAAI,OAAO,MAAM;AAEf,kBAAQ,OAAO,KAAKA,OAAM;AAAA,QACpC,OAAe;AAEL,kBAAQ,IAAI,OAAOA,QAAO,MAAM;AAChC,UAAAA,QAAO,KAAK,KAAK;AAAA,QAClB;AACD,eAAO;AAAA,MACR,WAAU,YAAYA,SAAQ,KAAK,GAAG;AACrC,gBAAQ,OAAO,OAAOA,OAAM;AAAA,MACpC,OAAa;AACL,YAAI,OAAO,aAAa,aAAa;AACnC,kBAAQ,OAAO,eAAeA,OAAM;AACpC,kBAAQ,OAAO,OAAO,KAAK;AAAA,QAC5B,OACI;AACH,kBAAQ,OAAO,OAAO,SAAS;AAC/B,kBAAQ;AAAA,QACT;AAAA,MACF;AAED,UAAI,UAAU;AACZ,YAAI,QAAQ,WAAW,QAAQA,OAAM;AAErC,YAAI,SAAS,IAAI;AACf,iBAAO,YAAY,KAAK;AAAA,QACzB;AACD,mBAAW,KAAKA,OAAM;AACtB,oBAAY,KAAK,KAAK;AAAA,MACvB;AAED,UAAI,YAAYA,SAAQ,SAAS,GAAG;AAClC,QAAAA,QAAO,QAAQ,SAAS,OAAO,KAAK;AAClC,cAAI,WAAW,OAAO,KAAKC,SAAQ,CAAC;AACpC,cAAI,aAAa,OAAO,OAAOA,SAAQ,CAAC;AACxC,gBAAM,IAAI,UAAU,UAAU;AAAA,QACxC,CAAS;AAAA,MACF;AACD,UAAI,YAAYD,SAAQ,SAAS,GAAG;AAClC,QAAAA,QAAO,QAAQ,SAAS,OAAO;AAC7B,cAAI,aAAa,OAAO,OAAOC,SAAQ,CAAC;AACxC,gBAAM,IAAI,UAAU;AAAA,QAC9B,CAAS;AAAA,MACF;AAED,eAAS,KAAKD,SAAQ;AACpB,YAAI,QAAQ,OAAO,yBAAyBA,SAAQ,CAAC;AACrD,YAAI,OAAO;AACT,gBAAM,CAAC,IAAI,OAAOA,QAAO,CAAC,GAAGC,SAAQ,CAAC;AAAA,QACvC;AAED,YAAI;AACF,cAAI,cAAc,OAAO,yBAAyBD,SAAQ,CAAC;AAC3D,cAAI,YAAY,QAAQ,aAAa;AAEnC;AAAA,UACD;AACD,gBAAM,CAAC,IAAI,OAAOA,QAAO,CAAC,GAAGC,SAAQ,CAAC;AAAA,QACvC,SAAO,GAAE;AACR,cAAI,aAAa,WAAW;AAG1B;AAAA,UACZ,WAAqB,aAAa,gBAAgB;AAEtC;AAAA,UACD;AAAA,QACF;AAAA,MAEF;AAED,UAAI,OAAO,uBAAuB;AAChC,YAAI,UAAU,OAAO,sBAAsBD,OAAM;AACjD,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAGvC,cAAI,SAAS,QAAQ,CAAC;AACtB,cAAI,aAAa,OAAO,yBAAyBA,SAAQ,MAAM;AAC/D,cAAI,cAAc,CAAC,WAAW,cAAc,CAAC,sBAAsB;AACjE;AAAA,UACD;AACD,gBAAM,MAAM,IAAI,OAAOA,QAAO,MAAM,GAAGC,SAAQ,CAAC;AAChD,iBAAO,eAAe,OAAO,QAAQ,UAAU;AAAA,QAChD;AAAA,MACF;AAED,UAAI,sBAAsB;AACxB,YAAI,mBAAmB,OAAO,oBAAoBD,OAAM;AACxD,iBAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAChD,cAAI,eAAe,iBAAiB,CAAC;AACrC,cAAI,aAAa,OAAO,yBAAyBA,SAAQ,YAAY;AACrE,cAAI,cAAc,WAAW,YAAY;AACvC;AAAA,UACD;AACD,gBAAM,YAAY,IAAI,OAAOA,QAAO,YAAY,GAAGC,SAAQ,CAAC;AAC5D,iBAAO,eAAe,OAAO,cAAc,UAAU;AAAA,QACtD;AAAA,MACF;AAED,aAAO;AAAA,IACR;AAED,WAAO,OAAO,QAAQ,KAAK;AAAA,EAC5B;AASD,EAAAF,OAAM,iBAAiB,SAAS,eAAe,QAAQ;AACrD,QAAI,WAAW;AACb,aAAO;AAET,QAAI,IAAI,WAAY;AAAA;AACpB,MAAE,YAAY;AACd,WAAO,IAAI,EAAC;AAAA,EAChB;AAIE,WAAS,WAAW,GAAG;AACrB,WAAO,OAAO,UAAU,SAAS,KAAK,CAAC;AAAA,EACxC;AACD,EAAAA,OAAM,aAAa;AAEnB,WAAS,SAAS,GAAG;AACnB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACnD;AACD,EAAAA,OAAM,WAAW;AAEjB,WAAS,UAAU,GAAG;AACpB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACnD;AACD,EAAAA,OAAM,YAAY;AAElB,WAAS,WAAW,GAAG;AACrB,WAAO,OAAO,MAAM,YAAY,WAAW,CAAC,MAAM;AAAA,EACnD;AACD,EAAAA,OAAM,aAAa;AAEnB,WAAS,iBAAiB,IAAI;AAC5B,QAAI,QAAQ;AACZ,QAAI,GAAG;AAAQ,eAAS;AACxB,QAAI,GAAG;AAAY,eAAS;AAC5B,QAAI,GAAG;AAAW,eAAS;AAC3B,WAAO;AAAA,EACR;AACD,EAAAA,OAAM,mBAAmB;AAEzB,SAAOA;AACT,EAAC;;"}