"use strict";
const common_vendor = require("../../common/vendor.js");
const api_news = require("../../api/news.js");
const api_menus = require("../../api/menus.js");
const api_test = require("../../api/test.js");
const utils_image = require("../../utils/image.js");
const CustomNavbar = () => "../../components/common/custom-navbar.js";
const _sfc_main = {
  components: {
    CustomNavbar
  },
  data() {
    return {
      // 轮播图数据
      swiperList: [
        {
          src: "/static/images/1.png",
          title: "学习贯彻习近平新时代中国特色社会主义思想"
        },
        {
          src: "/static/images/2.png",
          title: "深入学习贯彻党的二十大精神"
        },
        {
          src: "/static/images/3.png",
          title: "喜迎建党102周年系列活动"
        }
      ],
      // 公告通知数据
      noticeList: [
        {
          id: 1,
          title: "关于开展2023年度党员民主评议工作的通知"
        },
        {
          id: 2,
          title: "关于召开2023年度组织生活会的通知"
        },
        {
          id: 3,
          title: "关于开展党史学习教育的通知"
        }
      ],
      // 快捷入口数据
      quickEntryList: [
        {
          id: 1,
          title: "党建工作",
          icon: "home-fill",
          url: "/pages/party/index"
        },
        {
          id: 2,
          title: "基层动态",
          icon: "bell-fill",
          url: ""
        },
        {
          id: 3,
          title: "党建品牌",
          icon: "star-fill",
          url: ""
        },
        {
          id: 4,
          title: "红旗支部",
          icon: "heart-fill",
          url: ""
        },
        {
          id: 5,
          title: "先锋党员",
          icon: "account-fill",
          url: ""
        },
        {
          id: 6,
          title: "学习资料",
          icon: "bookmark-fill",
          url: ""
        },
        {
          id: 7,
          title: "志愿服务",
          icon: "gift-fill",
          url: ""
        },
        {
          id: 8,
          title: "党费交纳",
          icon: "rmb-circle-fill",
          url: "",
          appId: "wxc324e5e2fb50b1c6"
          // 党费通小程序的appId
        }
      ],
      // 党建要闻数据
      newsList: [],
      // 底部导航数据
      bottomNavList: [],
      // 菜单图标映射
      menuIconMap: {
        "党建信息": "home-fill",
        "阵地建设": "star-fill",
        "学习园地": "bookmark-fill",
        "示范典型": "heart-fill",
        "群团工作": "account-fill"
      },
      // 广告图数据
      adImage: null,
      // 广告图URL
      adImageUrl: ""
    };
  },
  onLoad() {
    common_vendor.index.__f__("log", "at pages/index/index.vue:224", "开始测试API请求...");
    api_test.testGetArticleList();
    this.fetchArticleList();
    this.fetchMenuList();
    this.fetchAdImage();
  },
  methods: {
    // 获取文章列表
    async fetchArticleList() {
      try {
        const result = await api_news.getArticleList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:243", "获取文章列表成功:", result);
        if (result && result.success && Array.isArray(result.data)) {
          this.newsList = result.data;
          common_vendor.index.__f__("log", "at pages/index/index.vue:247", "文章列表数据已更新");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:250", "获取文章列表失败:", error);
      }
    },
    // 轮播图点击事件
    onSwiperClick(index) {
      common_vendor.index.showToast({
        title: "点击了轮播图：" + this.swiperList[index].title,
        icon: "none"
      });
    },
    // 搜索点击事件
    onSearchClick(e) {
      common_vendor.index.showToast({
        title: "搜索内容：" + (e.value || ""),
        icon: "none"
      });
    },
    // 公告点击事件
    onNoticeClick(index) {
      const item = this.noticeList[index];
      common_vendor.index.showToast({
        title: "点击了公告：" + item.title,
        icon: "none"
      });
    },
    // 快捷入口点击事件
    onGridClick(index) {
      const item = this.quickEntryList[index];
      common_vendor.index.__f__("log", "at pages/index/index.vue:278", "点击了快捷入口:", item);
      if (item.appId) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:282", "跳转到小程序，appId:", item.appId);
        this.navigateToMiniProgram(item);
      } else if (item.url) {
        common_vendor.index.__f__("log", "at pages/index/index.vue:286", "跳转到URL:", item.url);
        common_vendor.index.navigateTo({
          url: item.url,
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/index/index.vue:291", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      } else {
        common_vendor.index.showToast({
          title: "暂未配置：" + item.title,
          icon: "none"
        });
      }
    },
    // 跳转到小程序
    navigateToMiniProgram(item) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:308", "准备跳转到小程序:", item.title, "appId:", item.appId);
      common_vendor.index.navigateToMiniProgram({
        appId: item.appId,
        path: "",
        // 打开小程序首页，可以根据需要指定具体页面
        extraData: {
          // 可以传递一些数据给目标小程序
          source: "红色环投",
          from: "quickEntry"
        },
        envVersion: "release",
        // 正式版
        success(res) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:320", "跳转小程序成功:", res);
        },
        fail(err) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:323", "跳转小程序失败:", err);
          common_vendor.index.showToast({
            title: "无法打开" + item.title,
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    // 新闻点击事件
    onNewsClick(item) {
      common_vendor.index.__f__("log", "at pages/index/index.vue:334", "点击了文章:", item);
      common_vendor.index.navigateTo({
        url: `/pages/article/detail?id=${item.id}&type=news`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:339", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 获取菜单列表
    async fetchMenuList() {
      try {
        const result = await api_menus.getMenuList();
        common_vendor.index.__f__("log", "at pages/index/index.vue:351", "获取菜单列表成功:", result);
        if (result && result.success && Array.isArray(result.data)) {
          const firstLevelMenus = result.data.filter((item) => item.level === 1);
          this.bottomNavList = firstLevelMenus.map((item) => {
            return {
              ...item,
              icon: this.menuIconMap[item.name] || "home-fill"
            };
          });
          common_vendor.index.__f__("log", "at pages/index/index.vue:365", "底部导航菜单数据已更新:", this.bottomNavList);
          const secondLevelMenus = result.data.filter((item) => item.level === 2);
          this.quickEntryList.forEach((quickEntry, index) => {
            const matchedMenu = secondLevelMenus.find((menu) => menu.name === quickEntry.title);
            if (matchedMenu) {
              const parentMenu = firstLevelMenus.find((menu) => menu.id === matchedMenu.parentId);
              if (parentMenu) {
                this.quickEntryList[index].url = `/pages/menus/index?id=${parentMenu.id}&name=${encodeURIComponent(parentMenu.name)}&subMenuId=${matchedMenu.id}`;
                common_vendor.index.__f__("log", "at pages/index/index.vue:388", `已设置快捷入口 "${quickEntry.title}" 的URL:`, this.quickEntryList[index].url);
              }
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:395", "获取菜单列表失败:", error);
      }
    },
    // 广告图点击事件
    onAdClick() {
      common_vendor.index.__f__("log", "at pages/index/index.vue:405", "点击了广告图");
      common_vendor.index.showToast({
        title: "点击了广告图",
        icon: "none"
      });
    },
    // 底部导航点击事件
    onNavClick(index) {
      const item = this.bottomNavList[index];
      common_vendor.index.__f__("log", "at pages/index/index.vue:415", "点击了底部导航:", item);
      common_vendor.index.navigateTo({
        url: `/pages/menus/index?id=${item.id}&name=${encodeURIComponent(item.name)}`,
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:421", "跳转失败:", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    },
    // 获取广告图片数据
    async fetchAdImage() {
      try {
        const imageInfo = await utils_image.fetchImageById(36);
        common_vendor.index.__f__("log", "at pages/index/index.vue:435", "获取广告图片信息成功:", imageInfo);
        if (imageInfo && imageInfo.url) {
          this.adImage = imageInfo;
          this.adImageUrl = utils_image.getFullImageUrl(imageInfo.url);
          common_vendor.index.__f__("log", "at pages/index/index.vue:441", "广告图片URL:", this.adImageUrl);
        } else {
          common_vendor.index.__f__("error", "at pages/index/index.vue:443", "广告图片信息不完整");
          this.adImageUrl = "/static/images/640.gif";
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:448", "获取广告图片信息失败:", error);
        this.adImageUrl = "/static/images/640.gif";
      }
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _easycom_u_search2 = common_vendor.resolveComponent("u-search");
  const _easycom_u_image2 = common_vendor.resolveComponent("u-image");
  const _easycom_u_swiper2 = common_vendor.resolveComponent("u-swiper");
  const _easycom_u_notice_bar2 = common_vendor.resolveComponent("u-notice-bar");
  const _easycom_u_text2 = common_vendor.resolveComponent("u-text");
  const _easycom_u_grid_item2 = common_vendor.resolveComponent("u-grid-item");
  const _easycom_u_grid2 = common_vendor.resolveComponent("u-grid");
  const _easycom_u_cell2 = common_vendor.resolveComponent("u-cell");
  const _easycom_u_list2 = common_vendor.resolveComponent("u-list");
  (_easycom_u_icon2 + _component_custom_navbar + _easycom_u_search2 + _easycom_u_image2 + _easycom_u_swiper2 + _easycom_u_notice_bar2 + _easycom_u_text2 + _easycom_u_grid_item2 + _easycom_u_grid2 + _easycom_u_cell2 + _easycom_u_list2)();
}
const _easycom_u_icon = () => "../../components/u-icon/u-icon.js";
const _easycom_u_search = () => "../../uview-plus_3.4.28/components/u-search/u-search.js";
const _easycom_u_image = () => "../../uview-plus_3.4.28/components/u-image/u-image.js";
const _easycom_u_swiper = () => "../../uview-plus_3.4.28/components/u-swiper/u-swiper.js";
const _easycom_u_notice_bar = () => "../../uview-plus_3.4.28/components/u-notice-bar/u-notice-bar.js";
const _easycom_u_text = () => "../../uview-plus_3.4.28/components/u-text/u-text.js";
const _easycom_u_grid_item = () => "../../uview-plus_3.4.28/components/u-grid-item/u-grid-item.js";
const _easycom_u_grid = () => "../../uview-plus_3.4.28/components/u-grid/u-grid.js";
const _easycom_u_cell = () => "../../uview-plus_3.4.28/components/u-cell/u-cell.js";
const _easycom_u_list = () => "../../uview-plus_3.4.28/components/u-list/u-list.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_search + _easycom_u_image + _easycom_u_swiper + _easycom_u_notice_bar + _easycom_u_text + _easycom_u_grid_item + _easycom_u_grid + _easycom_u_cell + _easycom_u_list)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.p({
      name: "more-dot-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    b: common_vendor.p({
      name: "camera-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    c: common_vendor.p({
      title: "红色环投",
      showBack: false,
      showHome: false
    }),
    d: common_vendor.o($options.onSearchClick),
    e: common_vendor.p({
      placeholder: "搜索",
      placeholderColor: "#000000",
      disabled: true,
      shape: "round"
    }),
    f: common_vendor.o($options.onAdClick),
    g: common_vendor.p({
      src: $data.adImageUrl,
      width: "100%",
      height: "120",
      mode: "widthFix",
      fade: true,
      borderRadius: 8
    }),
    h: common_vendor.o($options.onSwiperClick),
    i: common_vendor.p({
      list: $data.swiperList,
      height: "300",
      radius: "8",
      autoplay: true,
      interval: 3e3,
      indicator: true,
      indicatorMode: "dot",
      keyName: "src",
      showTitle: true
    }),
    j: common_vendor.o($options.onNoticeClick),
    k: common_vendor.p({
      text: $data.noticeList.map((item) => item.title),
      direction: "column",
      duration: 3e3,
      speed: 80,
      color: "#333333",
      bgColor: "#ffffff",
      justifyContent: "flex-start"
    }),
    l: common_vendor.p({
      text: "快捷入口",
      size: "16",
      bold: true,
      color: "#333333"
    }),
    m: common_vendor.f($data.quickEntryList, (item, index, i0) => {
      return {
        a: "13664613-10-" + i0 + "," + ("13664613-9-" + i0),
        b: common_vendor.p({
          name: item.icon || "star-fill",
          size: "36",
          color: "#E51C23"
        }),
        c: "13664613-11-" + i0 + "," + ("13664613-9-" + i0),
        d: common_vendor.p({
          text: item.title,
          align: "center",
          size: "16",
          ["margin-top"]: "4"
        }),
        e: index,
        f: "13664613-9-" + i0 + ",13664613-8",
        g: common_vendor.p({
          index
        })
      };
    }),
    n: common_vendor.o($options.onGridClick),
    o: common_vendor.p({
      col: 4,
      border: false
    }),
    p: common_vendor.p({
      text: "党建信息",
      size: "16",
      bold: true,
      color: "#333333"
    }),
    q: common_vendor.f($data.newsList, (item, index, i0) => {
      return {
        a: index,
        b: common_vendor.o(($event) => $options.onNewsClick(item), index),
        c: "13664613-14-" + i0 + ",13664613-13",
        d: common_vendor.p({
          title: item.title,
          titleStyle: {
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap"
          },
          isLink: true
        })
      };
    }),
    r: common_vendor.p({
      ["enable-flex"]: true,
      height: "500px"
    }),
    s: common_vendor.f($data.bottomNavList, (item, index, i0) => {
      return {
        a: "13664613-17-" + i0 + "," + ("13664613-16-" + i0),
        b: common_vendor.p({
          name: item.icon || "home",
          size: "24",
          color: "#FF0000"
        }),
        c: "13664613-18-" + i0 + "," + ("13664613-16-" + i0),
        d: common_vendor.p({
          text: item.name,
          align: "center",
          size: "14",
          ["margin-top"]: "4"
        }),
        e: index,
        f: "13664613-16-" + i0 + ",13664613-15",
        g: common_vendor.p({
          index
        })
      };
    }),
    t: common_vendor.o($options.onNavClick),
    v: common_vendor.p({
      col: 5,
      border: false
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
