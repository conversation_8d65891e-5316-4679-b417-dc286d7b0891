"use strict";const e=require("../../libs/vue.js"),o=require("../../libs/config/props.js"),t=e.defineMixin({props:{text:{type:String,default:()=>o.props.rowNotice.text},icon:{type:String,default:()=>o.props.rowNotice.icon},mode:{type:String,default:()=>o.props.rowNotice.mode},color:{type:String,default:()=>o.props.rowNotice.color},bgColor:{type:String,default:()=>o.props.rowNotice.bgColor},fontSize:{type:[String,Number],default:()=>o.props.rowNotice.fontSize},speed:{type:[String,Number],default:()=>o.props.rowNotice.speed}}});exports.props=t;
