{"version": 3, "file": "config.js", "sources": ["uview-plus_3.4.28/libs/config/config.js"], "sourcesContent": ["const version = '3'\n\n// 开发环境才提示，生产环境不会提示\nif (process.env.NODE_ENV === 'development') {\n\tconsole.log(`\\n %c uview-plus V${version} %c https://ijry.github.io/uview-plus/ \\n\\n`, 'color: #ffffff; background: #3c9cff; padding:5px 0;', 'color: #3c9cff;background: #ffffff; padding:5px 0;');\n}\n\nexport default {\n    v: version,\n    version,\n    // 主题名称\n    type: [\n        'primary',\n        'success',\n        'info',\n        'error',\n        'warning'\n    ],\n    // 颜色部分，本来可以通过scss的:export导出供js使用，但是奈何nvue不支持\n    color: {\n        'u-primary': '#2979ff',\n        'u-warning': '#ff9900',\n        'u-success': '#19be6b',\n        'u-error': '#fa3534',\n        'u-info': '#909399',\n        'u-main-color': '#303133',\n        'u-content-color': '#606266',\n        'u-tips-color': '#909399',\n        'u-light-color': '#c0c4cc',\n        'up-primary': '#2979ff',\n        'up-warning': '#ff9900',\n        'up-success': '#19be6b',\n        'up-error': '#fa3534',\n        'up-info': '#909399',\n        'up-main-color': '#303133',\n        'up-content-color': '#606266',\n        'up-tips-color': '#909399',\n        'up-light-color': '#c0c4cc'\n    },\n    // 字体图标地址\n    iconUrl: 'https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf',\n     // 自定义图标\n    customIcon: {\n        family: '',\n        url: ''\n    },\n    customIcons: {}, // 自定义图标与unicode对应关系\n\t// 默认单位，可以通过配置为rpx，那么在用于传入组件大小参数为数值时，就默认为rpx\n\tunit: 'px',\n\t// 拦截器\n\tinterceptor: {\n\t\tnavbarLeftClick: null\n\t}\n}\n"], "names": [], "mappings": ";;AAAA,MAAM,UAAU;AAG4B;mFAC/B;AAAA,kBAAqB,OAAO;AAAA;AAAA,GAA+C,uDAAuD,oDAAoD;AACnM;AAEA,MAAe,SAAA;AAAA,EACX,GAAG;AAAA,EACH;AAAA;AAAA,EAEA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA;AAAA,EAEA,OAAO;AAAA,IACH,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,gBAAgB;AAAA,IAChB,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,kBAAkB;AAAA,EACtB;AAAA;AAAA,EAEA,SAAS;AAAA;AAAA,EAET,YAAY;AAAA,IACR,QAAQ;AAAA,IACR,KAAK;AAAA,EACT;AAAA,EACA,aAAa,CAAC;AAAA;AAAA;AAAA,EAEjB,MAAM;AAAA;AAAA,EAEN,aAAa;AAAA,IACZ,iBAAiB;AAAA,EAClB;AACD;;"}