<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮类型</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="默认按钮"
					    size="normal"
					    type="info"
						@click="click"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="成功按钮"
					    size="normal"
					    type="success"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="危险按钮"
					    size="normal"
					    type="error"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="主要按钮"
					    size="normal"
					    type="primary"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="警告按钮"
					    size="normal"
					    type="warning"
					></up-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">镂空按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="镂空按钮"
					    size="normal"
					    type="info"
					    plain
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="镂空按钮"
					    size="normal"
					    type="success"
					    plain
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="镂空按钮"
					    size="normal"
					    type="error"
					    plain
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="镂空按钮"
					    size="normal"
					    type="primary"
					    plain
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="镂空按钮"
					    size="normal"
					    type="warning"
					    plain
					></up-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">细边按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="细边按钮"
					    size="normal"
					    type="info"
					    plain
					    hairline
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="细边按钮"
					    size="normal"
					    type="success"
					    plain
					    hairline
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="细边按钮"
					    size="normal"
					    type="error"
					    plain
					    hairline
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="细边按钮"
					    size="normal"
					    type="primary"
					    plain
					    hairline
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="细边按钮"
					    size="normal"
					    type="warning"
					    plain
					    hairline
					></up-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">禁用按钮</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="info"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="success"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="error"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="primary"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    disabled
					    text="禁用按钮"
					    size="normal"
					    type="warning"
					></up-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">加载中</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    loadingText="加载中"
					    size="normal"
						loading
					    loadingMode="circle"
					    type="success"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    class="button-layout__item"
					    loadingText="加载中"
					    size="normal"
					    loading
					    type="error"
					></up-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">按钮图标&按钮形状</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="按钮图标"
					    size="normal"
					    icon="map"
					    plain
					    type="warning"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="按钮图标"
					    size="normal"
					    plain
					    shape="circle"
					    type="success"
					></up-button>
				</view>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义颜色</text>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="渐变色按钮"
					    size="normal"
					    color="linear-gradient(to right, rgb(66, 83, 216), rgb(213, 51, 186))"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="渐变色按钮"
					    size="normal"
					    color="linear-gradient(to right, rgb(220, 194, 11), rgb(4, 151, 99))"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="青绿色按钮"
					    size="normal"
					    color="rgb(10, 185, 156)"
					></up-button>
				</view>
			</view>
		</view>

		<view class="u-demo-block">
			<text class="u-demo-block__title">自定义大小</text>
			<view class="u-demo-block__content" style="padding-bottom: 15px; flex-direction: column;align-items: stretch;flex-wrap: nowrap;">
				<up-button
				    text="超大尺寸"
				    size="large"
				    type="success"
				></up-button>
			</view>
			<view class="u-demo-block__content">
				<view class="u-page__button-item">
					<up-button
					    text="普通尺寸"
					    size="normal"
					    type="error"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    text="小型尺寸"
					    size="small"
					    type="primary"
					></up-button>
				</view>
				<view class="u-page__button-item">
					<up-button
					    class="button-layout__item"
					    text="超小尺寸"
					    size="mini"
					    type="warning"
					></up-button>
				</view>
			</view>
		</view>
		<up-action-sheet
			v-model:show="show"
			:actions="[{
					name: '选项1',
				},
				{
					name: '选项2',
				}]"
			:closeOnClickOverlay="false"
		>
		</up-action-sheet>
	</view>
</template>

<script>
	import { http } from '@/uni_modules/uview-plus'
	export default {
		data() {
			return {
				show: false,
				// type: 'default',
				// disabled: false
			}
		},
		onLoad() {
			setTimeout(() => {
				this.type = 'primary'
				this.disabled = true
			}, 2000)
		},
		methods: {
			click() {
				this.show = true;
				console.log('click');
				http.post('/api/v1/core/user/login', {params: {account: 'name', password: '123456'}}).then(res => {
					console.log(res)
				}).catch(err => {
					console.log(err)
				})
			}
		}
	}
</script>

<style lang="scss">
	.u-page {
		&__button-item {
			margin: 0 15px 15px 0;
		}
	}

	.u-demo-block__content {
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;
	}
</style>
