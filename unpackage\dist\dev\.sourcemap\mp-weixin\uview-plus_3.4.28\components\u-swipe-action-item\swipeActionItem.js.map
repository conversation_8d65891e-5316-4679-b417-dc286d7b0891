{"version": 3, "file": "swipeActionItem.js", "sources": ["uview-plus_3.4.28/components/u-swipe-action-item/swipeActionItem.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:01:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swipeActionItem.js\n */\nexport default {\n    // swipeActionItem 组件\n    swipeActionItem: {\n        show: false,\n        closeOnClick: true,\n        name: '',\n        disabled: false,\n        threshold: 20,\n        autoClose: true,\n        options: [],\n        duration: 300\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,kBAAA;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,MAAM;AAAA,IACN,cAAc;AAAA,IACd,MAAM;AAAA,IACN,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS,CAAE;AAAA,IACX,UAAU;AAAA,EACb;AACL;;"}