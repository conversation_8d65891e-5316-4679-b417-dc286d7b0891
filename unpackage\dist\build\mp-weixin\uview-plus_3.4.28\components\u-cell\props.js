"use strict";const e=require("../../libs/vue.js"),l=require("../../libs/config/props.js"),t=e.defineMixin({props:{title:{type:[String,Number],default:()=>l.props.cell.title},label:{type:[String,Number],default:()=>l.props.cell.label},value:{type:[String,Number],default:()=>l.props.cell.value},icon:{type:String,default:()=>l.props.cell.icon},disabled:{type:<PERSON><PERSON><PERSON>,default:()=>l.props.cell.disabled},border:{type:<PERSON><PERSON><PERSON>,default:()=>l.props.cell.border},center:{type:<PERSON><PERSON><PERSON>,default:()=>l.props.cell.center},url:{type:String,default:()=>l.props.cell.url},linkType:{type:String,default:()=>l.props.cell.linkType},clickable:{type:<PERSON><PERSON><PERSON>,default:()=>l.props.cell.clickable},isLink:{type:<PERSON><PERSON><PERSON>,default:()=>l.props.cell.isLink},required:{type:<PERSON>olean,default:()=>l.props.cell.required},rightIcon:{type:String,default:()=>l.props.cell.rightIcon},arrowDirection:{type:String,default:()=>l.props.cell.arrowDirection},iconStyle:{type:[Object,String],default:()=>l.props.cell.iconStyle},rightIconStyle:{type:[Object,String],default:()=>l.props.cell.rightIconStyle},titleStyle:{type:[Object,String],default:()=>l.props.cell.titleStyle},size:{type:String,default:()=>l.props.cell.size},stop:{type:Boolean,default:()=>l.props.cell.stop},name:{type:[Number,String],default:()=>l.props.cell.name}}});exports.props=t;
