"use strict";const e=require("../../libs/vue.js"),r=require("../../libs/config/props.js"),t=e.defineMixin({props:{shape:{type:String,default:()=>r.props.search.shape},bgColor:{type:String,default:()=>r.props.search.bgColor},placeholder:{type:String,default:()=>r.props.search.placeholder},clearabled:{type:<PERSON>olean,default:()=>r.props.search.clearabled},focus:{type:<PERSON><PERSON><PERSON>,default:()=>r.props.search.focus},showAction:{type:<PERSON><PERSON><PERSON>,default:()=>r.props.search.showAction},actionStyle:{type:Object,default:()=>r.props.search.actionStyle},actionText:{type:String,default:()=>r.props.search.actionText},inputAlign:{type:String,default:()=>r.props.search.inputAlign},inputStyle:{type:Object,default:()=>r.props.search.inputStyle},disabled:{type:Boolean,default:()=>r.props.search.disabled},borderColor:{type:String,default:()=>r.props.search.borderColor},searchIconColor:{type:String,default:()=>r.props.search.searchIconColor},color:{type:String,default:()=>r.props.search.color},placeholderColor:{type:String,default:()=>r.props.search.placeholderColor},searchIcon:{type:String,default:()=>r.props.search.searchIcon},searchIconSize:{type:[Number,String],default:()=>r.props.search.searchIconSize},margin:{type:String,default:()=>r.props.search.margin},animation:{type:Boolean,default:()=>r.props.search.animation},modelValue:{type:String,default:()=>r.props.search.value},value:{type:String,default:()=>r.props.search.value},maxlength:{type:[String,Number],default:()=>r.props.search.maxlength},height:{type:[String,Number],default:()=>r.props.search.height},label:{type:[String,Number,null],default:()=>r.props.search.label},adjustPosition:{type:Boolean,default:()=>!0},autoBlur:{type:Boolean,default:()=>!1},iconPosition:{type:String,default:()=>r.props.search.iconPosition}}});exports.props=t;
