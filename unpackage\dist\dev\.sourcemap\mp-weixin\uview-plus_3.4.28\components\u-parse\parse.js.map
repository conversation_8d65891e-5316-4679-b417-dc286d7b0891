{"version": 3, "file": "parse.js", "sources": ["uview-plus_3.4.28/components/u-parse/parse.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:17:33\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/parse.js\n */\nexport default {\n    // parse\n    parse: {\n        copyLink: true,\n        errorImg: '',\n        lazyLoad: false,\n        loadingImg: '',\n        pauseVideo: true,\n        previewImg: true,\n        setTitle: true,\n        showImgMenu: true\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,UAAU;AAAA,IACV,UAAU;AAAA,IACV,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,aAAa;AAAA,EAChB;AACL;;"}