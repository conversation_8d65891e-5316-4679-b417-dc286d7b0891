/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.u-empty.data-v-aef4e019,
.u-empty__wrap.data-v-aef4e019,
.u-tabs.data-v-aef4e019,
.u-tabs__wrapper.data-v-aef4e019,
.u-tabs__wrapper__scroll-view-wrapper.data-v-aef4e019,
.u-tabs__wrapper__scroll-view.data-v-aef4e019,
.u-tabs__wrapper__nav.data-v-aef4e019,
.u-tabs__wrapper__nav__line.data-v-aef4e019,
.up-empty.data-v-aef4e019,
.up-empty__wrap.data-v-aef4e019,
.up-tabs.data-v-aef4e019,
.up-tabs__wrapper.data-v-aef4e019,
.up-tabs__wrapper__scroll-view-wrapper.data-v-aef4e019,
.up-tabs__wrapper__scroll-view.data-v-aef4e019,
.up-tabs__wrapper__nav.data-v-aef4e019,
.up-tabs__wrapper__nav__line.data-v-aef4e019 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-badge.data-v-aef4e019 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  display: flex;
  flex-direction: row;
  line-height: 11px;
  text-align: center;
  font-size: 11px;
  color: #FFFFFF;
}
.u-badge--dot.data-v-aef4e019 {
  height: 8px;
  width: 8px;
}
.u-badge--inverted.data-v-aef4e019 {
  font-size: 13px;
}
.u-badge--not-dot.data-v-aef4e019 {
  padding: 2px 5px;
}
.u-badge--horn.data-v-aef4e019 {
  border-bottom-left-radius: 0;
}
.u-badge--primary.data-v-aef4e019 {
  background-color: #3c9cff;
}
.u-badge--primary--inverted.data-v-aef4e019 {
  color: #3c9cff;
}
.u-badge--error.data-v-aef4e019 {
  background-color: #f56c6c;
}
.u-badge--error--inverted.data-v-aef4e019 {
  color: #f56c6c;
}
.u-badge--success.data-v-aef4e019 {
  background-color: #5ac725;
}
.u-badge--success--inverted.data-v-aef4e019 {
  color: #5ac725;
}
.u-badge--info.data-v-aef4e019 {
  background-color: #909399;
}
.u-badge--info--inverted.data-v-aef4e019 {
  color: #909399;
}
.u-badge--warning.data-v-aef4e019 {
  background-color: #f9ae3d;
}
.u-badge--warning--inverted.data-v-aef4e019 {
  color: #f9ae3d;
}