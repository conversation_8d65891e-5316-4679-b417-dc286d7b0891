{"version": 3, "file": "index.js", "sources": ["pages/party/index.vue"], "sourcesContent": ["<template>\n\t<view class=\"common-container\">\n\t\t<!-- 自定义导航栏 -->\n\t\t<custom-navbar title=\"党建工作\" :showBack=\"true\" :showHome=\"true\" @leftClick=\"goBack\">\n\t\t\t<template #right>\n\t\t\t\t<view class=\"navbar-right\">\n\t\t\t\t\t<u-icon name=\"more-dot-fill\" color=\"#FFFFFF\" size=\"20\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</template>\n\t\t</custom-navbar>\n\n\t\t<!-- 党建工作标题区域 -->\n\t\t<view class=\"party-header\">\n\t\t\t<view class=\"header-title\">党建工作</view>\n\t\t\t<view class=\"header-desc\">坚持党的领导，加强党的建设</view>\n\t\t</view>\n\n\t\t<!-- 党建工作内容 -->\n\t\t<view class=\"party-content\">\n\t\t\t<view class=\"content-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<u-icon name=\"grid-fill\" color=\"#D9001B\" size=\"20\"></u-icon>\n\t\t\t\t\t<text>{{ currentTitle }}</text>\n\t\t\t\t</view>\n\n\n\t\t\t\t<view class=\"section-content\">\n\t\t\t\t\t<u-list :enable-flex=\"true\">\n\t\t\t\t\t\t<u-cell v-for=\"(item, index) in categoryList\" :key=\"index\"\n\t\t\t\t\t\t\t:title=\"item.name\"\n\t\t\t\t\t\t\t:titleStyle=\"{overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap'}\"\n\t\t\t\t\t\t\tisLink\n\t\t\t\t\t\t\t@click=\"onCategoryClick(index)\">\n\t\t\t\t\t\t</u-cell>\n\t\t\t\t\t</u-list>\n\t\t\t\t</view>\n\n\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"party-footer\">\n\t\t\t<u-button type=\"primary\" :text=\"categoryHistory.length > 0 ? '返回上一级' : '返回首页'\" @click=\"goBack\" :customStyle=\"{backgroundColor: '#D9001B'}\"></u-button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { categoryApi } from '@/api/index.js';\n\timport CustomNavbar from '@/components/common/custom-navbar.vue';\n\n\texport default {\n\t\tcomponents: {\n\t\t\tCustomNavbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 基层党组织数据\n\t\t\t\tcategoryList: [],\n\t\t\t\t// 分类历史记录，用于返回上一级\n\t\t\t\tcategoryHistory: [],\n\t\t\t\t// 当前分类层级\n\t\t\t\tcurrentLevel: 1,\n\t\t\t\t// 当前分类标题\n\t\t\t\tcurrentTitle: '基层党组织'\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\t// 页面加载时的逻辑\n\t\t\tconsole.log('党建工作页面加载');\n\t\t\t// 获取分类数据\n\t\t\tthis.fetchCategoryList();\n\t\t},\n\t\tmethods: {\n\t\t\t// 获取分类列表\n\t\t\tasync fetchCategoryList() {\n\t\t\t\ttry {\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '加载中...'\n\t\t\t\t\t});\n\n\t\t\t\t\tconst result = await categoryApi.getCategoryTreeList();\n\t\t\t\t\tconsole.log('获取分类树形列表成功:', result);\n\n\t\t\t\t\tif (result && result.success && Array.isArray(result.data)) {\n\t\t\t\t\t\tthis.categoryList = result.data;\n\t\t\t\t\t\tconsole.log('分类树形列表数据已更新');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取分类树形列表失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取分类数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 分类点击事件\n\t\t\tonCategoryClick(index) {\n\t\t\t\tconst item = this.categoryList[index];\n\t\t\t\tconsole.log('点击了基层党组织:', item);\n\n\t\t\t\t// 判断是否有子分类\n\t\t\t\tif (item.children && item.children.length > 0) {\n\t\t\t\t\t// 保存当前分类列表到历史记录\n\t\t\t\t\tthis.categoryHistory.push({\n\t\t\t\t\t\tlist: [...this.categoryList],\n\t\t\t\t\t\ttitle: this.currentTitle,\n\t\t\t\t\t\tlevel: this.currentLevel\n\t\t\t\t\t});\n\n\t\t\t\t\t// 有子分类，更新当前列表为子分类\n\t\t\t\t\tthis.categoryList = item.children;\n\n\t\t\t\t\t// 更新当前标题和层级\n\t\t\t\t\tthis.currentTitle = item.name;\n\t\t\t\t\tthis.currentLevel++;\n\n\t\t\t\t\t// 更新页面标题\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: item.name\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 没有子分类，跳转到分类文章列表页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/party/category-articles/index?id=${item.id}`,\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 返回上一级分类\n\t\t\tgoBackToParent() {\n\t\t\t\tif (this.categoryHistory.length > 0) {\n\t\t\t\t\t// 从历史记录中获取上一级分类信息\n\t\t\t\t\tconst prevCategory = this.categoryHistory.pop();\n\n\t\t\t\t\t// 恢复上一级分类列表\n\t\t\t\t\tthis.categoryList = prevCategory.list;\n\t\t\t\t\tthis.currentTitle = prevCategory.title;\n\t\t\t\t\tthis.currentLevel = prevCategory.level;\n\n\t\t\t\t\t// 更新页面标题\n\t\t\t\t\tuni.setNavigationBarTitle({\n\t\t\t\t\t\ttitle: this.currentTitle\n\t\t\t\t\t});\n\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t\treturn false;\n\t\t\t},\n\t\t\t// 返回首页或上一级\n\t\t\tgoBack() {\n\t\t\t\t// 先尝试返回上一级分类\n\t\t\t\tif (!this.goBackToParent()) {\n\t\t\t\t\t// 如果没有上一级分类，则返回首页\n\t\t\t\t\tuni.navigateBack({\n\t\t\t\t\t\tdelta: 1\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\t/* 使用全局样式文件中的.common-container */\n\n\t.party-content {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t/* 使用全局样式文件中的.party-footer */\n</style>\n"], "names": ["uni", "categoryApi.getCategoryTreeList"], "mappings": ";;;AAkDC,MAAK,eAAgB,MAAW;AAEhC,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EACD,OAAO;AACN,WAAO;AAAA;AAAA,MAEN,cAAc,CAAE;AAAA;AAAA,MAEhB,iBAAiB,CAAE;AAAA;AAAA,MAEnB,cAAc;AAAA;AAAA,MAEd,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EACD,SAAS;AAERA,kBAAAA,MAAY,MAAA,OAAA,+BAAA,UAAU;AAEtB,SAAK,kBAAiB;AAAA,EACtB;AAAA,EACD,SAAS;AAAA;AAAA,IAER,MAAM,oBAAoB;AACzB,UAAI;AACHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAED,cAAM,SAAS,MAAMC,eAAAA;AACrBD,sBAAA,MAAA,MAAA,OAAA,+BAAY,eAAe,MAAM;AAEjC,YAAI,UAAU,OAAO,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AAC3D,eAAK,eAAe,OAAO;AAC3BA,wBAAAA,MAAY,MAAA,OAAA,+BAAA,aAAa;AAAA,QAC1B;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,+BAAc,eAAe,KAAK;AAClCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,UAAU;AACTA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA,IAED,gBAAgB,OAAO;AACtB,YAAM,OAAO,KAAK,aAAa,KAAK;AACpCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,IAAI;AAG7B,UAAI,KAAK,YAAY,KAAK,SAAS,SAAS,GAAG;AAE9C,aAAK,gBAAgB,KAAK;AAAA,UACzB,MAAM,CAAC,GAAG,KAAK,YAAY;AAAA,UAC3B,OAAO,KAAK;AAAA,UACZ,OAAO,KAAK;AAAA,QACb,CAAC;AAGD,aAAK,eAAe,KAAK;AAGzB,aAAK,eAAe,KAAK;AACzB,aAAK;AAGLA,sBAAAA,MAAI,sBAAsB;AAAA,UACzB,OAAO,KAAK;AAAA,QACb,CAAC;AAAA,aACK;AAENA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK,2CAA2C,KAAK,EAAE;AAAA,UACvD,MAAM,CAAC,QAAQ;AACdA,0BAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAC1BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB;AAChB,UAAI,KAAK,gBAAgB,SAAS,GAAG;AAEpC,cAAM,eAAe,KAAK,gBAAgB,IAAG;AAG7C,aAAK,eAAe,aAAa;AACjC,aAAK,eAAe,aAAa;AACjC,aAAK,eAAe,aAAa;AAGjCA,sBAAAA,MAAI,sBAAsB;AAAA,UACzB,OAAO,KAAK;AAAA,QACb,CAAC;AAED,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAED,SAAS;AAER,UAAI,CAAC,KAAK,kBAAkB;AAE3BA,sBAAAA,MAAI,aAAa;AAAA,UAChB,OAAO;AAAA,QACR,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}