"use strict";const e=require("./props.js"),t=require("../../libs/mixin/mpMixin.js"),i=require("../../libs/mixin/mixin.js"),s=require("../../libs/config/props.js"),r=require("../../libs/function/index.js"),n=require("../../../common/vendor.js"),a={name:"u-tabs",mixins:[t.mpMixin,i.mixin,e.props],data:()=>({firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}),watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(this.innerCurrent="string"==typeof e?parseInt(e):e,this.$nextTick((()=>{this.resize()})))}},list(){this.$nextTick((()=>{this.resize()}))}},computed:{textStyle(){return e=>{const t={},i=e==this.innerCurrent?r.addStyle(this.activeStyle):r.addStyle(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),r.deepMerge(i,t)}},propsBadge:()=>s.props.badge},async mounted(){this.init()},emits:["click","longPress","change","update:current"],methods:{addStyle:r.addStyle,addUnit:r.addUnit,setLineLeft(){const e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0);const i=r.getPx(this.lineWidth);this.lineOffsetLeft=t+(e.rect.width-i)/2,this.firstTime&&setTimeout((()=>{this.firstTime=!1}),10)},animation(e,t=0){},clickHandler(e,t){this.$emit("click",{...e,index:t},t),e.disabled||this.innerCurrent!=t&&(this.innerCurrent=t,this.resize(),this.$emit("update:current",t),this.$emit("change",{...e,index:t},t))},longPressHandler(e,t){this.$emit("longPress",{...e,index:t})},init(){r.sleep().then((()=>{this.resize()}))},setScrollLeft(){this.innerCurrent<0&&(this.innerCurrent=0);const e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce(((e,t)=>e+t.rect.width),0),i=r.getWindowInfo().windowWidth;let s=t-(this.tabsRect.width-e.rect.width)/2-(i-this.tabsRect.right)/2+this.tabsRect.left/2;s=Math.min(s,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,s)},resize(){0!==this.list.length&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then((([e,t=[]])=>{e.left>e.width&&(e.right=e.right-Math.floor(e.left/e.width)*e.width,e.left=e.left%e.width),this.tabsRect=e,this.scrollViewWidth=0,t.map(((e,t)=>{this.scrollViewWidth+=e.width,this.list[t].rect=e})),this.setLineLeft(),this.setScrollLeft()}))},getTabsRect(){return new Promise((e=>{this.queryRect("u-tabs__wrapper__scroll-view").then((t=>e(t)))}))},getAllItemRect(){return new Promise((e=>{const t=this.list.map(((e,t)=>this.queryRect(`u-tabs__wrapper__nav__item-${t}`,!0)));Promise.all(t).then((t=>e(t)))}))},queryRect(e,t){return new Promise((t=>{this.$uGetRect(`.${e}`).then((e=>{t(e)}))}))}}};if(!Array){(n.resolveComponent("up-icon")+n.resolveComponent("u-badge"))()}Math;const d=n._export_sfc(a,[["render",function(e,t,i,s,r,a){return{a:n.f(e.list,((t,i,s)=>n.e(e.$slots.icon?{a:"icon-"+s,b:n.r("icon",{item:t,keyName:e.keyName,index:i},s)}:n.e({c:t.icon},t.icon?{d:"faccd8d1-0-"+s,e:n.p({name:t.icon,customStyle:a.addStyle(e.iconStyle)})}:{}),e.$slots.content?{f:"content-"+s,g:n.r("content",{item:t,keyName:e.keyName,index:i},s)}:e.$slots.content||!e.$slots.default&&!e.$slots.$default?{j:n.t(t[e.keyName]),k:n.n(t.disabled&&"u-tabs__wrapper__nav__item__text--disabled"),l:n.s(a.textStyle(i))}:{h:"d-"+s,i:n.r("d",{item:t,keyName:e.keyName,index:i},s)},{m:"faccd8d1-1-"+s,n:n.p({show:!(!t.badge||!(t.badge.show||t.badge.isDot||t.badge.value)),isDot:t.badge&&t.badge.isDot||a.propsBadge.isDot,value:t.badge&&t.badge.value||a.propsBadge.value,max:t.badge&&t.badge.max||a.propsBadge.max,type:t.badge&&t.badge.type||a.propsBadge.type,showZero:t.badge&&t.badge.showZero||a.propsBadge.showZero,bgColor:t.badge&&t.badge.bgColor||a.propsBadge.bgColor,color:t.badge&&t.badge.color||a.propsBadge.color,shape:t.badge&&t.badge.shape||a.propsBadge.shape,numberType:t.badge&&t.badge.numberType||a.propsBadge.numberType,inverted:t.badge&&t.badge.inverted||a.propsBadge.inverted,customStyle:"margin-left: 4px;"}),o:i,p:n.o((e=>a.clickHandler(t,i)),i),q:n.o((e=>a.longPressHandler(t,i)),i),r:`u-tabs__wrapper__nav__item-${i}`,s:n.n(`u-tabs__wrapper__nav__item-${i}`),t:n.n(t.disabled&&"u-tabs__wrapper__nav__item--disabled"),v:n.n(r.innerCurrent==i?"u-tabs__wrapper__nav__item-active":"")}))),b:e.$slots.icon,c:e.$slots.content,d:!e.$slots.content&&(e.$slots.default||e.$slots.$default),e:n.s(a.addStyle(e.itemStyle)),f:n.s({flex:e.scrollable?"":1}),g:n.s({width:a.addUnit(e.lineWidth),transform:`translate(${r.lineOffsetLeft}px)`,transitionDuration:`${r.firstTime?0:e.duration}ms`,height:a.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}),h:e.scrollable,i:r.scrollLeft,j:n.n(e.customClass)}}],["__scopeId","data-v-faccd8d1"]]);wx.createComponent(d);
