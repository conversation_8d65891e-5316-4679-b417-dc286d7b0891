/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入 uview-plus 主题文件 */
.u-empty.data-v-fc98a578,
.u-empty__wrap.data-v-fc98a578,
.u-tabs.data-v-fc98a578,
.u-tabs__wrapper.data-v-fc98a578,
.u-tabs__wrapper__scroll-view-wrapper.data-v-fc98a578,
.u-tabs__wrapper__scroll-view.data-v-fc98a578,
.u-tabs__wrapper__nav.data-v-fc98a578,
.u-tabs__wrapper__nav__line.data-v-fc98a578,
.up-empty.data-v-fc98a578,
.up-empty__wrap.data-v-fc98a578,
.up-tabs.data-v-fc98a578,
.up-tabs__wrapper.data-v-fc98a578,
.up-tabs__wrapper__scroll-view-wrapper.data-v-fc98a578,
.up-tabs__wrapper__scroll-view.data-v-fc98a578,
.up-tabs__wrapper__nav.data-v-fc98a578,
.up-tabs__wrapper__nav__line.data-v-fc98a578 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-text.data-v-fc98a578 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
}
.u-text__price.data-v-fc98a578 {
  font-size: 14px;
  color: #606266;
}
.u-text__value.data-v-fc98a578 {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  color: #606266;
  flex-wrap: wrap;
  text-overflow: ellipsis;
  align-items: center;
}
.u-text__value--primary.data-v-fc98a578 {
  color: #3c9cff;
}
.u-text__value--warning.data-v-fc98a578 {
  color: #f9ae3d;
}
.u-text__value--success.data-v-fc98a578 {
  color: #5ac725;
}
.u-text__value--info.data-v-fc98a578 {
  color: #909399;
}
.u-text__value--error.data-v-fc98a578 {
  color: #f56c6c;
}
.u-text__value--main.data-v-fc98a578 {
  color: #303133;
}
.u-text__value--content.data-v-fc98a578 {
  color: #606266;
}
.u-text__value--tips.data-v-fc98a578 {
  color: #909193;
}
.u-text__value--light.data-v-fc98a578 {
  color: #c0c4cc;
}