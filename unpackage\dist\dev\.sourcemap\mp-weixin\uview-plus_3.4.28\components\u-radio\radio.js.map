{"version": 3, "file": "radio.js", "sources": ["uview-plus_3.4.28/components/u-radio/radio.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:02:34\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/radio.js\n */\nexport default {\n    // radio组件\n    radio: {\n        name: '',\n        shape: '',\n        disabled: '',\n        labelDisabled: '',\n        activeColor: '',\n        inactiveColor: '',\n        iconSize: '',\n        labelSize: '',\n        label: '',\n        labelColor: '',\n        size: '',\n        iconColor: '',\n        placement: ''\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,eAAe;AAAA,IACf,UAAU;AAAA,IACV,WAAW;AAAA,IACX,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,EACd;AACL;;"}