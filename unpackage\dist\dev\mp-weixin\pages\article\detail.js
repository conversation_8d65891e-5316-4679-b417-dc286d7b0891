"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_image = require("../../utils/image.js");
const pages_article_map = require("./map.js");
const CustomNavbar = () => "../../components/common/custom-navbar.js";
const ArticleDetail = () => "../../components/article/article-detail.js";
const _sfc_main = {
  components: {
    CustomNavbar,
    ArticleDetail
  },
  data() {
    return {
      articleId: null,
      articleType: "news",
      // 默认为新闻文章，可选值：news, menu, party
      article: null,
      loading: true,
      pageTitle: "文章详情"
      // 默认页面标题
    };
  },
  computed: {
    // 当前文章类型对应的API函数
    apiFunction() {
      return pages_article_map.getArticleApi(this.articleType);
    }
  },
  onLoad(options) {
    if (options) {
      if (options.id) {
        this.articleId = options.id;
      } else {
        this.showError("文章ID不存在");
        return;
      }
      if (options.type) {
        this.articleType = options.type;
      }
      this.fetchArticleDetail();
    } else {
      this.showError("参数不存在");
    }
  },
  methods: {
    // 显示错误并返回
    showError(message) {
      common_vendor.index.showToast({
        title: message,
        icon: "none"
      });
      setTimeout(() => {
        common_vendor.index.navigateBack();
      }, 1500);
    },
    // 获取文章详情
    async fetchArticleDetail() {
      try {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        let result;
        result = await this.apiFunction(this.articleId);
        common_vendor.index.__f__("log", "at pages/article/detail.vue:100", result);
        if (result && result.success && result.data) {
          this.article = result.data;
          this.pageTitle = this.article.title || "文章详情";
          if (this.articleType === "news" && !this.article.content_markdown) {
            this.article.content_markdown = `这是一篇关于"${this.article.title}"的文章内容。

由于API暂未提供文章内容，这里展示的是默认内容。`;
          } else if (!this.article.content) {
            this.article.content = `这是一篇关于"${this.article.title}"的文章内容。

由于API暂未提供完整内容，这里展示的是默认内容。`;
          }
          if (this.article.content && this.article.content.includes("[pic:")) {
            await this.processArticleImages(this.article);
          }
          if (this.article.coverImageId) {
            await this.fetchCoverImage(this.article.coverImageId);
          }
        } else {
          this.showError("获取文章失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/article/detail.vue:130", "获取文章详情失败:", error);
        this.showError("获取文章失败");
      } finally {
        common_vendor.index.hideLoading();
        this.loading = false;
      }
    },
    // 处理文章内容中的图片
    async processArticleImages(article) {
      if (!article.content)
        return;
      const picRegex = /\[pic:(\d+)\]/g;
      let match;
      let processedContent = article.content;
      const imagePromises = [];
      const imageMatches = [];
      while ((match = picRegex.exec(article.content)) !== null) {
        const imageId = parseInt(match[1]);
        imageMatches.push({
          fullMatch: match[0],
          imageId
        });
        imagePromises.push(utils_image.fetchImageById(imageId));
      }
      if (imagePromises.length > 0) {
        const imageResults = await Promise.all(imagePromises);
        for (let i = 0; i < imageMatches.length; i++) {
          const imageData = imageResults[i];
          const { fullMatch } = imageMatches[i];
          if (imageData && imageData.url) {
            const imageUrl = utils_image.getFullImageUrl(imageData.url);
            processedContent = processedContent.replace(
              fullMatch,
              `<img src="${imageUrl}" alt="${imageData.altText || "文章图片"}" style="max-width: 100%;">`
            );
          } else {
            processedContent = processedContent.replace(fullMatch, "");
          }
        }
        article.content = processedContent;
      }
      if (article.coverImageId && !article.coverImage) {
        try {
          const imageData = await utils_image.fetchImageById(article.coverImageId);
          if (imageData && imageData.url) {
            article.coverImage = utils_image.getFullImageUrl(imageData.url);
          }
        } catch (err) {
          common_vendor.index.__f__("error", "at pages/article/detail.vue:197", "获取文章封面图失败:", err);
        }
      }
    },
    // 获取封面图片
    async fetchCoverImage(imageId) {
      try {
        const imageData = await utils_image.fetchImageById(imageId);
        common_vendor.index.__f__("log", "at pages/article/detail.vue:206", "获取封面图片成功:", imageData);
        if (imageData && imageData.url) {
          if (this.articleType === "normal" || this.articleType === "party") {
            this.article.coverImage = utils_image.getFullImageUrl(imageData.url);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/article/detail.vue:216", "获取封面图片失败:", error);
      }
    }
  }
};
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _component_custom_navbar = common_vendor.resolveComponent("custom-navbar");
  const _component_article_detail = common_vendor.resolveComponent("article-detail");
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  (_easycom_u_icon2 + _component_custom_navbar + _component_article_detail + _easycom_u_loading_icon2)();
}
const _easycom_u_icon = () => "../../components/u-icon/u-icon.js";
const _easycom_u_loading_icon = () => "../../uview-plus_3.4.28/components/u-loading-icon/u-loading-icon.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_loading_icon)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      name: "more-dot-fill",
      color: "#FFFFFF",
      size: "20"
    }),
    b: common_vendor.p({
      title: $data.pageTitle,
      showBack: true,
      showHome: true
    }),
    c: $data.article
  }, $data.article ? {
    d: common_vendor.p({
      article: $data.article,
      articleType: $data.articleType,
      showFooter: true
    })
  } : {
    e: common_vendor.p({
      mode: "circle",
      size: "28"
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/article/detail.js.map
