<template>
	<view class="u-page">
		<view class="u-demo-block">
			<text class="u-demo-block__title">基础使用</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					v-model="value"
					@change="change"
					confirmType="search"
					@confirm="handleSearch"
				></up-input>
				<up-button @click="value = Math.random().toString()"
					style="margin-top: 10px;">变化</up-button>
				{{value}}
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">颜色</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					color="blue"
					v-model="value"
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">可清空内容</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					clearable
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">数字键盘</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					type="number"
					v-model="inputNumber"
					clearable
				></up-input>
				{{ inputNumber }}
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">密码类型</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					password
					clearable
					v-model="inputPassword"
				></up-input>
				{{inputPassword}}
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">显示下划线</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="bottom"
					clearable
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">禁用状态</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="禁用状态"
					border="surround"
					disabled
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">圆形</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="请输入内容"
					border="surround"
					shape="circle"
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">前后图标</text>
			<view class="u-demo-block__content">
				<up-input
					placeholder="前置图标"
					prefixIcon="search"
					prefixIconStyle="font-size: 22px;color: #909399"
				></up-input>
			</view>
			<view
				class="u-demo-block__content"
				style="margin-top: 15px;"
			>
				<up-input
					placeholder="后置图标"
					suffixIcon="map-fill"
					suffixIconStyle="color: #909399"
				></up-input>
			</view>
		</view>
		<view class="u-demo-block">
			<text class="u-demo-block__title">前后插槽</text>
			<view class="u-demo-block__content">
				<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
				<!-- #ifndef APP-NVUE -->
				<up-input placeholder="前置插槽">
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					<up-input placeholder="前置插槽">
						<!-- #endif -->
						<template #prefix>
							<up-text
								text="http://"
								margin="0 3px 0 0"
								type="tips"
							></up-text>
						</template>
						<!-- #ifndef APP-NVUE -->
				</up-input>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				</up-input>
				<!-- #endif -->
			</view>
			<view
				class="u-demo-block__content"
				style="margin-top: 15px;"
			>
				<!-- 注意：由于兼容性差异，如果需要使用前后插槽，nvue下需使用u--input，非nvue下需使用u-input -->
				<!-- #ifndef APP-NVUE -->
				<up-input placeholder="后置插槽">
					<!-- #endif -->
					<!-- #ifdef APP-NVUE -->
					<up-input placeholder="后置插槽">
						<!-- #endif -->
						<template #suffix>
							<up-code
								ref="uCode"
								@change="codeChange"
								seconds="20"
								changeText="X秒重新获取哈哈哈"
							></up-code>
							<up-button
								@tap="getCode"
								:text="tips"
								type="success"
								size="mini"
							></up-button>
						</template>
						<!-- #ifndef APP-NVUE -->
				</up-input>
				<!-- #endif -->
				<!-- #ifdef APP-NVUE -->
				</up-input>
				<!-- #endif -->
			</view>
		</view>
		<up-gap
			bgColor="#fff"
			height="50"
		></up-gap>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tips: '',
				value: '',
				inputNumber: '',
				inputPassword: '123456'
			}
		},
		watch: {
			value(newValue, oldValue) {
				// console.log('v-model', newValue);
			},
			inputNumber(newValue) {
				// console.log('v-model', newValue);
			}
		},
		methods: {
			codeChange(text) {
				this.tips = text;
			},
			getCode() {
				if (this.$refs.uCode.canGetCode) {
					// 模拟向后端请求验证码
					uni.showLoading({
						title: '正在获取验证码'
					})
					setTimeout(() => {
						uni.hideLoading();
						// 这里此提示会被this.start()方法中的提示覆盖
						uni.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					}, 2000);
				} else {
					uni.$u.toast('倒计时结束后再发送');
				}
			},
			change(e) {
				console.log('change', e);
			},
			handleSearch(e) {
				uni.$u.toast('@confirm触发');
				console.log('change', e);
			}
		}
	}
</script>

<style lang="scss">

</style>
